import * as jsQRModule from 'jsqr';
const jsQR = (jsQRModule as any).default || jsQRModule;
import { Jim<PERSON> } from 'jimp';
import { logger } from '../utils/logger.js';
import { QRCodeResult } from '../types/index.js';

export class QRDecoder {
  async decodeFromBuffer(buffer: Buffer): Promise<QRCodeResult | null> {
    try {
      logger.debug('Starting QR decode from buffer');
      
      const image = await Jimp.read(buffer);
      const { width, height } = image.bitmap;
      
      // Convert to RGBA format for jsQR
      const imageData = new Uint8ClampedArray(image.bitmap.data);
      
      logger.debug({ width, height }, 'Image loaded, attempting QR decode');
      
      const code = jsQR(imageData, width, height, {
        inversionAttempts: 'attemptBoth'
      });
      
      if (!code) {
        logger.debug('No QR code found in image');
        return null;
      }
      
      logger.info({ 
        data: code.data.substring(0, 100) + (code.data.length > 100 ? '...' : ''),
        version: code.version 
      }, 'QR code successfully decoded');
      
      return {
        data: code.data,
        location: code.location,
        version: code.version
      };
    } catch (error) {
      logger.error({ error }, 'Error decoding QR code from buffer');
      throw error;
    }
  }

  async decodeFromBase64(base64: string): Promise<QRCodeResult | null> {
    try {
      const buffer = Buffer.from(base64, 'base64');
      return this.decodeFromBuffer(buffer);
    } catch (error) {
      logger.error({ error }, 'Error decoding QR code from base64');
      throw error;
    }
  }

  async decodeFromPath(path: string): Promise<QRCodeResult | null> {
    try {
      const image = await Jimp.read(path);
      const buffer = await image.getBuffer('image/png');
      return this.decodeFromBuffer(buffer);
    } catch (error) {
      logger.error({ error, path }, 'Error decoding QR code from file path');
      throw error;
    }
  }

  async decodeFromDataUri(dataUri: string): Promise<QRCodeResult | null> {
    try {
      if (!dataUri.startsWith('data:')) {
        throw new Error('Invalid data URI format');
      }

      const base64 = dataUri.split(',')[1];
      if (!base64) {
        throw new Error('No base64 data found in data URI');
      }

      return this.decodeFromBase64(base64);
    } catch (error) {
      logger.error({ error }, 'Error decoding QR code from data URI');
      throw error;
    }
  }

  // Advanced decoding with multiple attempts and preprocessing
  async decodeWithOptions(
    buffer: Buffer, 
    options: {
      inversionAttempts?: 'dontInvert' | 'onlyInvert' | 'attemptBoth';
      greyScaleWeights?: { red?: number; green?: number; blue?: number };
    } = {}
  ): Promise<QRCodeResult | null> {
    try {
      const image = await Jimp.read(buffer);
      const { width, height } = image.bitmap;
      
      // Apply grayscale weights if provided
      if (options.greyScaleWeights) {
        const { red = 0.299, green = 0.587, blue = 0.114 } = options.greyScaleWeights;
        
        // Custom grayscale conversion
        image.scan(0, 0, width, height, (x, y, idx) => {
          const r = image.bitmap.data[idx + 0] || 0;
          const g = image.bitmap.data[idx + 1] || 0;
          const b = image.bitmap.data[idx + 2] || 0;

          const gray = Math.round(r * red + g * green + b * blue);

          image.bitmap.data[idx + 0] = gray;
          image.bitmap.data[idx + 1] = gray;
          image.bitmap.data[idx + 2] = gray;
        });
      }
      
      const imageData = new Uint8ClampedArray(image.bitmap.data);
      
      const code = jsQR(imageData, width, height, {
        inversionAttempts: options.inversionAttempts || 'attemptBoth'
      });
      
      if (!code) {
        logger.debug('No QR code found with advanced options');
        return null;
      }
      
      logger.info({ 
        data: code.data.substring(0, 100) + (code.data.length > 100 ? '...' : ''),
        version: code.version,
        options 
      }, 'QR code successfully decoded with options');
      
      return {
        data: code.data,
        location: code.location,
        version: code.version
      };
    } catch (error) {
      logger.error({ error, options }, 'Error decoding QR code with options');
      throw error;
    }
  }
}
