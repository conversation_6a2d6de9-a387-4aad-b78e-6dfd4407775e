import rateLimit from 'express-rate-limit';
import { config } from '../utils/config.js';
import { logger } from '../utils/logger.js';

export const rateLimiter = rateLimit({
  windowMs: config.RATE_LIMIT_WINDOW_MS,
  max: config.RATE_LIMIT_MAX_REQUESTS,
  message: {
    success: false,
    error: {
      message: 'Too many requests from this IP, please try again later',
      retryAfter: Math.ceil(config.RATE_LIMIT_WINDOW_MS / 1000)
    },
    timestamp: new Date().toISOString()
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    logger.warn({
      ip: req.ip,
      method: req.method,
      url: req.url,
      headers: req.headers
    }, 'Rate limit exceeded');

    res.status(429).json({
      success: false,
      error: {
        message: 'Too many requests from this IP, please try again later',
        retryAfter: Math.ceil(config.RATE_LIMIT_WINDOW_MS / 1000)
      },
      timestamp: new Date().toISOString()
    });
  }
});

// Stricter rate limiting for QR processing endpoints
export const qrProcessingRateLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 10, // 10 requests per minute
  message: {
    success: false,
    error: {
      message: 'Too many QR processing requests, please try again later',
      retryAfter: 60
    },
    timestamp: new Date().toISOString()
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    logger.warn({
      ip: req.ip,
      method: req.method,
      url: req.url
    }, 'QR processing rate limit exceeded');

    res.status(429).json({
      success: false,
      error: {
        message: 'Too many QR processing requests, please try again later',
        retryAfter: 60
      },
      timestamp: new Date().toISOString()
    });
  }
});
