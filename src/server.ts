import { createApp } from './app.js';
import { QRMCPServer } from './mcp/server.js';
import { config } from './utils/config.js';
import { logger } from './utils/logger.js';

class Server {
  private app: ReturnType<typeof createApp>;
  private mcpServer: QRMCPServer;
  private httpServer: any;

  constructor() {
    this.app = createApp();
    this.mcpServer = new QRMCPServer();
  }

  async start() {
    try {
      logger.info('Starting QRMCPNode server...');

      // Initialize MCP server
      await this.initializeMCPServer();

      // Start HTTP server
      await this.startHTTPServer();

      // Setup graceful shutdown
      this.setupGracefulShutdown();

      logger.info({
        port: config.PORT,
        host: config.HOST,
        nodeEnv: config.NODE_ENV
      }, 'QRMCPNode server started successfully');

    } catch (error) {
      logger.error({ error }, 'Failed to start server');
      process.exit(1);
    }
  }

  private async initializeMCPServer() {
    try {
      logger.info('Initializing MCP server...');

      // Connect the MCP server
      await this.mcpServer.connect();

      // Integrate MCP transport with Express
      this.integrateMCPWithExpress();

      logger.info('MCP server initialized successfully');
    } catch (error) {
      logger.error({ error }, 'Failed to initialize MCP server');
      throw error;
    }
  }

  private integrateMCPWithExpress() {
    const transport = this.mcpServer.getTransport();

    // Handle MCP requests through the transport
    this.app.use('/mcp', (req, res, next) => {
      try {
        // For now, just acknowledge the MCP endpoint exists
        // The actual transport integration will be handled differently
        res.json({
          message: 'MCP endpoint active',
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        logger.error({ error }, 'MCP transport error');
        next(error);
      }
    });

    logger.debug('MCP transport integrated with Express');
  }

  private async startHTTPServer() {
    return new Promise<void>((resolve, reject) => {
      try {
        this.httpServer = this.app.listen(config.PORT, config.HOST, () => {
          logger.info({
            port: config.PORT,
            host: config.HOST
          }, 'HTTP server listening');
          resolve();
        });

        this.httpServer.on('error', (error: Error) => {
          logger.error({ error }, 'HTTP server error');
          reject(error);
        });

      } catch (error) {
        logger.error({ error }, 'Failed to start HTTP server');
        reject(error);
      }
    });
  }

  private setupGracefulShutdown() {
    const shutdown = async (signal: string) => {
      logger.info({ signal }, 'Received shutdown signal, starting graceful shutdown...');

      try {
        // Close HTTP server
        if (this.httpServer) {
          await new Promise<void>((resolve) => {
            this.httpServer.close(() => {
              logger.info('HTTP server closed');
              resolve();
            });
          });
        }

        // Close MCP server
        if (this.mcpServer) {
          await this.mcpServer.close();
          logger.info('MCP server closed');
        }

        logger.info('Graceful shutdown completed');
        process.exit(0);
      } catch (error) {
        logger.error({ error }, 'Error during graceful shutdown');
        process.exit(1);
      }
    };

    // Handle shutdown signals
    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error({ error }, 'Uncaught exception');
      process.exit(1);
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      logger.error({ reason, promise }, 'Unhandled promise rejection');
      process.exit(1);
    });
  }

  async stop() {
    try {
      logger.info('Stopping server...');

      if (this.httpServer) {
        await new Promise<void>((resolve) => {
          this.httpServer.close(() => {
            logger.info('HTTP server stopped');
            resolve();
          });
        });
      }

      if (this.mcpServer) {
        await this.mcpServer.close();
        logger.info('MCP server stopped');
      }

      logger.info('Server stopped successfully');
    } catch (error) {
      logger.error({ error }, 'Error stopping server');
      throw error;
    }
  }

  getApp() {
    return this.app;
  }

  getMCPServer() {
    return this.mcpServer;
  }
}

// Start the server if this file is run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const server = new Server();
  server.start().catch((error) => {
    logger.error({ error }, 'Failed to start server');
    process.exit(1);
  });
}

export { Server };
export default Server;
