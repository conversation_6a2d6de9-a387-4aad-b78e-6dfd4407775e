import { Router, Request, Response } from 'express';
import { logger } from '../utils/logger.js';
import { HealthStatus } from '../types/index.js';
import { QRMCPServer } from '../mcp/server.js';

const router = Router();

// Store server start time for uptime calculation
const startTime = Date.now();

// Health check endpoint
router.get('/', (req: Request, res: Response) => {
  try {
    const uptime = Date.now() - startTime;
    
    const healthStatus: HealthStatus = {
      status: 'healthy',
      version: '1.0.0',
      uptime,
      timestamp: new Date().toISOString(),
      services: {
        mcp: true, // Will be updated when MCP server is available
        qr_processor: true,
        stream_processor: true
      }
    };

    logger.debug({ healthStatus }, 'Health check performed');
    
    res.status(200).json(healthStatus);
  } catch (error) {
    logger.error({ error }, 'Health check failed');
    
    const healthStatus: HealthStatus = {
      status: 'unhealthy',
      version: '1.0.0',
      uptime: Date.now() - startTime,
      timestamp: new Date().toISOString(),
      services: {
        mcp: false,
        qr_processor: false,
        stream_processor: false
      }
    };
    
    res.status(503).json(healthStatus);
  }
});

// Detailed health check endpoint
router.get('/detailed', (req: Request, res: Response) => {
  try {
    const uptime = Date.now() - startTime;
    const memoryUsage = process.memoryUsage();
    
    const detailedHealth = {
      status: 'healthy',
      version: '1.0.0',
      uptime,
      timestamp: new Date().toISOString(),
      services: {
        mcp: true,
        qr_processor: true,
        stream_processor: true
      },
      system: {
        memory: {
          rss: Math.round(memoryUsage.rss / 1024 / 1024) + ' MB',
          heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024) + ' MB',
          heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024) + ' MB',
          external: Math.round(memoryUsage.external / 1024 / 1024) + ' MB'
        },
        process: {
          pid: process.pid,
          nodeVersion: process.version,
          platform: process.platform,
          arch: process.arch
        }
      }
    };

    logger.debug({ detailedHealth }, 'Detailed health check performed');
    
    res.status(200).json(detailedHealth);
  } catch (error) {
    logger.error({ error }, 'Detailed health check failed');
    
    res.status(503).json({
      status: 'unhealthy',
      version: '1.0.0',
      uptime: Date.now() - startTime,
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Readiness probe endpoint
router.get('/ready', (req: Request, res: Response) => {
  try {
    // Check if all required services are ready
    const isReady = true; // Will be updated with actual service checks
    
    if (isReady) {
      res.status(200).json({
        ready: true,
        timestamp: new Date().toISOString()
      });
    } else {
      res.status(503).json({
        ready: false,
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    logger.error({ error }, 'Readiness check failed');
    
    res.status(503).json({
      ready: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

// Liveness probe endpoint
router.get('/live', (req: Request, res: Response) => {
  try {
    res.status(200).json({
      alive: true,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error({ error }, 'Liveness check failed');
    
    res.status(503).json({
      alive: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

export { router as healthRoutes };
