import { Router, Request, Response } from 'express';
import { logger } from '../utils/logger.js';
import { QRService } from '../services/qr.service.js';
import { qrProcessingRateLimiter } from '../middleware/rate-limit.middleware.js';

const router = Router();
const qrService = new QRService();

// API info endpoint
router.get('/', (req: Request, res: Response) => {
  try {
    const apiInfo = {
      name: 'QRMCPNode API',
      version: '1.0.0',
      description: 'REST API for QR code processing',
      endpoints: [
        {
          path: '/api/qr/scan',
          method: 'POST',
          description: 'Scan QR code from image'
        },
        {
          path: '/api/qr/validate',
          method: 'POST',
          description: 'Validate image format'
        }
      ],
      timestamp: new Date().toISOString()
    };

    res.status(200).json(apiInfo);
  } catch (error) {
    logger.error({ error }, 'API info route error');
    
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

// QR code scanning endpoint
router.post('/qr/scan', qrProcessingRateLimiter, async (req: Request, res: Response) => {
  try {
    const { input, format = 'base64', options } = req.body;

    if (!input) {
      return res.status(400).json({
        success: false,
        error: 'Input is required',
        timestamp: new Date().toISOString()
      });
    }

    logger.info({ format, hasOptions: !!options }, 'API QR scan request');

    const result = await qrService.scanQRCode(input, format, options);
    
    res.status(200).json(result);
  } catch (error) {
    logger.error({ error }, 'API QR scan error');
    
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

// Image validation endpoint
router.post('/qr/validate', async (req: Request, res: Response) => {
  try {
    const { input, format = 'base64' } = req.body;

    if (!input) {
      return res.status(400).json({
        success: false,
        error: 'Input is required',
        timestamp: new Date().toISOString()
      });
    }

    let buffer: Buffer;
    
    try {
      switch (format) {
        case 'base64':
          buffer = Buffer.from(input, 'base64');
          break;
        case 'datauri':
          const base64Data = input.split(',')[1];
          if (!base64Data) {
            throw new Error('Invalid data URI format');
          }
          buffer = Buffer.from(base64Data, 'base64');
          break;
        default:
          throw new Error(`Unsupported format: ${format}`);
      }

      const isValid = await qrService.validateImageFormat(buffer);
      
      res.status(200).json({
        success: true,
        valid: isValid,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        valid: false,
        error: error instanceof Error ? error.message : 'Invalid image format',
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    logger.error({ error }, 'API image validation error');
    
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

export { router as apiRoutes };
