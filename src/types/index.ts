export interface QRCodeResult {
  data: string;
  location: {
    topLeft: { x: number; y: number };
    topRight: { x: number; y: number };
    bottomLeft: { x: number; y: number };
    bottomRight: { x: number; y: number };
  };
  version: number;
}

export interface ProcessingOptions {
  resize?: { width: number; height: number };
  grayscale?: boolean;
  contrast?: number;
  brightness?: number;
  normalize?: boolean;
  inversionAttempts?: 'dontInvert' | 'onlyInvert' | 'attemptBoth' | undefined;
  greyScaleWeights?: {
    red?: number | undefined;
    green?: number | undefined;
    blue?: number | undefined;
  } | undefined;
}

export interface StreamOptions {
  interval: number;
  maxDuration?: number;
  format: 'json' | 'text';
}

export interface QRScanRequest {
  input: string;
  format?: 'base64' | 'filepath' | 'datauri';
  options?: ProcessingOptions;
}

export interface QRScanResponse {
  success: boolean;
  data?: string;
  location?: QRCodeResult['location'];
  version?: number;
  error?: string;
  timestamp: string;
}

export interface StreamEvent {
  type: 'qr-detected' | 'no-qr' | 'error' | 'connected' | 'heartbeat';
  timestamp: string;
  data?: QRCodeResult;
  error?: string;
  streamId?: string;
}

export interface HealthStatus {
  status: 'healthy' | 'unhealthy';
  version: string;
  uptime: number;
  timestamp: string;
  services: {
    mcp: boolean;
    qr_processor: boolean;
    stream_processor: boolean;
  };
}
