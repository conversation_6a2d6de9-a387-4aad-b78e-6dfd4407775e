import { z } from 'zod';
import { QRService } from '../../services/qr.service.js';
import { logger } from '../../utils/logger.js';
import { ScanQRInputSchema, MCPToolResponse } from '../../types/mcp.types.js';

export function createScanQRTool() {
  const qrService = new QRService();

  return {
    name: 'scan-qr',
    description: 'Scan and decode QR codes from images provided as base64, file path, or data URI',
    inputSchema: {
      input: z.string().describe('Base64, file path, or data URI of image'),
      format: z.enum(['base64', 'filepath', 'datauri']).optional().default('base64'),
      options: z.object({
        inversionAttempts: z.enum(['dontInvert', 'onlyInvert', 'attemptBoth']).optional(),
        greyScaleWeights: z.object({
          red: z.number().optional(),
          green: z.number().optional(),
          blue: z.number().optional()
        }).optional()
      }).optional()
    },
    handler: async (args: any): Promise<any> => {
      const startTime = Date.now();
      
      try {
        logger.info({ 
          format: args.format,
          hasOptions: !!args.options,
          inputLength: args.input.length 
        }, 'Processing QR scan request');
        
        // Validate input
        if (!args.input || args.input.trim().length === 0) {
          return {
            content: [{
              type: 'text',
              text: JSON.stringify({
                success: false,
                error: 'Input is required and cannot be empty',
                timestamp: new Date().toISOString()
              }, null, 2)
            }],
            isError: true
          };
        }

        // Perform QR code scanning
        const result = await qrService.scanQRCode(
          args.input,
          args.format || 'base64',
          args.options
        );
        
        const processingTime = Date.now() - startTime;
        
        if (result.success) {
          logger.info({ 
            processingTime,
            dataLength: result.data?.length,
            version: result.version 
          }, 'QR code scan successful');
          
          return {
            content: [{
              type: 'text',
              text: JSON.stringify({
                success: true,
                data: result.data,
                location: result.location,
                version: result.version,
                timestamp: result.timestamp,
                processingTime
              }, null, 2)
            }]
          };
        } else {
          logger.info({ processingTime, error: result.error }, 'QR code scan failed');
          
          return {
            content: [{
              type: 'text',
              text: JSON.stringify({
                success: false,
                error: result.error,
                timestamp: result.timestamp,
                processingTime
              }, null, 2)
            }]
          };
        }
      } catch (error) {
        const processingTime = Date.now() - startTime;
        logger.error({ error, processingTime }, 'QR scan tool error');
        
        return {
          content: [{
            type: 'text',
            text: JSON.stringify({
              success: false,
              error: error instanceof Error ? error.message : 'Unknown error occurred',
              timestamp: new Date().toISOString(),
              processingTime
            }, null, 2)
          }],
          isError: true
        };
      }
    }
  };
}

// Helper function to validate base64 input
function isValidBase64(str: string): boolean {
  try {
    return Buffer.from(str, 'base64').toString('base64') === str;
  } catch (error) {
    return false;
  }
}

// Helper function to validate data URI
function isValidDataUri(str: string): boolean {
  const dataUriRegex = /^data:([a-zA-Z0-9][a-zA-Z0-9\/+]*);base64,([a-zA-Z0-9+/]+=*)$/;
  return dataUriRegex.test(str);
}

// Enhanced scan-qr tool with validation
export function createEnhancedScanQRTool() {
  const qrService = new QRService();

  return {
    name: 'scan-qr-enhanced',
    description: 'Enhanced QR code scanner with input validation and multiple processing strategies',
    inputSchema: {
      input: z.string().describe('Base64, file path, or data URI of image'),
      format: z.enum(['base64', 'filepath', 'datauri']).optional().default('base64'),
      options: z.object({
        inversionAttempts: z.enum(['dontInvert', 'onlyInvert', 'attemptBoth']).optional(),
        greyScaleWeights: z.object({
          red: z.number().optional(),
          green: z.number().optional(),
          blue: z.number().optional()
        }).optional()
      }).optional(),
      validateInput: z.boolean().optional().default(true),
      useMultipleStrategies: z.boolean().optional().default(false)
    },
    handler: async (args: any): Promise<any> => {
      const startTime = Date.now();
      
      try {
        logger.info({ 
          format: args.format,
          validateInput: args.validateInput,
          useMultipleStrategies: args.useMultipleStrategies
        }, 'Processing enhanced QR scan request');
        
        // Input validation if enabled
        if (args.validateInput) {
          if (args.format === 'base64' && !isValidBase64(args.input)) {
            return {
              content: [{
                type: 'text',
                text: JSON.stringify({
                  success: false,
                  error: 'Invalid base64 format',
                  timestamp: new Date().toISOString()
                }, null, 2)
              }],
              isError: true
            };
          }
          
          if (args.format === 'datauri' && !isValidDataUri(args.input)) {
            return {
              content: [{
                type: 'text',
                text: JSON.stringify({
                  success: false,
                  error: 'Invalid data URI format',
                  timestamp: new Date().toISOString()
                }, null, 2)
              }],
              isError: true
            };
          }
        }

        // Perform QR code scanning
        const result = await qrService.scanQRCode(
          args.input,
          args.format || 'base64',
          args.options
        );
        
        const processingTime = Date.now() - startTime;
        
        return {
          content: [{
            type: 'text',
            text: JSON.stringify({
              ...result,
              processingTime,
              enhanced: true
            }, null, 2)
          }],
          isError: !result.success
        };
      } catch (error) {
        const processingTime = Date.now() - startTime;
        logger.error({ error, processingTime }, 'Enhanced QR scan tool error');
        
        return {
          content: [{
            type: 'text',
            text: JSON.stringify({
              success: false,
              error: error instanceof Error ? error.message : 'Unknown error occurred',
              timestamp: new Date().toISOString(),
              processingTime,
              enhanced: true
            }, null, 2)
          }],
          isError: true
        };
      }
    }
  };
}
