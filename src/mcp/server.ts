import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { StreamableHTTPServerTransport } from '@modelcontextprotocol/sdk/server/streamableHttp.js';
import { randomUUID } from 'node:crypto';
import { logger } from '../utils/logger.js';
import { createScanQRTool } from './tools/scan-qr.tool.js';
import { createStreamQRTool } from './tools/stream-qr.tool.js';
import { createQRHistoryResource } from './resources/qr-history.resource.js';

export class QRMCPServer {
  private mcpServer: McpServer;
  private transport: StreamableHTTPServerTransport;

  constructor() {
    this.mcpServer = new McpServer({
      name: 'qrmcp-node',
      version: '1.0.0',
      description: 'QR Code MCP Server for Node.js'
    }, {
      capabilities: {
        logging: {},
        tools: {},
        resources: {}
      }
    });
    
    this.transport = new StreamableHTTPServerTransport({
      sessionIdGenerator: () => randomUUID()
    });
    
    this.initialize();
  }

  private initialize() {
    try {
      // Register tools
      this.registerTools();
      
      // Register resources
      this.registerResources();
      
      // Set up error handling
      this.mcpServer.server.onerror = (error: Error) => {
        logger.error({ error }, 'MCP server error');
      };
      
      logger.info('MCP server initialized successfully');
    } catch (error) {
      logger.error({ error }, 'Failed to initialize MCP server');
      throw error;
    }
  }

  private registerTools() {
    try {
      // Register scan-qr tool
      const scanQRTool = createScanQRTool();
      this.mcpServer.registerTool(
        scanQRTool.name,
        {
          title: 'QR Code Scanner',
          description: scanQRTool.description,
          inputSchema: scanQRTool.inputSchema
        },
        scanQRTool.handler
      );

      // Register stream-qr tool
      const streamQRTool = createStreamQRTool();
      this.mcpServer.registerTool(
        streamQRTool.name,
        {
          title: 'QR Code Stream Scanner',
          description: streamQRTool.description,
          inputSchema: streamQRTool.inputSchema
        },
        streamQRTool.handler
      );

      logger.info('MCP tools registered successfully');
    } catch (error) {
      logger.error({ error }, 'Failed to register MCP tools');
      throw error;
    }
  }

  private registerResources() {
    try {
      // Register QR history resource
      const qrHistoryResource = createQRHistoryResource();
      this.mcpServer.registerResource(
        qrHistoryResource.name,
        qrHistoryResource.template,
        {
          title: qrHistoryResource.title,
          description: qrHistoryResource.description
        },
        qrHistoryResource.handler
      );

      logger.info('MCP resources registered successfully');
    } catch (error) {
      logger.error({ error }, 'Failed to register MCP resources');
      throw error;
    }
  }

  async connect() {
    try {
      await this.mcpServer.connect(this.transport);
      logger.info('MCP server connected successfully');
    } catch (error) {
      logger.error({ error }, 'Failed to connect MCP server');
      throw error;
    }
  }

  async close() {
    try {
      await this.mcpServer.close();
      logger.info('MCP server closed successfully');
    } catch (error) {
      logger.error({ error }, 'Failed to close MCP server');
      throw error;
    }
  }

  getTransport() {
    return this.transport;
  }

  getMcpServer() {
    return this.mcpServer;
  }

  // Health check method
  isHealthy(): boolean {
    try {
      // Basic health check - ensure server is initialized
      return this.mcpServer !== null && this.transport !== null;
    } catch (error) {
      logger.error({ error }, 'Health check failed');
      return false;
    }
  }
}
