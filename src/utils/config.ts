import { z } from 'zod';
import dotenv from 'dotenv';

dotenv.config();

const ConfigSchema = z.object({
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.string().transform(Number).default('3000'),
  HOST: z.string().default('0.0.0.0'),
  LOG_LEVEL: z.enum(['trace', 'debug', 'info', 'warn', 'error']).default('info'),
  CORS_ORIGIN: z.string().default('*'),
  MAX_FILE_SIZE: z.string().transform(Number).default('10485760'), // 10MB
  STREAM_INTERVAL: z.string().transform(Number).default('100'), // ms
  AUTH_ENABLED: z.string().transform(v => v === 'true').default('false'),
  AUTH_TOKEN: z.string().optional(),
  RATE_LIMIT_WINDOW_MS: z.string().transform(Number).default('900000'), // 15 minutes
  RATE_LIMIT_MAX_REQUESTS: z.string().transform(Number).default('100'),
  HEALTH_CHECK_INTERVAL: z.string().transform(Number).default('30000'), // 30 seconds
});

export type Config = z.infer<typeof ConfigSchema>;

export const config = ConfigSchema.parse(process.env);

export default config;
