# QRMCPNode - QR Code MCP Server for Node.js

A production-ready Model Context Protocol (MCP) server for QR code detection and processing, built with TypeScript and Node.js.

## 🚀 Features

- **MCP Server**: Full Model Context Protocol implementation with tools and resources
- **QR Code Processing**: Advanced QR code detection using jsQR and Jimp
- **REST API**: HTTP endpoints for direct QR code processing
- **Express Integration**: Production-ready web server with security middleware
- **TypeScript**: Full type safety and modern development experience
- **Comprehensive Logging**: Structured logging with Pino
- **Rate Limiting**: Built-in protection against abuse
- **Health Monitoring**: Health check endpoints for monitoring
- **Testing**: Comprehensive test suite with Vitest

## 📦 Installation

```bash
# Clone the repository
git clone <repository-url>
cd MCPQRTS

# Install dependencies
npm install

# Copy environment configuration
cp .env.example .env

# Build the project
npm run build
```

## 🏃‍♂️ Quick Start

### Development Mode
```bash
npm run dev
```

### Production Mode
```bash
npm run build
npm start
```

### Running Tests
```bash
npm test
```

## 🔧 Configuration

Environment variables can be configured in `.env`:

```env
NODE_ENV=development
PORT=3000
HOST=0.0.0.0
LOG_LEVEL=info
CORS_ORIGIN=*
MAX_FILE_SIZE=10485760
STREAM_INTERVAL=100
AUTH_ENABLED=false
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

## 📡 API Endpoints

### Health Check
```bash
GET /health
```

### QR Code Scanning
```bash
POST /api/qr/scan
Content-Type: application/json

{
  "input": "base64-encoded-image-data",
  "format": "base64",
  "options": {
    "inversionAttempts": "attemptBoth"
  }
}
```

### MCP Server Info
```bash
GET /mcp/info
```

## 🛠 MCP Tools

### scan-qr
Scan and decode QR codes from images provided as base64, file path, or data URI.

**Input Schema:**
- `input` (string): Base64, file path, or data URI of image
- `format` (enum): Input format - 'base64', 'filepath', 'datauri'
- `options` (object): Processing options

### stream-qr
Start streaming QR code detection (placeholder for Phase 4 implementation).

## 📊 Project Status

### ✅ Completed Phases

- **Phase 1: Foundation & Setup** - Complete
  - Dependencies installed and configured
  - TypeScript configuration optimized
  - Project structure established
  - Environment configuration with Zod validation
  - Pino logger setup

- **Phase 2: Core MCP Implementation** - Complete
  - MCP server with tools and resources
  - Express application with middleware
  - Health check endpoints
  - Main server entry point
  - Basic testing suite

### 🚧 Upcoming Phases

- **Phase 3: QR Code Processing** - In Progress
  - Enhanced QR decoder implementation
  - Advanced image processing
  - Multiple detection strategies

- **Phase 4: Streaming & Real-time Features** - Planned
  - Server-Sent Events (SSE) streaming
  - Real-time QR monitoring
  - Streaming tools implementation

- **Phase 5: Testing & Quality Assurance** - Planned
  - Comprehensive unit tests
  - Integration tests
  - End-to-end tests

- **Phase 6: Containerization & Deployment** - Planned
  - Docker configuration
  - CI/CD pipeline
  - Deployment documentation

## 🧪 Testing

The project includes a comprehensive test suite:

```bash
# Run all tests
npm test

# Run unit tests only
npm run test:unit

# Run integration tests
npm run test:integration

# Run with coverage
npm run test:coverage
```

## 🏗 Architecture

```
src/
├── mcp/                 # MCP server implementation
│   ├── server.ts       # Main MCP server
│   ├── tools/          # MCP tools
│   └── resources/      # MCP resources
├── processors/         # Image and QR processing
├── services/           # Business logic services
├── routes/             # Express routes
├── middleware/         # Express middleware
├── utils/              # Utilities and configuration
└── types/              # TypeScript type definitions
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.
