{"name": "qrmcp-node", "version": "1.0.0", "description": "QR Code MCP Server for Node.js - A production-ready Model Context Protocol server for QR code detection and processing", "main": "dist/server.js", "type": "module", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "tsx src/server.ts", "dev:watch": "tsx watch src/server.ts", "test": "vitest", "test:unit": "vitest run --dir test/unit", "test:integration": "hurl --test --glob 'test/integration/**/*.hurl'", "test:e2e": "vitest run --dir test/e2e", "test:coverage": "vitest run --coverage", "test:watch": "vitest watch", "lint": "echo '<PERSON><PERSON> not configured yet'", "format": "echo 'Formatting not configured yet'"}, "keywords": ["mcp", "qr-code", "model-context-protocol", "nodejs", "typescript", "server"], "author": "QRMCPNode Development Team", "license": "MIT", "devDependencies": {"@orangeopensource/hurl": "^7.0.0", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/node": "^24.3.1", "dotenv": "^17.2.2", "nodemon": "^3.1.10", "pino-http": "^10.5.0", "pino-pretty": "^13.1.1", "tsx": "^4.20.5", "typescript": "^5.9.2", "vitest": "^3.2.4"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.17.5", "cors": "^2.8.5", "express": "^5.1.0", "express-rate-limit": "^8.1.0", "helmet": "^8.1.0", "jimp": "^1.6.0", "jsqr": "^1.4.0", "pino": "^9.9.5", "zod": "^3.25.76"}}