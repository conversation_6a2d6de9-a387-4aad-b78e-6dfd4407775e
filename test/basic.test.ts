import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { Server } from '../src/server.js';

describe('QRMCPNode Basic Tests', () => {
  let server: Server;
  let baseUrl: string;

  beforeAll(async () => {
    server = new Server();
    await server.start();
    baseUrl = 'http://localhost:3000';
  });

  afterAll(async () => {
    if (server) {
      await server.stop();
    }
  });

  it('should respond to health check', async () => {
    const response = await fetch(`${baseUrl}/health`);
    const data = await response.json();
    
    expect(response.status).toBe(200);
    expect(data.status).toBe('healthy');
    expect(data.version).toBe('1.0.0');
  });

  it('should provide API info', async () => {
    const response = await fetch(`${baseUrl}/api`);
    const data = await response.json();
    
    expect(response.status).toBe(200);
    expect(data.name).toBe('QRMCPNode API');
    expect(data.endpoints).toHaveLength(2);
  });

  it('should provide MCP info', async () => {
    const response = await fetch(`${baseUrl}/mcp/info`);
    const data = await response.json();
    
    expect(response.status).toBe(200);
    expect(data.name).toBe('qrmcp-node');
    expect(data.capabilities.tools).toBe(true);
    expect(data.tools).toHaveLength(2);
    expect(data.tools[0].name).toBe('scan-qr');
  });

  it('should handle invalid QR scan request', async () => {
    const response = await fetch(`${baseUrl}/api/qr/scan`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        input: 'invalid-base64-data',
        format: 'base64'
      })
    });
    
    const data = await response.json();
    expect(response.status).toBe(200);
    expect(data.success).toBe(false);
  });
});
