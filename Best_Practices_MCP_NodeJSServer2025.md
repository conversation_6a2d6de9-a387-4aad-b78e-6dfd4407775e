# 🚀 Best Practices for MCP (Model Context Protocol) Node.js Server - 2025 Edition

> **Key Highlights:**
> - **TypeScript-first implementation** with official MCP SDK
> - **Multiple framework options** (Express, Fastify, NestJS)
> - **Streamable HTTP transport** with SSE support
> - **Comprehensive testing** with Hurl and .http files
> - **Production-ready** patterns with Docker and CI/CD

---

## 📋 Table of Contents

1. [Overview & Architecture](#overview--architecture)
2. [Quick Start Guide](#quick-start-guide)
3. [Core Implementation](#core-implementation)
4. [REST API Integration](#rest-api-integration)
5. [Testing with .http Files](#testing-with-http-files)
6. [Authentication (Optional)](#authentication-optional)
7. [Production Deployment](#production-deployment)
8. [Best Practices Summary](#best-practices-summary)

---

## 📐 Overview & Architecture

### What is MCP?

The **Model Context Protocol (MCP)** is an open standard that provides a universal way for AI models to securely interact with external data sources, tools, and environments [[1]](https://www.getzep.com/ai-agents/developer-guide-to-mcp/#:~:text=Model%20Context%20Protocol%20%28MCP%29,tools%2C%20and%20environments%2C%20allowing).

### Key Architectural Components

| Component | Description | Best Practice |
|-----------|-------------|---------------|
| **MCP Server** | Exposes tools, resources, and prompts | Use official `@modelcontextprotocol/sdk` [[2]](https://www.npmjs.com/package/@modelcontextprotocol/sdk#:~:text=import%20%7B%20McpServer%20%7D%20from%20%22%40modelcontextprotocol/sdk/server/mcp.js%22%3B) |
| **Transport Layer** | Communication mechanism | Stdio for local, HTTP/SSE for remote [[3]](https://www.npmjs.com/package/@modelcontextprotocol/sdk#:~:text=%2D%20Use%20standard%20transports,like%20stdio%20and%20Streamable) |
| **Protocol** | Message format | JSON-RPC 2.0 [[1]](https://www.getzep.com/ai-agents/developer-guide-to-mcp/#:~:text=Model%20Context%20Protocol%20%28MCP%29,tools%2C%20and%20environments%2C%20allowing) |
| **Validation** | Data integrity | Zod schemas for type safety [[2]](https://www.npmjs.com/package/@modelcontextprotocol/sdk#:~:text=import%20%7B%20McpServer%20%7D%20from%20%22%40modelcontextprotocol/sdk/server/mcp.js%22%3B) |
| **Logging** | Observability | Pino for file-based logs [[2]](https://www.npmjs.com/package/@modelcontextprotocol/sdk#:~:text=import%20%7B%20McpServer%20%7D%20from%20%22%40modelcontextprotocol/sdk/server/mcp.js%22%3B) |

### Framework Options

| Framework | Use Case | Performance | Complexity |
|-----------|----------|-------------|------------|
| **Express.js** | General purpose, middleware-rich | Good | Low [[4]](https://flatirons.com/blog/top-nodejs-backend-frameworks-2024/#:~:text=Express.js%20is%20widely%20used,for%20its%20simplicity%20and) |
| **Fastify** | High-performance, schema-based | Excellent | Medium [[5]](https://www.speakeasy.com/blog/picking-a-javascript-api-framework#:~:text=Fastify%3A%20A%20replacement%20for) |
| **Koa.js** | Lightweight, async-first | Good | Low [[6]](https://flatirons.com/blog/top-nodejs-backend-frameworks-2024/#:~:text=Koa.js%20%7C%20Streamlined%20and,smaller%20core%20for%20customization) |
| **NestJS** | Enterprise, TypeScript-native | Good | High [[7]](https://flatirons.com/blog/top-nodejs-backend-frameworks-2024/#:~:text=Some%20I/O%20operations%20handled,building%20server%2Dside%20applications%20and) |

---

## 🚀 Quick Start Guide

### Prerequisites

```bash
# Node.js 20+ LTS recommended
node --version  # Should be >= 20.0.0
npm --version   # Should be >= 10.0.0
```

### Initialize Project

```bash
# Create project directory
mkdir mcp-nodejs-server && cd mcp-nodejs-server

# Initialize npm project
npm init -y

# Install core dependencies
npm install @modelcontextprotocol/sdk zod pino

# Install TypeScript and dev dependencies
npm install -D typescript @types/node tsx
npm install -D @orangeopensource/hurl  # For .http testing

# Initialize TypeScript
npx tsc --init
```

### Project Structure

```
/mcp-nodejs-server
├── /src
│   ├── /tools          # MCP tool implementations
│   ├── /handlers       # Request handlers
│   ├── /schemas        # Zod schemas
│   ├── server.ts       # Main server file
│   └── types.ts        # TypeScript definitions
├── /test
│   └── /integration    # .hurl test files
├── package.json
├── tsconfig.json
├── Dockerfile
└── README.md
```

---

## 💻 Core Implementation

### Basic MCP Server (Express Example)

```typescript
// src/server.ts
import express from 'express';
import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { StdioServerTransport, HttpServerTransport } from '@modelcontextprotocol/sdk/server/index.js';
import { z } from 'zod';
import pino from 'pino';

// Initialize logger
const logger = pino({
  transport: {
    target: 'pino-pretty',
    options: {
      destination: './logs/mcp-server.log'
    }
  }
});

// Initialize MCP server
const mcpServer = new McpServer({
  name: 'nodejs-mcp-server',
  version: '1.0.0'
});

// Tool input schema
const WeatherSchema = z.object({
  city: z.string().min(1).describe('City name'),
  units: z.enum(['metric', 'imperial']).optional().default('metric')
});

// Register MCP tool
mcpServer.registerTool('get-weather', {
  title: 'Get Weather',
  description: 'Fetch current weather for a city',
  inputSchema: WeatherSchema
}, async (args) => {
  try {
    const { city, units } = WeatherSchema.parse(args);
    
    // Implementation logic here
    const weatherData = await fetchWeatherData(city, units);
    
    return {
      content: [{
        type: 'text',
        text: JSON.stringify(weatherData, null, 2)
      }]
    };
  } catch (error) {
    logger.error({ error, args }, 'Weather tool error');
    throw new Error(`Failed to fetch weather: ${error.message}`);
  }
});

// Setup transport based on environment
async function startServer() {
  if (process.env.MCP_TRANSPORT === 'stdio') {
    const transport = new StdioServerTransport();
    await mcpServer.connect(transport);
    logger.info('MCP server started with stdio transport');
  } else {
    // HTTP/SSE transport
    const app = express();
    app.use(express.json());
    
    const transport = new HttpServerTransport();
    transport.attachToExpress(app, '/mcp');
    
    await mcpServer.connect(transport);
    
    const port = process.env.PORT || 3000;
    app.listen(port, () => {
      logger.info(`MCP server started on port ${port}`);
    });
  }
}

startServer().catch(console.error);
```

### Fastify Implementation (High Performance)

```typescript
// src/server-fastify.ts
import Fastify from 'fastify';
import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { HttpServerTransport } from '@modelcontextprotocol/sdk/server/http.js';
import { z } from 'zod';
import pino from 'pino';

const fastify = Fastify({
  logger: {
    level: 'info',
    file: './logs/mcp-server.log'
  }
});

const mcpServer = new McpServer({
  name: 'fastify-mcp-server',
  version: '1.0.0'
});

// Register tools (same as Express example)
// ...

// Attach MCP to Fastify
const transport = new HttpServerTransport();
transport.attachToFastify(fastify, '/mcp');

// Start server
const start = async () => {
  try {
    await mcpServer.connect(transport);
    await fastify.listen({ port: 3000, host: '0.0.0.0' });
  } catch (err) {
    fastify.log.error(err);
    process.exit(1);
  }
};

start();
```

### Resource Implementation

```typescript
// src/tools/database-resource.ts
import { z } from 'zod';

// Resource schema
const QuerySchema = z.object({
  query: z.string(),
  params: z.array(z.any()).optional()
});

// Register resource
mcpServer.registerResource('database-query', {
  title: 'Database Query',
  description: 'Execute database queries',
  mimeType: 'application/json',
  inputSchema: QuerySchema
}, async (args) => {
  const { query, params } = QuerySchema.parse(args);
  
  // Execute query with proper connection pooling
  const results = await db.query(query, params);
  
  return {
    content: [{
      type: 'resource',
      mimeType: 'application/json',
      data: JSON.stringify(results)
    }]
  };
});
```

---

## 🔗 REST API Integration

### Semantic Tool for REST APIs

```typescript
// src/tools/rest-api-tool.ts
import { z } from 'zod';
import axios from 'axios';
import axiosRetry from 'axios-retry';

// Configure axios with retries
axiosRetry(axios, {
  retries: 3,
  retryDelay: axiosRetry.exponentialDelay,
  retryCondition: (error) => {
    return axiosRetry.isNetworkOrIdempotentRequestError(error) ||
           error.response?.status === 429;
  }
});

// API endpoint schema
const ApiCallSchema = z.object({
  endpoint: z.string().url(),
  method: z.enum(['GET', 'POST', 'PUT', 'DELETE']).default('GET'),
  headers: z.record(z.string()).optional(),
  body: z.any().optional(),
  timeout: z.number().default(30000)
});

// Register REST API tool
mcpServer.registerTool('call-api', {
  title: 'Call REST API',
  description: 'Make HTTP requests to external APIs',
  inputSchema: ApiCallSchema
}, async (args) => {
  const config = ApiCallSchema.parse(args);
  
  try {
    const response = await axios({
      url: config.endpoint,
      method: config.method,
      headers: config.headers,
      data: config.body,
      timeout: config.timeout
    });
    
    return {
      content: [{
        type: 'text',
        text: JSON.stringify({
          status: response.status,
          headers: response.headers,
          data: response.data
        }, null, 2)
      }]
    };
  } catch (error) {
    logger.error({ error: error.message, config }, 'API call failed');
    
    if (axios.isAxiosError(error)) {
      throw new Error(`API call failed: ${error.response?.status} - ${error.response?.data?.message || error.message}`);
    }
    throw error;
  }
});
```

### Streaming Response Support

```typescript
// src/tools/streaming-tool.ts
import { Readable } from 'stream';

mcpServer.registerTool('stream-data', {
  title: 'Stream Large Dataset',
  description: 'Stream large amounts of data efficiently',
  inputSchema: z.object({
    source: z.string(),
    chunkSize: z.number().default(1024)
  })
}, async function* (args) {
  const { source, chunkSize } = args;
  
  // Create readable stream
  const stream = createDataStream(source);
  
  // Yield chunks
  for await (const chunk of stream) {
    yield {
      content: [{
        type: 'text',
        text: chunk.toString()
      }]
    };
  }
});
```

---

## 🧪 Testing with .http Files

### Install Hurl for Testing

```bash
# Install Hurl globally
npm install -g @orangeopensource/hurl

# Add to package.json scripts
{
  "scripts": {
    "test:integration": "hurl --test --glob \"test/integration/**/*.hurl\""
  }
}
```

### Example .hurl Test File

```hurl
# test/integration/tools.hurl

# Test: Get Weather Tool
POST http://localhost:3000/mcp
Content-Type: application/json
```json
{
  "jsonrpc": "2.0",
  "method": "tools/invoke",
  "params": {
    "name": "get-weather",
    "arguments": {
      "city": "London",
      "units": "metric"
    }
  },
  "id": 1
}
```
HTTP 200
[Asserts]
jsonpath "$.result.content[0].text" contains "temperature"
jsonpath "$.result.content[0].text" contains "London"

# Test: Stream Data with SSE
POST http://localhost:3000/mcp
Accept: text/event-stream
Content-Type: application/json
```json
{
  "jsonrpc": "2.0",
  "method": "tools/invoke",
  "params": {
    "name": "stream-data",
    "arguments": {
      "source": "large-dataset",
      "chunkSize": 512
    }
  },
  "id": 2
}
```
HTTP 200
[Asserts]
header "Content-Type" == "text/event-stream"
body contains "data:"
```

### Unit Testing with Jest

```typescript
// test/unit/tools.test.ts
import { describe, it, expect, beforeAll } from '@jest/globals';
import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';

describe('MCP Tools', () => {
  let server: McpServer;
  
  beforeAll(() => {
    server = new McpServer({ name: 'test', version: '1.0.0' });
    // Register tools
  });
  
  it('should validate weather tool input', async () => {
    const result = await server.invokeTool('get-weather', {
      city: 'London',
      units: 'metric'
    });
    
    expect(result.content[0].type).toBe('text');
    expect(JSON.parse(result.content[0].text)).toHaveProperty('temperature');
  });
});
```

---

## 🔐 Authentication (Optional)

### Bearer Token Authentication

```typescript
// src/middleware/auth.ts
import { Request, Response, NextFunction } from 'express';

export const bearerAuth = (req: Request, res: Response, next: NextFunction) => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader?.startsWith('Bearer ')) {
    return res.status(401).json({ error: 'Missing bearer token' });
  }
  
  const token = authHeader.substring(7);
  
  // Validate token
  if (!isValidToken(token)) {
    return res.status(403).json({ error: 'Invalid token' });
  }
  
  next();
};

// Apply to MCP endpoint
app.use('/mcp', bearerAuth);
```

### OAuth 2.1 Support

```typescript
// src/auth/oauth.ts
import { Issuer } from 'openid-client';

async function setupOAuth() {
  const issuer = await Issuer.discover('https://auth.example.com');
  
  const client = new issuer.Client({
    client_id: process.env.OAUTH_CLIENT_ID,
    client_secret: process.env.OAUTH_CLIENT_SECRET,
    redirect_uris: ['http://localhost:3000/callback'],
    response_types: ['code']
  });
  
  return client;
}
```

---

## 🚀 Production Deployment

### Multi-Stage Dockerfile

```dockerfile
# Build stage
FROM node:20-alpine AS builder
WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci

# Copy source
COPY . .

# Build TypeScript
RUN npm run build

# Production stage
FROM node:20-alpine
WORKDIR /app

# Install production dependencies only
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

# Copy built application
COPY --from=builder /app/dist ./dist

# Create non-root user
USER node

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:3000/health', (r) => process.exit(r.statusCode === 200 ? 0 : 1))"

# Expose port
EXPOSE 3000

# Start server
CMD ["node", "dist/server.js"]
```

### Docker Compose Configuration

```yaml
# docker-compose.yml
version: '3.8'

services:
  mcp-server:
    build: .
    ports:
      - "3000:3000"
    environment:
      NODE_ENV: production
      LOG_LEVEL: info
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - mcp-network

  redis:
    image: redis:alpine
    networks:
      - mcp-network

networks:
  mcp-network:
    driver: bridge
```

### CI/CD Pipeline (GitHub Actions)

```yaml
# .github/workflows/deploy.yml
name: Deploy MCP Server

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18.x, 20.x, 22.x]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    
    - run: npm ci
    - run: npm run lint
    - run: npm run test
    - run: npm run test:integration
    
    - name: Security scan
      run: npm audit --production

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        push: true
        tags: myregistry/mcp-server:latest
    
    - name: Deploy to production
      run: |
        # Deploy script here
        echo "Deploying to production..."
```

---

## 📊 Best Practices Summary

### ✅ Architecture & Design

| Practice | Description | Implementation |
|----------|-------------|----------------|
| **Modular Structure** | Separate concerns into modules | `/tools`, `/handlers`, `/schemas` directories [[8]](https://www.flowhunt.io/blog/mcp-server-development-guide/#:~:text=For%20Node.js%2C%20use%20npm%20install) |
| **TypeScript First** | Type safety and better IDE support | Use strict TypeScript configuration [[2]](https://www.npmjs.com/package/@modelcontextprotocol/sdk#:~:text=import%20%7B%20McpServer%20%7D%20from%20%22%40modelcontextprotocol/sdk/server/mcp.js%22%3B) |
| **Schema Validation** | Validate all inputs/outputs | Zod for runtime validation [[2]](https://www.npmjs.com/package/@modelcontextprotocol/sdk#:~:text=import%20%7B%20McpServer%20%7D%20from%20%22%40modelcontextprotocol/sdk/server/mcp.js%22%3B) |
| **Error Handling** | Comprehensive error handling | Try-catch blocks, structured errors [[9]](https://www.npmjs.com/package/@modelcontextprotocol/sdk#:~:text=description%3A%20%22Get%20weather%20data%20for%20a) |

### 🔒 Security

> **Security Checklist:**
> - ✅ Use TLS for all remote connections [[10]](https://www.getzep.com/ai-agents/developer-guide-to-mcp/#:~:text=Unified%20Security%20%26%20Policy,its%20tools.%20In%20an)
> - ✅ Implement OAuth 2.1 for production [[11]](https://www.getzep.com/ai-agents/developer-guide-to-mcp/#:~:text=Security%20and%20Access%20Control,authorized%20data%20and%20respecting)
> - ✅ Run containers as non-root user [[12]](https://moldstud.com/articles/p-building-a-seamless-cicd-pipeline-for-nodejs-applications-using-docker#:~:text=Lastly%2C%20consider%20using%20a%20non%2Droot%20user%20with)
> - ✅ Regular dependency scanning [[13]](https://www.w3schools.com/nodejs/nodejs_ci_cd.asp#:~:text=to%20exclude%20unnecessary%20files,%2D%20Scan%20images%20for)
> - ✅ Whitelist allowed operations [[14]](https://blog.promptlayer.com/how-to-build-mcp-server/#:~:text=%2D%20Remote%20servers%3A%20Require,use%20TLS%20for%20encrypted)

### ⚡ Performance

```typescript
// Performance optimizations
const optimizations = {
  connectionPooling: true,
  compressionEnabled: true,
  cachingStrategy: 'redis',
  streamingThreshold: 1024 * 1024, // 1MB
  maxConcurrentRequests: 100
};
```

### 🧪 Testing Strategy

1. **Unit Tests**: Jest/Mocha for business logic
2. **Integration Tests**: Hurl for HTTP endpoints [[15]](http://blog.humphd.org/http-testing-with-hurl-in-node-js/#:~:text=I%27ve%20been%20using%20Hurl,tests%20in%20JavaScript%2Dbased%20testing)
3. **E2E Tests**: Full workflow validation
4. **Performance Tests**: Load testing with k6
5. **Security Tests**: OWASP dependency check

### 📈 Monitoring & Observability

```typescript
// Monitoring setup
import { PrometheusExporter } from '@opentelemetry/exporter-prometheus';

const prometheusExporter = new PrometheusExporter({
  port: 9090,
}, () => {
  console.log('Prometheus metrics server started');
});

// Log aggregation with Pino
const logger = pino({
  level: process.env.LOG_LEVEL || 'info',
  transport: {
    targets: [
      { target: 'pino-pretty', options: { destination: 1 } },
      { target: 'pino-file', options: { destination: './logs/app.log' } }
    ]
  }
});
```

---

## 🎯 Key Takeaways

> **Production Readiness Checklist:**
> 
> 1. **Framework Selection**: Choose based on performance needs (Fastify for high throughput) [[5]](https://www.speakeasy.com/blog/picking-a-javascript-api-framework#:~:text=Fastify%3A%20A%20replacement%20for)
> 2. **Testing**: Comprehensive testing with Hurl for .http files [[15]](http://blog.humphd.org/http-testing-with-hurl-in-node-js/#:~:text=I%27ve%20been%20using%20Hurl,tests%20in%20JavaScript%2Dbased%20testing)
> 3. **Security**: OAuth 2.1, TLS, dependency scanning [[11]](https://www.getzep.com/ai-agents/developer-guide-to-mcp/#:~:text=Security%20and%20Access%20Control,authorized%20data%20and%20respecting)
> 4. **Deployment**: Multi-stage Docker builds, CI/CD automation [[16]](https://moldstud.com/articles/p-building-a-seamless-cicd-pipeline-for-nodejs-applications-using-docker#:~:text=Implement%20a%20multi%2Dstage%20build,Statistics%20indicate%20that%20multi%2Dstage)
> 5. **Monitoring**: Structured logging, metrics, health checks [[17]](https://moldstud.com/articles/p-building-a-seamless-cicd-pipeline-for-nodejs-applications-using-docker#:~:text=Incorporate%20a%20monitoring%20solution,up%20to%2040%25.%20Tracking)

### 🔗 Additional Resources

- [Official MCP SDK Documentation](https://www.npmjs.com/package/@modelcontextprotocol/sdk)
- [Node.js Best Practices Repository](https://github.com/goldbergyoni/nodebestpractices)
- [MCP Inspector Tool](https://github.com/modelcontextprotocol/inspector)
- [Hurl Documentation](https://hurl.dev/)

---

**Document Version**: 1.0.0  
**Last Updated**: September 2025  
**Maintained By**: Your DevOps Team


### References

1. **How to Build an MCP Server in Python: A Complete Guide**. [https://scrapfly.io](https://scrapfly.io/blog/how-to-build-an-mcp-server-in-python-a-complete-guide/#:~:text=In%20MCP%2C%20there%20are%20three%20key)
2. **How MCP servers work: Components, logic, and architecture**. [https://workos.com](https://workos.com/blog/how-mcp-servers-work#:~:text=%21%21JSON%2DRPC%20is%20a%20lightweight,a%20server%2C%20enabling%20the)
3. **How MCP servers work: Components, logic, and architecture**. [https://workos.com](https://workos.com/blog/how-mcp-servers-work#:~:text=A%20context%20store%20is,or%20memory.%20It%20could)
4. **Development Guide for MCP Servers**. [https://www.flowhunt.io](https://www.flowhunt.io/blog/mcp-server-development-guide/#:~:text=keeps%20a%20list%2C%20or,add%20new%20tools%20easily%2C)
5. **How to Build an MCP Server in Python: A Complete Guide**. [https://scrapfly.io](https://scrapfly.io/blog/how-to-build-an-mcp-server-in-python-a-complete-guide/#:~:text=Add%207%20and)
6. **How to Build an MCP Server in Python: A Complete Guide**. [https://scrapfly.io](https://scrapfly.io/blog/how-to-build-an-mcp-server-in-python-a-complete-guide/#:~:text=This%20returns%20the%20content,when%20requested%20by%20the)
7. **How MCP servers work: Components, logic, and architecture**. [https://workos.com](https://workos.com/blog/how-mcp-servers-work#:~:text=How%20MCP%20servers%20work%3A%20Components%2C%20logic%2C%20and)
8. **Development Guide for MCP Servers**. [https://www.flowhunt.io](https://www.flowhunt.io/blog/mcp-server-development-guide/#:~:text=This%20overview%20shows%20how,clear%20and%20reliable%20protocol%2Dbased)
9. **HTTP Testing with Hurl in node.js**. [http://blog.humphd.org](http://blog.humphd.org/http-testing-with-hurl-in-node-js/#:~:text=I%27ve%20been%20using%20Hurl,tests%20in%20JavaScript%2Dbased%20testing)
10. **How .http Files in Your IDE Can Revolutionize Your Workflow**. [https://testfully.io](https://testfully.io/blog/http-files/#:~:text=http%20files%20are%20a,Think%20of%20them%20as)
11. **Understanding the function of Supertest in API testing.**. [https://www.accelq.com](https://www.accelq.com/blog/supertest/#:~:text=Supertest%20is%20a%20Node.js,automated%20tests%20for%20routes)
12. **10 Tools for Mocking APIs in Node.js Unit Tests**. [https://medium.com](https://medium.com/@arunangshudas/10-tools-for-mocking-apis-in-node-js-unit-tests-d5fd69f18862#:~:text=Nock%20is%20a%20powerful,or%20libraries%20like%20axios%2C)
13. **HTTP Testing with Hurl in node.js**. [http://blog.humphd.org](http://blog.humphd.org/http-testing-with-hurl-in-node-js/#:~:text=which%20you%20can%20call%20in%20your)
14. **Mocha - the fun, simple, flexible JavaScript test framework**. [https://mochajs.org](https://mochajs.org/#:~:text=Mocha%20is%20a%20feature%2Drich,asynchronous%20testing%20simple%20and)
15. **HTTP Testing with Hurl in node.js**. [http://blog.humphd.org](http://blog.humphd.org/http-testing-with-hurl-in-node-js/#:~:text=%22test%3Aintegration%22%3A%20%22hurl%20%2D%2Dtest%20%2D%2Dglob%20%5C%22test/integration/%2A%2A/%2A.hurl%5C%22%22)
16. **HTTP Testing with Hurl in node.js**. [http://blog.humphd.org](http://blog.humphd.org/http-testing-with-hurl-in-node-js/#:~:text=Hurl%20is%20a%20command%2Dline,year%20ago%2C%20and%20have)
17. **A Developer's Guide to the MCP - Zep**. [https://www.getzep.com](https://www.getzep.com/ai-agents/developer-guide-to-mcp/#:~:text=Model%20Context%20Protocol%20%28MCP%29,tools%2C%20and%20environments%2C%20allowing)
18. **modelcontextprotocol/sdk**. [https://www.npmjs.com](https://www.npmjs.com/package/@modelcontextprotocol/sdk#:~:text=%2D%20Use%20standard%20transports,like%20stdio%20and%20Streamable)
19. **A Developer's Guide to the MCP - Zep**. [https://www.getzep.com](https://www.getzep.com/ai-agents/developer-guide-to-mcp/#:~:text=Security%20and%20Access%20Control,authorized%20data%20and%20respecting)
20. **modelcontextprotocol/sdk**. [https://www.npmjs.com](https://www.npmjs.com/package/@modelcontextprotocol/sdk#:~:text=import%20%7B%20McpServer%20%7D%20from%20%22%40modelcontextprotocol/sdk/server/mcp.js%22%3B)
21. **Development Guide for MCP Servers**. [https://www.flowhunt.io](https://www.flowhunt.io/blog/mcp-server-development-guide/#:~:text=For%20Node.js%2C%20use%20npm%20install)
22. **Development Guide for MCP Servers**. [https://www.flowhunt.io](https://www.flowhunt.io/blog/mcp-server-development-guide/#:~:text=%C3%82%20%C3%82%20%C3%82%20%C3%82)
23. **5 Best Practices for Building MCP Servers**. [https://snyk.io](https://snyk.io/articles/5-best-practices-for-building-mcp-servers/#:~:text=%29%2C%20and%20don%E2%80%99t%20use,or%20square%20brackets%20like)
24. **modelcontextprotocol/sdk**. [https://www.npmjs.com](https://www.npmjs.com/package/@modelcontextprotocol/sdk#:~:text=description%3A%20%22Get%20weather%20data%20for%20a)
25. **5 Best Practices for Building MCP Servers**. [https://snyk.io](https://snyk.io/articles/5-best-practices-for-building-mcp-servers/#:~:text=The%20premise%20is%20as,data%20as%20possible%20for)
26. **5 Best Practices for Building MCP Servers**. [https://snyk.io](https://snyk.io/articles/5-best-practices-for-building-mcp-servers/#:~:text=My%20recommendation%3A%20Use%20a,logging%20library%20such%20as)
27. **5 Best Practices for Building MCP Servers**. [https://snyk.io](https://snyk.io/articles/5-best-practices-for-building-mcp-servers/#:~:text=However%2C%20forgoing%20logging%20entirely,calls%20and%20other%20processing)
28. **A Developer's Guide to the MCP - Zep**. [https://www.getzep.com](https://www.getzep.com/ai-agents/developer-guide-to-mcp/#:~:text=Unified%20Security%20%26%20Policy,its%20tools.%20In%20an)
29. **How to Build an MCP Server: Setup and Management Guide**. [https://blog.promptlayer.com](https://blog.promptlayer.com/how-to-build-mcp-server/#:~:text=%2D%20Remote%20servers%3A%20Require,use%20TLS%20for%20encrypted)
30. **How to Build an MCP Server Fast: A Step-by-Step Tutorial**. [https://medium.com](https://medium.com/@eugenesh4work/how-to-build-an-mcp-server-fast-a-step-by-step-tutorial-e09faa5f7e3b#:~:text=Use%20the%20MCP%20inspector%20to%20test%20your)
31. **Building a Production-Ready Weather MCP Server with ...**. [https://dev.to](https://dev.to/glaucia86/building-a-production-ready-weather-mcp-server-with-clean-architecture-redis-cache-and-solid-32cp#:~:text=Testable%3A%20comprehensive%20unit%20and%20integration%20testing%20strategies)
32. **Debugging Model Context Protocol (MCP) Servers: Tips and ...**. [https://www.mcpevals.io](https://www.mcpevals.io/blog/debugging-mcp-servers-tips-and-best-practices#:~:text=content%3A%20%5B%7B%20type%3A%20%22text%22%2C,%60Result%3A%20%24%7Ba%20%2B%20b%7D%60)
33. **Building a Production-Ready Weather MCP Server with ...**. [https://dev.to](https://dev.to/glaucia86/building-a-production-ready-weather-mcp-server-with-clean-architecture-redis-cache-and-solid-32cp#:~:text=%2D%20Observability%3A%20Detailed%20logging%20for%20debugging%20and)
34. **How to Build an MCP Server: Setup and Management Guide**. [https://blog.promptlayer.com](https://blog.promptlayer.com/how-to-build-mcp-server/#:~:text=%2D%20Set%20up%20alerts,in%20errors%20or%20unusual)
35. **5 Best Practices for Building MCP Servers**. [https://snyk.io](https://snyk.io/articles/5-best-practices-for-building-mcp-servers/#:~:text=MCP%20Server%20best%20practice%202%3A%20MCP%20Server)
36. **Building a Production-Ready Weather MCP Server with ...**. [https://dev.to](https://dev.to/glaucia86/building-a-production-ready-weather-mcp-server-with-clean-architecture-redis-cache-and-solid-32cp#:~:text=this.logger.info%28%27Weather%20MCP%20Server%20started%20successfully%27%29%3B)
37. **A Developer's Guide to the MCP - Zep**. [https://www.getzep.com](https://www.getzep.com/ai-agents/developer-guide-to-mcp/#:~:text=How%20MCP%20is%20used%3A,to%20connect%20to%20a)
38. **5 Best Practices for Building MCP Servers**. [https://snyk.io](https://snyk.io/articles/5-best-practices-for-building-mcp-servers/#:~:text=Alternatives%20are%20to%20use,or%20an%20underscore%20as)
39. **Building a Production-Ready Weather MCP Server with ...**. [https://dev.to](https://dev.to/glaucia86/building-a-production-ready-weather-mcp-server-with-clean-architecture-redis-cache-and-solid-32cp#:~:text=%2D%20%F0%9F%A7%AA%20Tests%3A%20Unit,%2B%20Integration%20tests%20com)
40. **5 Best Practices for Building MCP Servers**. [https://snyk.io](https://snyk.io/articles/5-best-practices-for-building-mcp-servers/#:~:text=5%20Best%20Practices%20for%20Building%20MCP)
41. **Building a Production-Ready Weather MCP Server with ...**. [https://dev.to](https://dev.to/glaucia86/building-a-production-ready-weather-mcp-server-with-clean-architecture-redis-cache-and-solid-32cp#:~:text=A%20robust%20Weather%20MCP%20Server%20in)
42. **How to Build an MCP Server: Setup and Management Guide**. [https://blog.promptlayer.com](https://blog.promptlayer.com/how-to-build-mcp-server/#:~:text=How%20to%20Build%20an,Step%2Dby%2DStep%20Guide%20for%20Claude)
43. **How to Build an MCP Server Fast: A Step-by-Step Tutorial**. [https://medium.com](https://medium.com/@eugenesh4work/how-to-build-an-mcp-server-fast-a-step-by-step-tutorial-e09faa5f7e3b#:~:text=How%20to%20Build%20an,Server%20Fast%3A%20A%20Step%2Dby%2DStep)
44. **Seamless CICD Pipeline for Node.js Apps with Docker**. [https://moldstud.com](https://moldstud.com/articles/p-building-a-seamless-cicd-pipeline-for-nodejs-applications-using-docker#:~:text=Opt%20for%20node%3Aalpine%20as,size%20under%2010%20MB%2C)
45. **Node.js CI/CD**. [https://www.w3schools.com](https://www.w3schools.com/nodejs/nodejs_ci_cd.asp#:~:text=%2D%20Use%20specific%20version%20tags)
46. **Seamless CICD Pipeline for Node.js Apps with Docker**. [https://moldstud.com](https://moldstud.com/articles/p-building-a-seamless-cicd-pipeline-for-nodejs-applications-using-docker#:~:text=Implement%20a%20multi%2Dstage%20build,Statistics%20indicate%20that%20multi%2Dstage)
47. **Node.js CI/CD**. [https://www.w3schools.com](https://www.w3schools.com/nodejs/nodejs_ci_cd.asp#:~:text=%23%20Install%20production%20dependencies)
48. **Node.js CI/CD**. [https://www.w3schools.com](https://www.w3schools.com/nodejs/nodejs_ci_cd.asp#:~:text=2.%20Install%20%7C%20npm%20ci)
49. **Seamless CICD Pipeline for Node.js Apps with Docker**. [https://moldstud.com](https://moldstud.com/articles/p-building-a-seamless-cicd-pipeline-for-nodejs-applications-using-docker#:~:text=file%2C%20clearly%20defining%20both,essential%20for%20production%20versus)
50. **Seamless CICD Pipeline for Node.js Apps with Docker**. [https://moldstud.com](https://moldstud.com/articles/p-building-a-seamless-cicd-pipeline-for-nodejs-applications-using-docker#:~:text=Lastly%2C%20consider%20using%20a%20non%2Droot%20user%20with)
51. **Node.js CI/CD**. [https://www.w3schools.com](https://www.w3schools.com/nodejs/nodejs_ci_cd.asp#:~:text=%23%20Run%20as%20non%2Droot)
52. **Node.js CI/CD**. [https://www.w3schools.com](https://www.w3schools.com/nodejs/nodejs_ci_cd.asp#:~:text=to%20exclude%20unnecessary%20files,%2D%20Scan%20images%20for)
53. **Seamless CICD Pipeline for Node.js Apps with Docker**. [https://moldstud.com](https://moldstud.com/articles/p-building-a-seamless-cicd-pipeline-for-nodejs-applications-using-docker#:~:text=Implement%20caching%20strategies%20by,commands%20effectively.%20Place%20COPY)
54. **Seamless CICD Pipeline for Node.js Apps with Docker**. [https://moldstud.com](https://moldstud.com/articles/p-building-a-seamless-cicd-pipeline-for-nodejs-applications-using-docker#:~:text=to%20monitor%20the%20application,the%20resilience%20of%20the)
55. **Node.js CI/CD**. [https://www.w3schools.com](https://www.w3schools.com/nodejs/nodejs_ci_cd.asp#:~:text=Docker%20Compose%20for%20Local)
56. **Node.js CI/CD**. [https://www.w3schools.com](https://www.w3schools.com/nodejs/nodejs_ci_cd.asp#:~:text=COPY%20%2D%2Dfrom%3Dbuilder%20/app/dist)
57. **Best Practices for CI/CD Configuration in Node.js**. [https://moldstud.com](https://moldstud.com/articles/p-expert-tips-for-continuous-integration-and-deployment-configuration-in-nodejs#:~:text=%2D%20Utilize%20frameworks%20like,for%20unit%20and%20integration)
58. **Node.js CI/CD**. [https://www.w3schools.com](https://www.w3schools.com/nodejs/nodejs_ci_cd.asp#:~:text=Tip%3A%20For%20most%20Node.js,GitHub%20or%20GitLab%20for)
59. **Best Practices for CI/CD Configuration in Node.js**. [https://moldstud.com](https://moldstud.com/articles/p-expert-tips-for-continuous-integration-and-deployment-configuration-in-nodejs#:~:text=%2D%20Integrate%20Puppeteer%20or,ensure%20everything%20functions%20as)
60. **Best Practices for CI/CD Configuration in Node.js**. [https://moldstud.com](https://moldstud.com/articles/p-expert-tips-for-continuous-integration-and-deployment-configuration-in-nodejs#:~:text=%2D%20Store%20sensitive%20information,Secrets%20Manager%20or%20HashiCorp)
61. **Node.js CI/CD**. [https://www.w3schools.com](https://www.w3schools.com/nodejs/nodejs_ci_cd.asp#:~:text=%2D%20Deployment%20Environments%3A%20Manage,with%20protection%20rules%20and)
62. **Node.js CI/CD**. [https://www.w3schools.com](https://www.w3schools.com/nodejs/nodejs_ci_cd.asp#:~:text=Development%20%E2%86%92%20Testing%20%E2%86%92%20Staging%20%E2%86%92)
63. **Node.js CI/CD**. [https://www.w3schools.com](https://www.w3schools.com/nodejs/nodejs_ci_cd.asp#:~:text=%2D%20Matrix%20Builds%3A%20Test,Node.js%20versions%20and%20operating)
64. **CI/CD best practices in Node.js**. [https://developers.redhat.com](https://developers.redhat.com/articles/2023/11/01/cicd-best-practices-nodejs#:~:text=%2D%20application%20dependencies%20for%20vulnerabilities)
65. **Node.js CI/CD**. [https://www.w3schools.com](https://www.w3schools.com/nodejs/nodejs_ci_cd.asp#:~:text=%2D%20Manual%20approval%20for%20production)
66. **Best Practices for CI/CD Configuration in Node.js**. [https://moldstud.com](https://moldstud.com/articles/p-expert-tips-for-continuous-integration-and-deployment-configuration-in-nodejs#:~:text=%2D%20Implement%20blue%2Dgreen%20deployment,canary%20releases%20to%20reduce)
67. **Seamless CICD Pipeline for Node.js Apps with Docker**. [https://moldstud.com](https://moldstud.com/articles/p-building-a-seamless-cicd-pipeline-for-nodejs-applications-using-docker#:~:text=Incorporate%20a%20monitoring%20solution,up%20to%2040%25.%20Tracking)
68. **Node.js CI/CD**. [https://www.w3schools.com](https://www.w3schools.com/nodejs/nodejs_ci_cd.asp#:~:text=%2D%20MTTR%3A%20Mean%20Time%20To%20Recover%20from)
69. **Best Practices for CI/CD Configuration in Node.js**. [https://moldstud.com](https://moldstud.com/articles/p-expert-tips-for-continuous-integration-and-deployment-configuration-in-nodejs#:~:text=Integrating%20Code%20Quality%20Checks%20and)
70. **Seamless CICD Pipeline for Node.js Apps with Docker**. [https://moldstud.com](https://moldstud.com/articles/p-building-a-seamless-cicd-pipeline-for-nodejs-applications-using-docker#:~:text=Lastly%2C%20engage%20in%20frequent,by%2030%25%2C%20thereby%20accelerating)
71. **Node.js CI/CD**. [https://www.w3schools.com](https://www.w3schools.com/nodejs/nodejs_ci_cd.asp#:~:text=%2D%20name%3A%20Use%20Node.js%20%24%7B%7B%20matrix.node%2Dversion)
72. **Seamless CICD Pipeline for Node.js Apps with Docker**. [https://moldstud.com](https://moldstud.com/articles/p-building-a-seamless-cicd-pipeline-for-nodejs-applications-using-docker#:~:text=Begin%20with%20a%20lightweight%20base%20image%20like)
73. **Seamless CICD Pipeline for Node.js Apps with Docker**. [https://moldstud.com](https://moldstud.com/articles/p-building-a-seamless-cicd-pipeline-for-nodejs-applications-using-docker#:~:text=and%20npm%20run)
74. **Node.js CI/CD**. [https://www.w3schools.com](https://www.w3schools.com/nodejs/nodejs_ci_cd.asp#:~:text=5.%20Build%20%7C%20npm%20run%20build)
75. **Best Practices for CI/CD Configuration in Node.js**. [https://moldstud.com](https://moldstud.com/articles/p-expert-tips-for-continuous-integration-and-deployment-configuration-in-nodejs#:~:text=Implement%20automated%20testing%20at,report%2025%25%20fewer%20production)
76. **Node.js CI/CD**. [https://www.w3schools.com](https://www.w3schools.com/nodejs/nodejs_ci_cd.asp#:~:text=%2D%20Testing%3A%20Automated%20tests%2C%20code%20quality)
77. **CI/CD best practices in Node.js**. [https://developers.redhat.com](https://developers.redhat.com/articles/2023/11/01/cicd-best-practices-nodejs#:~:text=%2D%20Part%203%3A%20Code%20consistency%20in)
78. **Best Practices for CI/CD Configuration in Node.js**. [https://moldstud.com](https://moldstud.com/articles/p-expert-tips-for-continuous-integration-and-deployment-configuration-in-nodejs#:~:text=with%20best%20practices%20in%20security)
79. **Seamless CICD Pipeline for Node.js Apps with Docker**. [https://moldstud.com](https://moldstud.com/articles/p-building-a-seamless-cicd-pipeline-for-nodejs-applications-using-docker#:~:text=Creating%20a%20Dockerfile%20with%20Best)
80. **Node.js CI/CD**. [https://www.w3schools.com](https://www.w3schools.com/nodejs/nodejs_ci_cd.asp#:~:text=CI/CD%20Best%20Practices%20for)
81. **Top Node.js Backend Frameworks in 2025**. [https://flatirons.com](https://flatirons.com/blog/top-nodejs-backend-frameworks-2024/#:~:text=Express.js%20is%20widely%20used,for%20its%20simplicity%20and)
82. **10 Best Node.js Frameworks of 2024**. [https://www.ropstam.com](https://www.ropstam.com/best-node-js-frameworks/#:~:text=%2D%20Real%2Dtime%20Applications%3A%20With,applications%20such%20as%20streaming)
83. **Top 10 Node.js Frameworks To Consider In 2025**. [https://www.mindinventory.com](https://www.mindinventory.com/blog/best-node-js-frameworks/#:~:text=Express.js%20is%20one%20of,apps%20%28with%20Socket.io%29%2C%20middleware%2Drich)
84. **12 Best Node.js Frameworks for App Development in 2024**. [https://www.simform.com](https://www.simform.com/blog/best-nodejs-frameworks/#:~:text=applications%20of%20high%2Dstreaming%2C%20real%2Dtime%20functionalities%20require)
85. **Choosing the right JavaScript API framework - Speakeasy**. [https://www.speakeasy.com](https://www.speakeasy.com/blog/picking-a-javascript-api-framework#:~:text=Fastify%3A%20A%20replacement%20for)
86. **Top 10 Node.js Frameworks To Consider In 2025**. [https://www.mindinventory.com](https://www.mindinventory.com/blog/best-node-js-frameworks/#:~:text=%2D%20Uses%20plugin%2Dbased)
87. **Top Node.js Backend Frameworks in 2025**. [https://flatirons.com](https://flatirons.com/blog/top-nodejs-backend-frameworks-2024/#:~:text=Koa.js%20%7C%20Streamlined%20and,smaller%20core%20for%20customization)
88. **10 Best Node.js Frameworks of 2024**. [https://www.ropstam.com](https://www.ropstam.com/best-node-js-frameworks/#:~:text=%2D%20Building%20RESTful%20APIs%3A,applications%2C%20mobile%20apps%2C%20and)
89. **Top Node.js Backend Frameworks in 2025**. [https://flatirons.com](https://flatirons.com/blog/top-nodejs-backend-frameworks-2024/#:~:text=Some%20I/O%20operations%20handled,building%20server%2Dside%20applications%20and)
90. **10 Best Node.js Frameworks of 2024**. [https://www.ropstam.com](https://www.ropstam.com/best-node-js-frameworks/#:~:text=%2D%20Enterprise%20Applications%3A%20NestJS%E2%80%99s,developing%20large%2Dscale%2C%20complex%20enterprise)
91. **Choosing the right JavaScript API framework - Speakeasy**. [https://www.speakeasy.com](https://www.speakeasy.com/blog/picking-a-javascript-api-framework#:~:text=NestJS%3A%20For%20Robust%20and%20Secure%20Enterprise)
92. **10 Best Node.js Frameworks of 2024**. [https://www.ropstam.com](https://www.ropstam.com/best-node-js-frameworks/#:~:text=%2D%20Real%2Dtime%20Applications%3A%20NestJS%E2%80%99s,collaborative%20tools%2C%20and%20streaming)
93. **Top Node.js Backend Frameworks in 2025**. [https://flatirons.com](https://flatirons.com/blog/top-nodejs-backend-frameworks-2024/#:~:text=Hapi.js%20%7C%20Highly%20customizable%2C,on%20configuration%20over%20convention)
94. **Top Node.js Backend Frameworks in 2025**. [https://flatirons.com](https://flatirons.com/blog/top-nodejs-backend-frameworks-2024/#:~:text=Sails.js%20%7C%20Rapid%20development%2C,support%2C%20extensible%20through%20plugins)
95. **10 Best Node.js Frameworks of 2024**. [https://www.ropstam.com](https://www.ropstam.com/best-node-js-frameworks/#:~:text=%2D%20Real%2DTime%20Applications%3A%20Sails.js,support%20for%20WebSockets%20and)
96. **Node.js Streams vs. Web Streams API**. [https://betterstack.com](https://betterstack.com/community/guides/scaling-nodejs/nodejs-streams-vs-web-streams-api/#:~:text=In%20browsers%2C%20the%20increasing,JavaScript%20data%20handling%20methods.)
97. **Node.js Streams vs. Web Streams API**. [https://betterstack.com](https://betterstack.com/community/guides/scaling-nodejs/nodejs-streams-vs-web-streams-api/#:~:text=Today%2C%20while%20Node.js%20maintains,consistency%20and%20interoperability%20across)
98. **How to stream data over HTTP using Node and Fetch API**. [https://dev.to](https://dev.to/bsorrentino/how-to-stream-data-over-http-using-node-and-fetch-api-4ij2#:~:text=HTTP%20streaming%20is%20a,response%20in%20multiple%20chunks%2C)
99. **How to stream data over HTTP using Node and Fetch API**. [https://dev.to](https://dev.to/bsorrentino/how-to-stream-data-over-http-using-node-and-fetch-api-4ij2#:~:text=the%20async%20generators%20an,is%20ideal%20for%20this)
100. **12 Best Node.js Frameworks for App Development in 2024**. [https://www.simform.com](https://www.simform.com/blog/best-nodejs-frameworks/#:~:text=which%20specializes%20in%20building,with%20over%2040%2C000%20Github)
