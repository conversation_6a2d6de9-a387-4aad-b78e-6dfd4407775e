# Based on these best practices and the attached README.md Create me detailed implementation plan for creating QRMCPNode project that implements QR Code MCP Server on NodeJS

# 🚀 QRMCPNode Implementation Plan - QR Code MCP Server for Node.js

> **Executive Summary:**
> A comprehensive implementation plan for building a production-ready QR Code MCP Server using Node.js, featuring real-time streaming, multiple input formats, and full MCP protocol compliance.

---

## 📋 Table of Contents

1. [Project Overview & Goals](#project-overview--goals)
2. [Technology Stack & Architecture](#technology-stack--architecture)
3. [Project Structure](#project-structure)
4. 1: Foundation & Setup](#phase-1-foundation--setup)
5. 2: Core MCP Implementation](#phase-2-core-mcp-implementation)
6. [Phase 3: QR Code Processing](#phase-3-qr-code-processing)
7. [Phase 4: Streaming & Real-time Features](#phase-4-streaming--real-time-features)
8. [Phase 5: Testing & Quality Assurance](#phase-5-testing--quality-assurance)
9. 6: Containerization & Deployment](#phase-6-containerization--deployment)
10. [Implementation Timeline](#implementation-timeline)

---

## 📎 Project Overview & Goals

### Primary Objectives

| Objective | Description | Success Criteria |
|-----------|-------------|------------------|
| **MCP Compliance** | Full Model Context Protocol support | All MCP operations functional [[1]](file://README.md) |
| **QR Detection** | Accurate QR code detection/decoding | 95%+ detection accuracy [[1]](file://README.md) |
| **Real-time Streaming** | Live QR monitoring via SSE | <100ms latency [[1]](file://README.md) |
| **Multi-format Support** | Base64, file path, data URI | All formats working [[1]](file://README.md) |
| **Production Ready** | Docker, monitoring, security | Deployment-ready [[1]](file://README.md) |

### Key Features to Implement

```mermaid
graph TB
    A[QRMCPNode Server] --> B[Express.js HTTP Server]
    A --> C[MCP Protocol Handler]
    A --> D[QR Code Processor]
    A --> E[Streaming Engine]
    A --> F[Health Monitor]
    
    B --> G[API Endpoints]
    B --> H[CORS Support]
    
    C --> I[Tools Registry]
    C --> J[Resources]
    C --> K[Prompts]
    
    D --> L[jsQR Decoder]
    D --> M[Image Processing]
    
    E --> N[SSE Stream]
    E --> O[WebSocket Option]
```

---

## 🏗️ Technology Stack & Architecture

### Core Dependencies

```json
{
  "dependencies": {
    "@modelcontextprotocol/sdk": "^1.0.0",
    "express": "^4.19.0",
    "cors": "^2.8.5",
    "zod": "^3.23.0",
    "jsqr": "^1.4.0",
    "jimp": "^0.22.12",
    "pino": "^9.0.0",
    "express-rate-limit": "^7.2.0",
    "helmet": "^7.1.0"
  },
  "devDependencies": {
    "typescript": "^5.4.0",
    "@types/node": "^20.12.0",
    "@types/express": "^4.17.21",
    "tsx": "^4.7.0",
    "vitest": "^1.5.0",
    "@orangeopensource/hurl": "^4.3.0",
    "nodemon": "^3.1.0"
  }
}
```

### Architecture Diagram

```
┌─────────────────────────────────────────────────────────────┐
│                     QRMCPNode Server                        │
├─────────────────────────────────────────────────────────────┤
│  Presentation Layer                                         │
│  ┌─────────────┐  ┌──────────────┐  ┌─────────────────┐   │
│  │   Express   │  │ Swagger Docs │  │   CORS Config   │   │
│  └─────────────┘  └──────────────┘  └─────────────────┘   │
├─────────────────────────────────────────────────────────────┤
│  MCP Protocol Layer                                         │
│  ┌─────────────┐  ┌──────────────┐  ┌─────────────────┐   │
│  │  MCP Server │  │ HTTP Transport│  │   SSE Handler   │   │
│  └─────────────┘  └──────────────┘  └─────────────────┘   │
├─────────────────────────────────────────────────────────────┤
│  Business Logic Layer                                       │
│  ┌─────────────┐  ┌──────────────┐  ┌─────────────────┐   │
│  │ QR Processor│  │ Image Handler │  │ Stream Manager  │   │
│  └─────────────┘  └──────────────┘  └─────────────────┘   │
├─────────────────────────────────────────────────────────────┤
│  Data Layer                                                 │
│  ┌─────────────┐  ┌──────────────┐  ┌─────────────────┐   │
│  │ File System │  │   Cache      │  │   Monitoring    │   │
│  └─────────────┘  └──────────────┘  └─────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

---

## 📁 Project Structure

```
qrmcp-node/
├── src/
│   ├── server.ts                 # Main server entry point
│   ├── app.ts                    # Express app configuration
│   ├── types/
│   │   ├── index.ts              # Type definitions
│   │   └── mcp.types.ts          # MCP-specific types
│   ├── mcp/
│   │   ├── server.ts             # MCP server instance
│   │   ├── tools/                # MCP tools
│   │   │   ├── scan-qr.tool.ts
│   │   │   ├── stream-qr.tool.ts
│   │   │   └── index.ts
│   │   ├── resources/            # MCP resources
│   │   │   └── qr-history.resource.ts
│   │   └── transport/
│   │       └── http.transport.ts
│   ├── processors/
│   │   ├── qr-decoder.ts         # QR code detection logic
│   │   ├── image-processor.ts    # Image preprocessing
│   │   └── stream-processor.ts   # Streaming handler
│   ├── middleware/
│   │   ├── auth.middleware.ts    # Authentication (optional)
│   │   ├── error.middleware.ts   # Error handling
│   │   ├── validation.middleware.ts
│   │   └── rate-limit.middleware.ts
│   ├── schemas/
│   │   ├── qr.schema.ts          # Zod schemas for QR operations
│   │   └── common.schema.ts
│   ├── routes/
│   │   ├── health.routes.ts      # Health check endpoints
│   │   ├── mcp.routes.ts         # MCP protocol routes
│   │   └── api.routes.ts         # REST API routes
│   ├── utils/
│   │   ├── logger.ts             # Pino logger configuration
│   │   ├── config.ts             # Environment configuration
│   │   └── helpers.ts
│   └── services/
│       ├── qr.service.ts         # QR code business logic
│       └── stream.service.ts     # Streaming service
├── test/
│   ├── unit/                     # Unit tests
│   ├── integration/              # Integration tests
│   │   └── *.hurl                # Hurl test files
│   └── e2e/                      # End-to-end tests
├── docker/
│   ├── Dockerfile
│   ├── docker-compose.yml
│   └── .dockerignore
├── docs/
│   ├── API.md
│   ├── MCP.md
│   └── DEPLOYMENT.md
├── scripts/
│   ├── build.sh
│   ├── test.sh
│   └── deploy.sh
├── .github/
│   └── workflows/
│       ├── ci.yml
│       └── deploy.yml
├── package.json
├── tsconfig.json
├── .env.example
├── .gitignore
└── README.md
```

---

## 🚀 Phase 1: Foundation & Setup

### 1.1 Project Initialization

```bash
# Initialize project
mkdir qrmcp-node && cd qrmcp-node
npm init -y

# Configure TypeScript
npx tsc --init
```

### 1.2 TypeScript Configuration

```typescript
// tsconfig.json
{
  "compilerOptions": {
    "target": "ES2022",
    "module": "NodeNext",
    "lib": ["ES2022"],
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "moduleResolution": "NodeNext",
    "types": ["node"],
    "sourceMap": true
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist", "test"]
}
```

### 1.3 Environment Configuration

```typescript
// src/utils/config.ts
import { z } from 'zod';
import dotenv from 'dotenv';

dotenv.config();

const ConfigSchema = z.object({
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.string().transform(Number).default('3000'),
  HOST: z.string().default('0.0.0.0'),
  LOG_LEVEL: z.enum(['trace', 'debug', 'info', 'warn', 'error']).default('info'),
  CORS_ORIGIN: z.string().default('*'),
  MAX_FILE_SIZE: z.string().transform(Number).default('10485760'), // 10MB
  STREAM_INTERVAL: z.string().transform(Number).default('100'), // ms
  AUTH_ENABLED: z.string().transform(v => v === 'true').default('false'),
  AUTH_TOKEN: z.string().optional(),
});

export const config = ConfigSchema.parse(process.env);
```

### 1.4 Logger Setup

```typescript
// src/utils/logger.ts
import pino from 'pino';
import { config } from './config';

export const logger = pino({
  level: config.LOG_LEVEL,
  transport: config.NODE_ENV === 'development' ? {
    target: 'pino-pretty',
    options: {
      colorize: true,
      translateTime: 'SYS:standard',
      ignore: 'pid,hostname'
    }
  } : undefined,
  serializers: {
    error: pino.stdSerializers.err,
    req: pino.stdSerializers.req,
    res: pino.stdSerializers.res
  }
});
```

---

## 🔧 Phase 2: Core MCP Implementation

### 2.1 MCP Server Setup

```typescript
// src/mcp/server.ts
import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { HttpServerTransport } from '@modelcontextprotocol/sdk/server/http.js';
import { logger } from '../utils/logger';
import { registerTools } from './tools';
import { registerResources } from './resources';

export class QRMCPServer {
  private mcpServer: McpServer;
  private transport: HttpServerTransport;

  constructor() {
    this.mcpServer = new McpServer({
      name: 'qrmcp-node',
      version: '1.0.0',
      description: 'QR Code MCP Server for Node.js'
    });
    
    this.transport = new HttpServerTransport();
    this.initialize();
  }

  private initialize() {
    // Register tools
    registerTools(this.mcpServer);
    
    // Register resources
    registerResources(this.mcpServer);
    
    // Set up error handling
    this.mcpServer.on('error', (error) => {
      logger.error({ error }, 'MCP server error');
    });
    
    logger.info('MCP server initialized');
  }

  async connect() {
    await this.mcpServer.connect(this.transport);
    logger.info('MCP server connected');
  }

  getTransport() {
    return this.transport;
  }
}
```

### 2.2 MCP Tools Implementation

```typescript
// src/mcp/tools/scan-qr.tool.ts
import { z } from 'zod';
import { QRService } from '../../services/qr.service';
import { logger } from '../../utils/logger';

export const ScanQRInputSchema = z.object({
  input: z.string().describe('Base64, file path, or data URI of image'),
  format: z.enum(['base64', 'filepath', 'datauri']).optional(),
  options: z.object({
    inversionAttempts: z.enum(['dontInvert', 'onlyInvert', 'attemptBoth']).optional(),
    greyScaleWeights: z.object({
      red: z.number().optional(),
      green: z.number().optional(),
      blue: z.number().optional()
    }).optional()
  }).optional()
});

export function createScanQRTool() {
  const qrService = new QRService();

  return {
    name: 'scan-qr',
    description: 'Scan QR code from image',
    inputSchema: ScanQRInputSchema,
    handler: async (args: z.infer<typeof ScanQRInputSchema>) => {
      try {
        logger.info({ args }, 'Scanning QR code');
        
        const result = await qrService.scanQRCode(
          args.input,
          args.format || 'base64',
          args.options
        );
        
        if (!result) {
          return {
            content: [{
              type: 'text',
              text: JSON.stringify({
                success: false,
                error: 'No QR code found in image'
              })
            }]
          };
        }
        
        return {
          content: [{
            type: 'text',
            text: JSON.stringify({
              success: true,
              data: result.data,
              location: result.location,
              version: result.version
            }, null, 2)
          }]
        };
      } catch (error) {
        logger.error({ error }, 'Error scanning QR code');
        throw error;
      }
    }
  };
}
```

### 2.3 Express Server Integration

```typescript
// src/app.ts
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { pinoHttp } from 'pino-http';
import { config } from './utils/config';
import { logger } from './utils/logger';
import { errorHandler } from './middleware/error.middleware';
import { rateLimiter } from './middleware/rate-limit.middleware';
import { healthRoutes } from './routes/health.routes';
import { mcpRoutes } from './routes/mcp.routes';
import { apiRoutes } from './routes/api.routes';

export function createApp() {
  const app = express();

  // Security middleware
  app.use(helmet());
  app.use(cors({
    origin: config.CORS_ORIGIN,
    credentials: true
  }));

  // Request parsing
  app.use(express.json({ limit: '10mb' }));
  app.use(express.urlencoded({ extended: true }));

  // Logging
  app.use(pinoHttp({ logger }));

  // Rate limiting
  app.use(rateLimiter);

  // Routes
  app.use('/health', healthRoutes);
  app.use('/mcp', mcpRoutes);
  app.use('/api', apiRoutes);

  // Error handling
  app.use(errorHandler);

  return app;
}
```

---

## 🎯 Phase 3: QR Code Processing

### 3.1 QR Decoder Implementation

```typescript
// src/processors/qr-decoder.ts
import jsQR from 'jsqr';
import Jimp from 'jimp';
import { logger } from '../utils/logger';

export interface QRCodeResult {
  data: string;
  location: {
    topLeft: { x: number; y: number };
    topRight: { x: number; y: number };
    bottomLeft: { x: number; y: number };
    bottomRight: { x: number; y: number };
  };
  version: number;
}

export class QRDecoder {
  async decodeFromBuffer(buffer: Buffer): Promise<QRCodeResult | null> {
    try {
      const image = await Jimp.read(buffer);
      const { width, height, data } = image.bitmap;
      
      const imageData = new Uint8ClampedArray(data);
      const code = jsQR(imageData, width, height);
      
      if (!code) {
        logger.debug('No QR code found in image');
        return null;
      }
      
      return {
        data: code.data,
        location: code.location,
        version: code.version
      };
    } catch (error) {
      logger.error({ error }, 'Error decoding QR code');
      throw error;
    }
  }

  async decodeFromBase64(base64: string): Promise<QRCodeResult | null> {
    const buffer = Buffer.from(base64, 'base64');
    return this.decodeFromBuffer(buffer);
  }

  async decodeFromPath(path: string): Promise<QRCodeResult | null> {
    const image = await Jimp.read(path);
    const buffer = await image.getBufferAsync(Jimp.MIME_PNG);
    return this.decodeFromBuffer(buffer);
  }

  async decodeFromDataUri(dataUri: string): Promise<QRCodeResult | null> {
    const base64 = dataUri.split(',')[1];
    return this.decodeFromBase64(base64);
  }
}
```

### 3.2 Image Processing Service

```typescript
// src/processors/image-processor.ts
import Jimp from 'jimp';
import { logger } from '../utils/logger';

export interface ProcessingOptions {
  resize?: { width: number; height: number };
  grayscale?: boolean;
  contrast?: number;
  brightness?: number;
  normalize?: boolean;
}

export class ImageProcessor {
  async preprocessImage(
    buffer: Buffer, 
    options: ProcessingOptions = {}
  ): Promise<Buffer> {
    try {
      let image = await Jimp.read(buffer);
      
      // Apply preprocessing
      if (options.resize) {
        image = image.resize(options.resize.width, options.resize.height);
      }
      
      if (options.grayscale) {
        image = image.grayscale();
      }
      
      if (options.contrast !== undefined) {
        image = image.contrast(options.contrast);
      }
      
      if (options.brightness !== undefined) {
        image = image.brightness(options.brightness);
      }
      
      if (options.normalize) {
        image = image.normalize();
      }
      
      return await image.getBufferAsync(Jimp.MIME_PNG);
    } catch (error) {
      logger.error({ error }, 'Error preprocessing image');
      throw error;
    }
  }

  async extractFramesFromGif(buffer: Buffer): Promise<Buffer[]> {
    // Implementation for GIF frame extraction
    // This would use a library like gif-frames
    return [];
  }
}
```

---

## 🌊 Phase 4: Streaming & Real-time Features

### 4.1 SSE Stream Handler

```typescript
// src/processors/stream-processor.ts
import { Response } from 'express';
import { EventEmitter } from 'events';
import { logger } from '../utils/logger';
import { QRDecoder } from './qr-decoder';

export interface StreamOptions {
  interval: number;
  maxDuration?: number;
  format: 'json' | 'text';
}

export class StreamProcessor extends EventEmitter {
  private decoder: QRDecoder;
  private activeStreams: Map<string, NodeJS.Timeout>;

  constructor() {
    super();
    this.decoder = new QRDecoder();
    this.activeStreams = new Map();
  }

  startSSEStream(
    res: Response, 
    streamId: string,
    options: StreamOptions
  ): void {
    // Set SSE headers
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('X-Accel-Buffering', 'no');

    // Send initial connection event
    res.write('event: connected\n');
    res.write(`data: ${JSON.stringify({ streamId, status: 'connected' })}\n\n`);

    // Set up heartbeat
    const heartbeat = setInterval(() => {
      res.write(':heartbeat\n\n');
    }, 30000);

    // Handle client disconnect
    res.on('close', () => {
      this.stopStream(streamId);
      clearInterval(heartbeat);
      logger.info({ streamId }, 'SSE stream closed');
    });

    // Store stream reference
    this.activeStreams.set(streamId, heartbeat);
    logger.info({ streamId }, 'SSE stream started');
  }

  async processImageStream(
    streamId: string,
    imageBuffer: Buffer,
    res: Response
  ): Promise<void> {
    try {
      const result = await this.decoder.decodeFromBuffer(imageBuffer);
      
      if (result) {
        const event = {
          type: 'qr-detected',
          timestamp: new Date().toISOString(),
          data: result
        };
        
        res.write(`event: qr-detected\n`);
        res.write(`data: ${JSON.stringify(event)}\n\n`);
        
        this.emit('qr-detected', { streamId, result });
      } else {
        res.write(`event: no-qr\n`);
        res.write(`data: ${JSON.stringify({ 
          type: 'no-qr',
          timestamp: new Date().toISOString() 
        })}\n\n`);
      }
    } catch (error) {
      logger.error({ error, streamId }, 'Error processing stream image');
      
      res.write(`event: error\n`);
      res.write(`data: ${JSON.stringify({ 
        type: 'error',
        error: error.message 
      })}\n\n`);
    }
  }

  stopStream(streamId: string): void {
    const heartbeat = this.activeStreams.get(streamId);
    if (heartbeat) {
      clearInterval(heartbeat);
      this.activeStreams.delete(streamId);
      this.emit('stream-stopped', { streamId });
    }
  }

  getActiveStreams(): string[] {
    return Array.from(this.activeStreams.keys());
  }
}
```

### 4.2 Streaming MCP Tool

```typescript
// src/mcp/tools/stream-qr.tool.ts
import { z } from 'zod';
import { StreamService } from '../../services/stream.service';

export const StreamQRInputSchema = z.object({
  source: z.enum(['camera', 'file', 'url']),
  sourceData: z.string().optional(),
  options: z.object({
    interval: z.number().default(100),
    duration: z.number().optional(),
    format: z.enum(['json', 'text']).default('json')
  }).optional()
});

export function createStreamQRTool() {
  const streamService = new StreamService();

  return {
    name: 'stream-qr',
    description: 'Start streaming QR code detection',
    inputSchema: StreamQRInputSchema,
    handler: async function* (args: z.infer<typeof StreamQRInputSchema>) {
      const stream = await streamService.createStream(args);
      
      for await (const result of stream) {
        yield {
          content: [{
            type: 'text',
            text: JSON.stringify(result)
          }]
        };
      }
    }
  };
}
```

---

## 🧪 Phase 5: Testing & Quality Assurance

### 5.1 Unit Test Example

```typescript
// test/unit/qr-decoder.test.ts
import { describe, it, expect, beforeEach } from 'vitest';
import { QRDecoder } from '../../src/processors/qr-decoder';
import fs from 'fs/promises';
import path from 'path';

describe('QRDecoder', () => {
  let decoder: QRDecoder;
  
  beforeEach(() => {
    decoder = new QRDecoder();
  });
  
  it('should decode QR code from buffer', async () => {
    const buffer = await fs.readFile(
      path.join(__dirname, '../fixtures/qr-test.png')
    );
    
    const result = await decoder.decodeFromBuffer(buffer);
    
    expect(result).toBeDefined();
    expect(result?.data).toBe('https://example.com');
    expect(result?.location).toHaveProperty('topLeft');
  });
  
  it('should return null for non-QR images', async () => {
    const buffer = await fs.readFile(
      path.join(__dirname, '../fixtures/no-qr.png')
    );
    
    const result = await decoder.decodeFromBuffer(buffer);
    
    expect(result).toBeNull();
  });
});
```

### 5.2 Integration Test with Hurl

```hurl
# test/integration/qr-scan.hurl

# Test: Health Check
GET http://localhost:3000/health
HTTP 200
[Asserts]
jsonpath "$.status" == "healthy"
jsonpath "$.version" exists

# Test: Scan QR Code Tool
POST http://localhost:3000/mcp
Content-Type: application/json
```json
{
  "jsonrpc": "2.0",
  "method": "tools/invoke",
  "params": {
    "name": "scan-qr",
    "arguments": {
      "input": "data:image/png;base64,iVBORw0KGgoAAAANS...",
      "format": "datauri"
    }
  },
  "id": 1
}
```
HTTP 200
[Asserts]
jsonpath "$.result.content[0].text" contains "success"
jsonpath "$.result.content[0].text" contains "data"

# Test: Stream QR with SSE
GET http://localhost:3000/mcp/stream
Accept: text/event-stream
HTTP 200
[Asserts]
header "Content-Type" == "text/event-stream"
body contains "event:"

# Test: Invalid Input
POST http://localhost:3000/mcp
Content-Type: application/json
```json
{
  "jsonrpc": "2.0",
  "method": "tools/invoke",
  "params": {
    "name": "scan-qr",
    "arguments": {
      "input": "invalid-base64"
    }
  },
  "id": 2
}
```
HTTP 200
[Asserts]
jsonpath "$.error" exists
```

### 5.3 Test Scripts Configuration

```json
// package.json scripts
{
  "scripts": {
    "test": "vitest",
    "test:unit": "vitest run --dir test/unit",
    "test:integration": "hurl --test --glob 'test/integration/**/*.hurl'",
    "test:e2e": "vitest run --dir test/e2e",
    "test:coverage": "vitest run --coverage",
    "test:watch": "vitest watch"
  }
}
```

---

## 🐳 Phase 6: Containerization & Deployment

### 6.1 Multi-stage Dockerfile

```dockerfile
# Build stage
FROM node:20-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci

# Copy source
COPY tsconfig.json ./
COPY src ./src

# Build TypeScript
RUN npm run build

# Production stage
FROM node:20-alpine

# Install required system dependencies for image processing
RUN apk add --no-cache \
    cairo-dev \
    jpeg-dev \
    pango-dev \
    giflib-dev

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

# Copy built application
COPY --from=builder /app/dist ./dist

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001
USER nodejs

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:3000/health', (r) => {process.exit(r.statusCode === 200 ? 0 : 1)})"

EXPOSE 3000

CMD ["node", "dist/server.js"]
```

### 6.2 Docker Compose

```yaml
# docker-compose.yml
version: '3.8'

services:
  qrmcp-node:
    build:
      context: .
      dockerfile: docker/Dockerfile
    ports:
      - "3000:3000"
    environment:
      NODE_ENV: production
      LOG_LEVEL: info
      CORS_ORIGIN: "*"
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - qrmcp-network
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M

  redis:
    image: redis:7-alpine
    networks:
      - qrmcp-network
    volumes:
      - redis-data:/data

networks:
  qrmcp-network:
    driver: bridge

volumes:
  redis-data:
```

### 6.3 CI/CD Pipeline

```yaml
# .github/workflows/ci.yml
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18.x, 20.x]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run linter
      run: npm run lint
    
    - name: Run tests
      run: npm test
    
    - name: Run integration tests
      run: |
        npm run build
        npm start &
        sleep 5
        npm run test:integration
    
    - name: Upload coverage
      uses: codecov/codecov-action@v4
      with:
        token: ${{ secrets.CODECOV_TOKEN }}

  build:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Login to Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_TOKEN }}
    
    - name: Build and push
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./docker/Dockerfile
        push: true
        tags: |
          yourusername/qrmcp-node:latest
          yourusername/qrmcp-node:${{ github.sha }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
```

---

## 📅 Implementation Timeline

### Development Schedule

| Phase | Duration | Tasks | Deliverables |
|-------|----------|-------|--------------|
| **Phase 1** | 1 week | Foundation setup, TypeScript config, basic structure | Working Express server |
| **Phase 2** | 2 weeks | MCP implementation, tool registry, protocol handling | MCP-compliant server |
| **Phase 3** | 1 week | QR code processing, image handling | QR detection working |
| **Phase 4** | 2 weeks | Streaming implementation, SSE setup | Real-time monitoring |
| **Phase 5** | 1 week | Testing suite, integration tests | 80%+ test coverage |
| **Phase 6** | 1 week | Docker, CI/CD, deployment | Production-ready app |

### Milestones

```mermaid
gantt
    title QRMCPNode Development Timeline
    dateFormat YYYY-MM-DD
    section Foundation
    Project Setup           :2025-01-01, 3d
    TypeScript Config      :3d
    Basic Server          :2d
    section MCP Core
    MCP Server            :2025-01-08, 5d
    Tool Implementation   :5d
    Protocol Handling     :4d
    section QR Processing
    QR Decoder            :2025-01-22, 3d
    Image Processing      :4d
    section Streaming
    SSE Implementation    :2025-01-29, 5d
    Stream Tools          :5d
    Testing              :4d
    section Production
    Docker Setup          :2025-02-12, 3d
    CI/CD Pipeline       :2d
    Documentation        :2d
```

---

## 🎯 Key Implementation Guidelines

### Architecture Principles

> **Design Principles:**
> - ✅ **Modular Architecture**: Separate concerns into distinct modules
> - ✅ **TypeScript First**: Strong typing throughout the codebase
> - ✅ **Error Resilience**: Comprehensive error handling at all levels
> - ✅ **Performance Focus**: Optimize for concurrent QR processing
> - ✅ **Security by Default**: Input validation, rate limiting, CORS

### Code Quality Standards

```typescript
// Example of quality standards
interface QualityStandards {
  // All async functions must have error handling
  asyncPattern: 'try-catch with logging';
  
  // All inputs must be validated
  validation: 'Zod schemas for all external inputs';
  
  // All services must be testable
  testing: 'Dependency injection, mocking support';
  
  // All endpoints must be documented
  documentation: 'JSDoc + OpenAPI annotations';
}
```

### Performance Optimization

| Area | Strategy | Implementation |
|------|----------|----------------|
| **Image Processing** | Worker threads | Offload CPU-intensive tasks |
| **Streaming** | Chunked responses | Process images in chunks |
| **Caching** | Redis integration | Cache decoded QR results |
| **Concurrency** | Queue management | Limit concurrent operations |

---

## 📚 Additional Resources

### Documentation Templates

```markdown
# API Documentation Template

## Endpoint: POST /mcp

### Description
Main MCP protocol endpoint supporting all MCP operations.

### Request
```json
{
  "jsonrpc": "2.0",
  "method": "tools/invoke",
  "params": {
    "name": "scan-qr",
    "arguments": {
      "input": "base64_string",
      "format": "base64"
    }
  },
  "id": 1
}
```

### Response
```json
{
  "jsonrpc": "2.0",
  "result": {
    "content": [{
      "type": "text",
      "text": "{\"success\": true, \"data\": \"QR content\"}"
    }]
  },
  "id": 1
}
```
```

### Development Commands

```bash
# Development workflow
npm run dev          # Start development server with hot reload
npm run build        # Build TypeScript to JavaScript
npm run lint         # Run ESLint
npm run format       # Format code with Prettier
npm test            # Run all tests
npm run test:watch  # Run tests in watch mode

# Docker workflow
docker build -t qrmcp-node .
docker run -p 3000:3000 qrmcp-node
docker-compose up -d

# Production deployment
npm run build
npm run start:prod
```

---

## 🏁 Success Criteria

> **Project Completion Checklist:**
> 
> - [ ] **MCP Compliance**: All MCP operations functional
> - [ ] **QR Detection**: 95%+ accuracy on standard QR codes  
> - [ ] **Streaming**: Real-time SSE streaming working
> - [ ] **Performance**: <100ms response time for single QR scan
> - [ ] **Testing**: 80%+ code coverage
> - [ ] **Documentation**: Complete API docs and README
> - [ ] **Docker**: Container builds and runs successfully
> - [ ] **CI/CD**: Automated testing and deployment pipeline
> - [ ] **Security**: Input validation, rate limiting, CORS configured
> - [ ] **Monitoring**: Health checks and logging implemented

---

**Document Version**: 1.0.0  
**Last Updated**: September 2025  
**Project Lead**: Your Development Team


### References

1. **Specification**. [https://modelcontextprotocol.io](https://modelcontextprotocol.io/specification/2025-06-18#:~:text=%28MCP%29%20is%20an%20open,a%20chat%20interface%2C%20or)
2. **Specification**. [https://modelcontextprotocol.io](https://modelcontextprotocol.io/specification/2025-06-18#:~:text=%3A%20Templated%20messages%20and%20workflows%20for)
3. **How to MCP - The Complete Guide to Understanding ...**. [https://simplescraper.io](https://simplescraper.io/blog/how-to-mcp#:~:text=%2D%20Streamable%20HTTP%20%282025%2D03%2D26%29%20%2D%20The%20modern)
4. **Specification**. [https://modelcontextprotocol.io](https://modelcontextprotocol.io/specification/2025-06-18#:~:text=Provide%20clear%20documentation%20of%20security%20implications)
5. **How to MCP - The Complete Guide to Understanding ...**. [https://simplescraper.io](https://simplescraper.io/blog/how-to-mcp#:~:text=%2D%20OAuth%20implementation%3A%20Set,Firebase%20and%20MCP%27s%20required)
6. **Specification and documentation for the Model Context ...**. [https://github.com](https://github.com/modelcontextprotocol/modelcontextprotocol#:~:text=The%20schema%20is%20defined,as%20well%2C%20for%20wider)
7. **MCP Server in Node.js**. [https://github.com](https://github.com/lucianoayres/mcp-server-node#:~:text=and%20returns%20it%20as)
8. **Build a TypeScript MCP server using Azure Container Apps**. [https://learn.microsoft.com](https://learn.microsoft.com/en-us/azure/developer/ai/build-mcp-server-ts#:~:text=file%20is%20the%20main,for%20Server%2DSent%20Events%20%28SSE%29)
9. **MCP Server in Node.js**. [https://github.com](https://github.com/lucianoayres/mcp-server-node#:~:text=%2D%20Input%20Validation%3A%20Uses%20Zod%20for%20schema)
10. **Build a TypeScript MCP server using Azure Container Apps**. [https://learn.microsoft.com](https://learn.microsoft.com/en-us/azure/developer/ai/build-mcp-server-ts#:~:text=%2D%20Two%2Dendpoint%20pattern%3A%20GET,connection%2C%20POST%20for%20sending)
11. **Build a TypeScript MCP server using Azure Container Apps**. [https://learn.microsoft.com](https://learn.microsoft.com/en-us/azure/developer/ai/build-mcp-server-ts#:~:text=class%20to%20manage%20individual,multiple%20transports%20and%20their)
12. **How to MCP - The Complete Guide to Understanding ...**. [https://simplescraper.io](https://simplescraper.io/blog/how-to-mcp#:~:text=Tool%20Authorization%20and%20User%2DSpecific%20Access)
13. **MCP Server in Node.js**. [https://github.com](https://github.com/lucianoayres/mcp-server-node#:~:text=Overview%20%C2%B7%20Features%20%C2%B7,Tool%20in%20Cursor%20%28Agent)
14. **Build a TypeScript MCP server using Azure Container Apps**. [https://learn.microsoft.com](https://learn.microsoft.com/en-us/azure/developer/ai/build-mcp-server-ts#:~:text=The%20sample%20repository%20contains,MCP%20server%20Azure%20deployment)
15. **MCP Server in Node.js**. [https://github.com](https://github.com/lucianoayres/mcp-server-node#:~:text=npx%20%40modelcontextprotocol/inspector%20node%20./mcp%2Dserver.js)
16. **Build a TypeScript MCP server using Azure Container Apps**. [https://learn.microsoft.com](https://learn.microsoft.com/en-us/azure/developer/ai/build-mcp-server-ts#:~:text=Explore%20the%20TypeScript%20remote,deploy%20it%20to%20Azure)
17. **MCP Node.js Implementation Guide 2025**. [https://www.byteplus.com](https://www.byteplus.com/en/topic/541240#:~:text=Learn%20how%20to%20implement,and%20best%20practices%20for)
18. **Specification**. [https://modelcontextprotocol.io](https://modelcontextprotocol.io/specification/2025-06-18#:~:text=Build%20robust%20consent%20and,authorization%20flows%20into%20their)
19. **Specification and documentation for the Model Context ...**. [https://github.com](https://github.com/modelcontextprotocol/modelcontextprotocol#:~:text=The%20official%20MCP%20documentation,Mintlify%20and%20available%20at)
20. **How to MCP - The Complete Guide to Understanding ...**. [https://simplescraper.io](https://simplescraper.io/blog/how-to-mcp#:~:text=tool%20and%20automatically%20adds%20the%20parameter%20host%3A)
21. **WebSockets vs Server-Sent Events: Key differences and ...**. [https://ably.com](https://ably.com/blog/websockets-vs-sse#:~:text=%2D%20Bi%2Ddirectional%20communication%20in,messages%20at%20the%20same)
22. **SSE vs WebSockets - Communication Protocols**. [https://softwaremill.com](https://softwaremill.com/sse-vs-websockets-comparing-real-time-communication-protocols/#:~:text=Nevertheless%2C%20in%20such%20a,we%20need%20to%20have)
23. **WebSockets vs Server-Sent Events: Key differences and ...**. [https://ably.com](https://ably.com/blog/websockets-vs-sse#:~:text=%2D%20Uses%20a%20custom,a%20lower%20level%20than)
24. **SSE vs WebSockets - Communication Protocols**. [https://softwaremill.com](https://softwaremill.com/sse-vs-websockets-comparing-real-time-communication-protocols/#:~:text=In%20short%2C%20it%20is,to%20this%20feature%2C%20we)
25. **SSE vs WebSockets - Communication Protocols**. [https://softwaremill.com](https://softwaremill.com/sse-vs-websockets-comparing-real-time-communication-protocols/#:~:text=%2D%20SSE%20fully%20relies,for%20both%20HTTP/1.1%20and)
26. **WebSockets vs Server-Sent Events: Key differences and ...**. [https://ably.com](https://ably.com/blog/websockets-vs-sse#:~:text=Server%2DSent%20Events%20%28SSE%29%20are,receiving%20updates%20whenever%20a)
27. **SSE vs WebSockets - Communication Protocols**. [https://softwaremill.com](https://softwaremill.com/sse-vs-websockets-comparing-real-time-communication-protocols/#:~:text=According%20to%20the%20specification%2C,their%20unique%20MIME%20type%3A)
28. **WebSockets vs Server-Sent Events: Key differences and ...**. [https://ably.com](https://ably.com/blog/websockets-vs-sse#:~:text=include%20Socket.IO%20and)
29. **WebSockets vs Server-Sent Events: Key differences and ...**. [https://ably.com](https://ably.com/blog/websockets-vs-sse#:~:text=and%20overhead%2C%20as%20fewer,chat%20and%20live%20data)
30. **Server-Sent Events vs WebSockets – How to Choose a ...**. [https://www.freecodecamp.org](https://www.freecodecamp.org/news/server-sent-events-vs-websockets/#:~:text=customer%20support%20chat%20systems%2C,providing%20seamless%20and%20efficient)
31. **Streaming in Next.js 15: WebSockets vs Server-Sent Events**. [https://hackernoon.com](https://hackernoon.com/streaming-in-nextjs-15-websockets-vs-server-sent-events#:~:text=web%20servers%20and%20firewalls.,as%20JSON%20or%20plain)
32. **Performance difference between websocket and server ...**. [https://stackoverflow.com](https://stackoverflow.com/questions/63583989/performance-difference-between-websocket-and-server-sent-events-sse-for-chat-r#:~:text=To%20me%2C%20Looking%20on,to%2020%2C000%2C%20100%2C000.%20There)
33. **SSE vs WebSockets - Communication Protocols**. [https://softwaremill.com](https://softwaremill.com/sse-vs-websockets-comparing-real-time-communication-protocols/#:~:text=%2D%20SSE%20provides%20only,the%20server%20to%20the)
34. **WebSockets vs Server-Sent Events: Key differences and ...**. [https://ably.com](https://ably.com/blog/websockets-vs-sse#:~:text=%2D%20Data%20format%20flexibility%3A,such%20as%20images%20and)
35. **SSE vs WebSockets - Communication Protocols**. [https://softwaremill.com](https://softwaremill.com/sse-vs-websockets-comparing-real-time-communication-protocols/#:~:text=not%20have%20to%20constantly,binary%20data%20or%20Unicode)
36. **WebSockets vs Server-Sent Events: Key differences and ...**. [https://ably.com](https://ably.com/blog/websockets-vs-sse#:~:text=like%20Socket.IO%2C%20which%20have%20such%20fallbacks)
37. **SSE vs WebSockets - Communication Protocols**. [https://softwaremill.com](https://softwaremill.com/sse-vs-websockets-comparing-real-time-communication-protocols/#:~:text=The%20biggest%20disadvantage%20of,CSRF%2Dlike%20attacks%20a%20lot)
38. **WebSockets vs Server-Sent Events: Key differences and ...**. [https://ably.com](https://ably.com/blog/websockets-vs-sse#:~:text=After%20connecting%20to%20the,of%20the%20HTTP%20handshake)
39. **Performance difference between websocket and server ...**. [https://stackoverflow.com](https://stackoverflow.com/questions/63583989/performance-difference-between-websocket-and-server-sent-events-sse-for-chat-r#:~:text=Please%20confirm%2C%20if%20SSE,a%20large%20no.%20of)
40. **Optimizing Real-Time User Experiences with Server-Sent ...**. [https://javascript.plainenglish.io](https://javascript.plainenglish.io/optimizing-real-time-user-experiences-with-server-sent-events-sse-a-modern-alternative-to-8c5fb2f3eee9#:~:text=Implementing%20SSE%20is%20straightforward.,Backend%20%28Node.js%20%2B%20Express%29.)
41. **🚀 Built-in WebSockets in Node.js 2024: A Comprehensive ...**. [https://dev.to](https://dev.to/hamzakhan/built-in-websockets-in-nodejs-2024-a-comprehensive-guide-2236#:~:text=ws.send%28%27Welcome%20to%20the%20WebSocket%20server%21%27%29%3B)
42. **How to Reliably Read QR Codes in Node.js**. [https://dev.to](https://dev.to/jdg2896/how-to-reliably-read-qr-codes-in-nodejs-502i#:~:text=const%20decodedQR%20%3D%20jsQR%28imageData.data%2C%20imageData.width%2C%20imageData.height%29%3B)
43. **How to Reliably Read QR Codes in Node.js**. [https://medium.com](https://medium.com/@jdg2896/how-to-reliably-read-qr-codes-in-node-js-e4747fbe1545#:~:text=const%20decodedQR%20%3D%20jsQR%28imageData.data%2C%20imageData.width%2C%20imageData.height%29%3B)
44. **Finding the Right QR Code Scanner for Your JavaScript Project**. [https://portalzine.de](https://portalzine.de/finding-the-right-qr-code-scanner-for-your-javascript-project/#:~:text=%3D%20img.width%3B%20canvas.height%20%3D%20img.height%3B%20ctx.drawImage%28img%2C%200%2C%200%29%3B%20const%20imageData%20%3D%20ctx.getImageData%280%2C%200%2C%20canvas.width%2C%20canvas.height%29%3B%20const%20code%20%3D%20jsQR%28imageData.data%2C%20imageData.width%2C%20imageData.height%29%3B%20if%20%28code%29%20%7B%20console.log%28%27QR%20Code%20found%3A%27%2C%20code.data%29%3B%20console.log%28%27Position%3A%27%2C%20code.location%29%3B)
45. **How to create and read QR codes in Node.js**. [https://blog.logrocket.com](https://blog.logrocket.com/create-read-qr-codes-node-js/#:~:text=//%20__%20Importing%20qrcode,if%20%28err%29%20throw%20err%3B)
46. **Anyone have a JS zxing example that actually works?**. [https://stackoverflow.com](https://stackoverflow.com/questions/61910422/anyone-have-a-js-zxing-example-that-actually-works#:~:text=const%20reader%20%3D%20new)
47. **Finding the Right QR Code Scanner for Your JavaScript Project**. [https://portalzine.de](https://portalzine.de/finding-the-right-qr-code-scanner-for-your-javascript-project/#:~:text=3.%20qr%2Dscanner%20%28nimiq%29%20%E2%80%93%20Best%20for)
48. **How to create and read QR codes in Node.js**. [https://blog.logrocket.com](https://blog.logrocket.com/create-read-qr-codes-node-js/#:~:text=The%20qrcode%20library%20provides,API%20for%20writing%20QR)
49. **Finding the Right QR Code Scanner for Your JavaScript Project**. [https://portalzine.de](https://portalzine.de/finding-the-right-qr-code-scanner-for-your-javascript-project/#:~:text=2.%20html5%2Dqrcode%20%E2%80%93%20Best%20for%20Full%2DFeatured)
50. **Finding the Right QR Code Scanner for Your JavaScript Project**. [https://portalzine.de](https://portalzine.de/finding-the-right-qr-code-scanner-for-your-javascript-project/#:~:text=Library%20%7C%20NPM%20Downloads/Week,%7C%20Best%20Use%20Case)
51. **Finding the Right QR Code Scanner for Your JavaScript Project**. [https://portalzine.de](https://portalzine.de/finding-the-right-qr-code-scanner-for-your-javascript-project/#:~:text=N/A%20%28Native%29%20%7C%200KB,only%20%7C%20Future%2Dproof%20solutions)
52. **How to Reliably Read QR Codes in Node.js**. [https://dev.to](https://dev.to/jdg2896/how-to-reliably-read-qr-codes-in-nodejs-502i#:~:text=For%20the%20npm%20packages%2C,for%20the%20QR%20code)
53. **How to create and read QR codes in Node.js**. [https://blog.logrocket.com](https://blog.logrocket.com/create-read-qr-codes-node-js/#:~:text=this%20article%2C%20we%20will,QR%20codes%2C%20and%20qrcode)
54. **jsqr vs @zxing/library vs html5-qrcode vs qr-scanner vs ...**. [https://npm-compare.com](https://npm-compare.com/@zxing/library,html5-qrcode,jsqr,qr-scanner,qrcode-reader#:~:text=%40zxing/library%20is%20known%20for,for%20quick%20processing%2C%20making)
