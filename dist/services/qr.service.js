import { QRDecoder } from '../processors/qr-decoder.js';
import { ImageProcessor } from '../processors/image-processor.js';
import { logger } from '../utils/logger.js';
import fs from 'fs/promises';
import path from 'path';
export class QRService {
    qrDecoder;
    imageProcessor;
    constructor() {
        this.qrDecoder = new QRDecoder();
        this.imageProcessor = new ImageProcessor();
    }
    async scanQRCode(input, format = 'base64', options) {
        const startTime = Date.now();
        try {
            logger.info({ format, hasOptions: !!options }, 'Starting QR code scan');
            let result = null;
            switch (format) {
                case 'base64':
                    result = await this.scanFromBase64(input, options);
                    break;
                case 'filepath':
                    result = await this.scanFromFilePath(input, options);
                    break;
                case 'datauri':
                    result = await this.scanFromDataUri(input, options);
                    break;
                default:
                    throw new Error(`Unsupported format: ${format}`);
            }
            const processingTime = Date.now() - startTime;
            logger.info({ processingTime, found: !!result }, 'QR code scan completed');
            if (result) {
                return {
                    success: true,
                    data: result.data,
                    location: result.location,
                    version: result.version,
                    timestamp: new Date().toISOString()
                };
            }
            else {
                return {
                    success: false,
                    error: 'No QR code found in image',
                    timestamp: new Date().toISOString()
                };
            }
        }
        catch (error) {
            const processingTime = Date.now() - startTime;
            logger.error({ error, processingTime }, 'QR code scan failed');
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error occurred',
                timestamp: new Date().toISOString()
            };
        }
    }
    async scanFromBase64(base64, options) {
        try {
            const buffer = Buffer.from(base64, 'base64');
            return await this.processImageBuffer(buffer, options);
        }
        catch (error) {
            logger.error({ error }, 'Failed to process base64 image');
            throw new Error('Invalid base64 image data');
        }
    }
    async scanFromFilePath(filePath, options) {
        try {
            // Security check - ensure file path is safe
            const resolvedPath = path.resolve(filePath);
            const stats = await fs.stat(resolvedPath);
            if (!stats.isFile()) {
                throw new Error('Path is not a file');
            }
            const buffer = await fs.readFile(resolvedPath);
            return await this.processImageBuffer(buffer, options);
        }
        catch (error) {
            logger.error({ error, filePath }, 'Failed to process file');
            throw new Error(`Failed to read file: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async scanFromDataUri(dataUri, options) {
        try {
            if (!dataUri.startsWith('data:')) {
                throw new Error('Invalid data URI format');
            }
            const base64Data = dataUri.split(',')[1];
            if (!base64Data) {
                throw new Error('No base64 data found in data URI');
            }
            return await this.scanFromBase64(base64Data, options);
        }
        catch (error) {
            logger.error({ error }, 'Failed to process data URI');
            throw new Error('Invalid data URI format');
        }
    }
    async processImageBuffer(buffer, options) {
        try {
            // Apply image preprocessing if options are provided
            let processedBuffer = buffer;
            if (options) {
                processedBuffer = await this.imageProcessor.preprocessImage(buffer, options);
            }
            // Attempt to decode QR code
            const result = await this.qrDecoder.decodeFromBuffer(processedBuffer);
            // If no result and no preprocessing was done, try with default preprocessing
            if (!result && !options) {
                logger.debug('No QR code found, trying with default preprocessing');
                const defaultProcessedBuffer = await this.imageProcessor.preprocessImage(buffer, {
                    grayscale: true,
                    normalize: true
                });
                return await this.qrDecoder.decodeFromBuffer(defaultProcessedBuffer);
            }
            return result;
        }
        catch (error) {
            logger.error({ error }, 'Failed to process image buffer');
            throw error;
        }
    }
    // Utility method to validate image format
    async validateImageFormat(buffer) {
        try {
            // Simple validation - check for common image headers
            const header = buffer.slice(0, 8);
            // PNG signature
            if (header[0] === 0x89 && header[1] === 0x50 && header[2] === 0x4E && header[3] === 0x47) {
                return true;
            }
            // JPEG signature
            if (header[0] === 0xFF && header[1] === 0xD8) {
                return true;
            }
            // GIF signature
            if (header.toString('ascii', 0, 3) === 'GIF') {
                return true;
            }
            // WebP signature
            if (header.toString('ascii', 0, 4) === 'RIFF' && header.toString('ascii', 8, 12) === 'WEBP') {
                return true;
            }
            return false;
        }
        catch (error) {
            logger.error({ error }, 'Failed to validate image format');
            return false;
        }
    }
}
//# sourceMappingURL=qr.service.js.map