import { ProcessingOptions, QRScanResponse } from '../types/index.js';
export declare class QRService {
    private qrDecoder;
    private imageProcessor;
    constructor();
    scanQRCode(input: string, format?: 'base64' | 'filepath' | 'datauri', options?: ProcessingOptions): Promise<QRScanResponse>;
    private scanFromBase64;
    private scanFromFilePath;
    private scanFromDataUri;
    private processImageBuffer;
    validateImageFormat(buffer: Buffer): Promise<boolean>;
}
//# sourceMappingURL=qr.service.d.ts.map