import { z } from 'zod';
declare const ConfigSchema: z.ZodObject<{
    NODE_ENV: z.ZodDefault<z.ZodEnum<["development", "production", "test"]>>;
    PORT: z.ZodDefault<z.ZodEffects<z.ZodString, number, string>>;
    HOST: z.Zod<PERSON>efault<z.ZodString>;
    LOG_LEVEL: z.ZodDefault<z.ZodEnum<["trace", "debug", "info", "warn", "error"]>>;
    CORS_ORIGIN: z.ZodDefault<z.ZodString>;
    MAX_FILE_SIZE: z.<PERSON>od<PERSON>efault<z.ZodEffects<z.ZodString, number, string>>;
    STREAM_INTERVAL: z.ZodDefault<z.ZodEffects<z.ZodString, number, string>>;
    AUTH_ENABLED: z.ZodDefault<z.ZodEffects<z.ZodString, boolean, string>>;
    AUTH_TOKEN: z.ZodOptional<z.ZodString>;
    RATE_LIMIT_WINDOW_MS: z.Z<PERSON>efault<z.ZodEffects<z.ZodString, number, string>>;
    RATE_LIMIT_MAX_REQUESTS: z.ZodDefault<z.ZodEffects<z.ZodString, number, string>>;
    HEALTH_CHECK_INTERVAL: z.ZodDefault<z.ZodEffects<z.ZodString, number, string>>;
}, "strip", z.ZodTypeAny, {
    NODE_ENV: "development" | "production" | "test";
    PORT: number;
    HOST: string;
    LOG_LEVEL: "trace" | "debug" | "info" | "warn" | "error";
    CORS_ORIGIN: string;
    MAX_FILE_SIZE: number;
    STREAM_INTERVAL: number;
    AUTH_ENABLED: boolean;
    RATE_LIMIT_WINDOW_MS: number;
    RATE_LIMIT_MAX_REQUESTS: number;
    HEALTH_CHECK_INTERVAL: number;
    AUTH_TOKEN?: string | undefined;
}, {
    NODE_ENV?: "development" | "production" | "test" | undefined;
    PORT?: string | undefined;
    HOST?: string | undefined;
    LOG_LEVEL?: "trace" | "debug" | "info" | "warn" | "error" | undefined;
    CORS_ORIGIN?: string | undefined;
    MAX_FILE_SIZE?: string | undefined;
    STREAM_INTERVAL?: string | undefined;
    AUTH_ENABLED?: string | undefined;
    AUTH_TOKEN?: string | undefined;
    RATE_LIMIT_WINDOW_MS?: string | undefined;
    RATE_LIMIT_MAX_REQUESTS?: string | undefined;
    HEALTH_CHECK_INTERVAL?: string | undefined;
}>;
export type Config = z.infer<typeof ConfigSchema>;
export declare const config: {
    NODE_ENV: "development" | "production" | "test";
    PORT: number;
    HOST: string;
    LOG_LEVEL: "trace" | "debug" | "info" | "warn" | "error";
    CORS_ORIGIN: string;
    MAX_FILE_SIZE: number;
    STREAM_INTERVAL: number;
    AUTH_ENABLED: boolean;
    RATE_LIMIT_WINDOW_MS: number;
    RATE_LIMIT_MAX_REQUESTS: number;
    HEALTH_CHECK_INTERVAL: number;
    AUTH_TOKEN?: string | undefined;
};
export default config;
//# sourceMappingURL=config.d.ts.map