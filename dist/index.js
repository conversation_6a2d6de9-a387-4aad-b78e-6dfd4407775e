// Legacy entry point - use server.ts instead
import { Server } from './server.js';
import { logger } from './utils/logger.js';
logger.warn('Using legacy entry point. Consider using server.ts directly.');
const server = new Server();
server.start().catch((error) => {
    logger.error({ error }, 'Failed to start server from legacy entry point');
    process.exit(1);
});
//# sourceMappingURL=index.js.map