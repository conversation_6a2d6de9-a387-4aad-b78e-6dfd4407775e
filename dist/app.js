import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { pinoHttp } from 'pino-http';
import { config } from './utils/config.js';
import { logger } from './utils/logger.js';
import { errorHandler, notFoundHandler } from './middleware/error.middleware.js';
import { rateLimiter } from './middleware/rate-limit.middleware.js';
import { healthRoutes } from './routes/health.routes.js';
import { mcpRoutes } from './routes/mcp.routes.js';
import { apiRoutes } from './routes/api.routes.js';
export function createApp() {
    const app = express();
    // Trust proxy for rate limiting and IP detection
    app.set('trust proxy', 1);
    // Security middleware
    app.use(helmet({
        contentSecurityPolicy: {
            directives: {
                defaultSrc: ["'self'"],
                styleSrc: ["'self'", "'unsafe-inline'"],
                scriptSrc: ["'self'"],
                imgSrc: ["'self'", "data:", "https:"],
                connectSrc: ["'self'"],
                fontSrc: ["'self'"],
                objectSrc: ["'none'"],
                mediaSrc: ["'self'"],
                frameSrc: ["'none'"],
            },
        },
        crossOriginEmbedderPolicy: false
    }));
    // CORS configuration
    app.use(cors({
        origin: config.CORS_ORIGIN === '*' ? true : config.CORS_ORIGIN.split(','),
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
    }));
    // Request parsing middleware
    app.use(express.json({
        limit: `${Math.floor(config.MAX_FILE_SIZE / 1024 / 1024)}mb`,
        verify: (req, res, buf) => {
            // Store raw body for MCP transport if needed
            req.rawBody = buf;
        }
    }));
    app.use(express.urlencoded({
        extended: true,
        limit: `${Math.floor(config.MAX_FILE_SIZE / 1024 / 1024)}mb`
    }));
    // Request logging
    app.use(pinoHttp({
        logger,
        customLogLevel: (req, res, err) => {
            if (res.statusCode >= 400 && res.statusCode < 500) {
                return 'warn';
            }
            else if (res.statusCode >= 500 || err) {
                return 'error';
            }
            else if (res.statusCode >= 300 && res.statusCode < 400) {
                return 'silent';
            }
            return 'info';
        },
        customSuccessMessage: (req, res) => {
            if (res.statusCode === 404) {
                return 'Resource not found';
            }
            return `${req.method} ${req.url}`;
        },
        customErrorMessage: (req, res, err) => {
            return `${req.method} ${req.url} - ${err.message}`;
        }
    }));
    // Rate limiting
    app.use(rateLimiter);
    // Health check routes (no additional rate limiting)
    app.use('/health', healthRoutes);
    // API routes
    app.use('/api', apiRoutes);
    // MCP routes - these will be handled by the MCP transport
    app.use('/mcp', mcpRoutes);
    // Root endpoint
    app.get('/', (req, res) => {
        res.json({
            name: 'QRMCPNode Server',
            version: '1.0.0',
            description: 'QR Code MCP Server for Node.js',
            endpoints: {
                health: '/health',
                mcp: '/mcp',
                api: '/api'
            },
            timestamp: new Date().toISOString()
        });
    });
    // 404 handler
    app.use(notFoundHandler);
    // Error handling middleware (must be last)
    app.use(errorHandler);
    return app;
}
export default createApp;
//# sourceMappingURL=app.js.map