import { z } from 'zod';
export declare const ScanQRInputSchema: z.ZodObject<{
    input: z.ZodString;
    format: z.ZodDefault<z.ZodOptional<z.ZodEnum<["base64", "filepath", "datauri"]>>>;
    options: z.<PERSON>od<PERSON>ptional<z.ZodObject<{
        inversionAttempts: z.ZodOptional<z.ZodEnum<["dontInvert", "onlyInvert", "attemptBoth"]>>;
        greyScaleWeights: z.ZodOptional<z.ZodObject<{
            red: z.ZodOptional<z.ZodNumber>;
            green: z.ZodOptional<z.ZodNumber>;
            blue: z.ZodOptional<z.ZodNumber>;
        }, "strip", z.<PERSON>odType<PERSON>ny, {
            red?: number | undefined;
            green?: number | undefined;
            blue?: number | undefined;
        }, {
            red?: number | undefined;
            green?: number | undefined;
            blue?: number | undefined;
        }>>;
    }, "strip", z.<PERSON>odTypeAny, {
        inversionAttempts?: "dontInvert" | "onlyInvert" | "attemptBoth" | undefined;
        greyScaleWeights?: {
            red?: number | undefined;
            green?: number | undefined;
            blue?: number | undefined;
        } | undefined;
    }, {
        inversionAttempts?: "dontInvert" | "onlyInvert" | "attemptBoth" | undefined;
        greyScaleWeights?: {
            red?: number | undefined;
            green?: number | undefined;
            blue?: number | undefined;
        } | undefined;
    }>>;
}, "strip", z.ZodTypeAny, {
    format: "base64" | "filepath" | "datauri";
    input: string;
    options?: {
        inversionAttempts?: "dontInvert" | "onlyInvert" | "attemptBoth" | undefined;
        greyScaleWeights?: {
            red?: number | undefined;
            green?: number | undefined;
            blue?: number | undefined;
        } | undefined;
    } | undefined;
}, {
    input: string;
    options?: {
        inversionAttempts?: "dontInvert" | "onlyInvert" | "attemptBoth" | undefined;
        greyScaleWeights?: {
            red?: number | undefined;
            green?: number | undefined;
            blue?: number | undefined;
        } | undefined;
    } | undefined;
    format?: "base64" | "filepath" | "datauri" | undefined;
}>;
export declare const StreamQRInputSchema: z.ZodObject<{
    source: z.ZodEnum<["camera", "file", "url"]>;
    sourceData: z.ZodOptional<z.ZodString>;
    options: z.ZodOptional<z.ZodObject<{
        interval: z.ZodDefault<z.ZodNumber>;
        duration: z.ZodOptional<z.ZodNumber>;
        format: z.ZodDefault<z.ZodEnum<["json", "text"]>>;
    }, "strip", z.ZodTypeAny, {
        format: "json" | "text";
        interval: number;
        duration?: number | undefined;
    }, {
        format?: "json" | "text" | undefined;
        interval?: number | undefined;
        duration?: number | undefined;
    }>>;
}, "strip", z.ZodTypeAny, {
    source: "url" | "camera" | "file";
    options?: {
        format: "json" | "text";
        interval: number;
        duration?: number | undefined;
    } | undefined;
    sourceData?: string | undefined;
}, {
    source: "url" | "camera" | "file";
    options?: {
        format?: "json" | "text" | undefined;
        interval?: number | undefined;
        duration?: number | undefined;
    } | undefined;
    sourceData?: string | undefined;
}>;
export type ScanQRInput = z.infer<typeof ScanQRInputSchema>;
export type StreamQRInput = z.infer<typeof StreamQRInputSchema>;
export interface MCPToolResponse {
    content: Array<{
        type: 'text';
        text: string;
    } | {
        type: 'image';
        data: string;
        mimeType: string;
    } | {
        type: 'resource';
        resource: {
            uri: string;
            text?: string;
            blob?: string;
            mimeType?: string;
        };
    }>;
    isError?: boolean;
}
export interface QRHistoryResource {
    uri: string;
    name: string;
    description: string;
    mimeType: string;
}
export interface MCPServerInfo {
    name: string;
    version: string;
    description: string;
    capabilities: {
        tools: boolean;
        resources: boolean;
        prompts: boolean;
        logging: boolean;
    };
}
//# sourceMappingURL=mcp.types.d.ts.map