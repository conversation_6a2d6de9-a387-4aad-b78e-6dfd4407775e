import { z } from 'zod';
// MCP Tool Input Schemas
export const ScanQRInputSchema = z.object({
    input: z.string().describe('Base64, file path, or data URI of image'),
    format: z.enum(['base64', 'filepath', 'datauri']).optional().default('base64'),
    options: z.object({
        inversionAttempts: z.enum(['dontInvert', 'onlyInvert', 'attemptBoth']).optional(),
        greyScaleWeights: z.object({
            red: z.number().optional(),
            green: z.number().optional(),
            blue: z.number().optional()
        }).optional()
    }).optional()
});
export const StreamQRInputSchema = z.object({
    source: z.enum(['camera', 'file', 'url']),
    sourceData: z.string().optional(),
    options: z.object({
        interval: z.number().default(100),
        duration: z.number().optional(),
        format: z.enum(['json', 'text']).default('json')
    }).optional()
});
//# sourceMappingURL=mcp.types.js.map