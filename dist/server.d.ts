import { QRMCPServer } from './mcp/server.js';
declare class Server {
    private app;
    private mcpServer;
    private httpServer;
    constructor();
    start(): Promise<void>;
    private initializeMCPServer;
    private integrateMCPWithExpress;
    private startHTTPServer;
    private setupGracefulShutdown;
    stop(): Promise<void>;
    getApp(): import("express-serve-static-core").Express;
    getMCPServer(): QRMCPServer;
}
export { Server };
export default Server;
//# sourceMappingURL=server.d.ts.map