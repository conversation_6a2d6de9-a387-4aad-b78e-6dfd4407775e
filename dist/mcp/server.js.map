{"version": 3, "file": "server.js", "sourceRoot": "", "sources": ["../../src/mcp/server.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,yCAAyC,CAAC;AACpE,OAAO,EAAE,6BAA6B,EAAE,MAAM,oDAAoD,CAAC;AACnG,OAAO,EAAE,UAAU,EAAE,MAAM,aAAa,CAAC;AACzC,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAC5C,OAAO,EAAE,gBAAgB,EAAE,MAAM,yBAAyB,CAAC;AAC3D,OAAO,EAAE,kBAAkB,EAAE,MAAM,2BAA2B,CAAC;AAC/D,OAAO,EAAE,uBAAuB,EAAE,MAAM,oCAAoC,CAAC;AAE7E,MAAM,OAAO,WAAW;IACd,SAAS,CAAY;IACrB,SAAS,CAAgC;IAEjD;QACE,IAAI,CAAC,SAAS,GAAG,IAAI,SAAS,CAAC;YAC7B,IAAI,EAAE,YAAY;YAClB,OAAO,EAAE,OAAO;YAChB,WAAW,EAAE,gCAAgC;SAC9C,EAAE;YACD,YAAY,EAAE;gBACZ,OAAO,EAAE,EAAE;gBACX,KAAK,EAAE,EAAE;gBACT,SAAS,EAAE,EAAE;aACd;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,GAAG,IAAI,6BAA6B,CAAC;YACjD,kBAAkB,EAAE,GAAG,EAAE,CAAC,UAAU,EAAE;SACvC,CAAC,CAAC;QAEH,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAEO,UAAU;QAChB,IAAI,CAAC;YACH,iBAAiB;YACjB,IAAI,CAAC,aAAa,EAAE,CAAC;YAErB,qBAAqB;YACrB,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAEzB,wBAAwB;YACxB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,GAAG,CAAC,KAAY,EAAE,EAAE;gBAC/C,MAAM,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,kBAAkB,CAAC,CAAC;YAC9C,CAAC,CAAC;YAEF,MAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;QACrD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,iCAAiC,CAAC,CAAC;YAC3D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,aAAa;QACnB,IAAI,CAAC;YACH,wBAAwB;YACxB,MAAM,UAAU,GAAG,gBAAgB,EAAE,CAAC;YACtC,IAAI,CAAC,SAAS,CAAC,YAAY,CACzB,UAAU,CAAC,IAAI,EACf;gBACE,KAAK,EAAE,iBAAiB;gBACxB,WAAW,EAAE,UAAU,CAAC,WAAW;gBACnC,WAAW,EAAE,UAAU,CAAC,WAAW;aACpC,EACD,UAAU,CAAC,OAAO,CACnB,CAAC;YAEF,0BAA0B;YAC1B,MAAM,YAAY,GAAG,kBAAkB,EAAE,CAAC;YAC1C,IAAI,CAAC,SAAS,CAAC,YAAY,CACzB,YAAY,CAAC,IAAI,EACjB;gBACE,KAAK,EAAE,wBAAwB;gBAC/B,WAAW,EAAE,YAAY,CAAC,WAAW;gBACrC,WAAW,EAAE,YAAY,CAAC,WAAW;aACtC,EACD,YAAY,CAAC,OAAO,CACrB,CAAC;YAEF,MAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;QACnD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,8BAA8B,CAAC,CAAC;YACxD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,iBAAiB;QACvB,IAAI,CAAC;YACH,+BAA+B;YAC/B,MAAM,iBAAiB,GAAG,uBAAuB,EAAE,CAAC;YACpD,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAC7B,iBAAiB,CAAC,IAAI,EACtB,iBAAiB,CAAC,QAAQ,EAC1B;gBACE,KAAK,EAAE,iBAAiB,CAAC,KAAK;gBAC9B,WAAW,EAAE,iBAAiB,CAAC,WAAW;aAC3C,EACD,iBAAiB,CAAC,OAAO,CAC1B,CAAC;YAEF,MAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;QACvD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,kCAAkC,CAAC,CAAC;YAC5D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,OAAO;QACX,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC7C,MAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;QACnD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,8BAA8B,CAAC,CAAC;YACxD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,KAAK;QACT,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;YAC7B,MAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;QAChD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,4BAA4B,CAAC,CAAC;YACtD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,YAAY;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED,YAAY;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED,sBAAsB;IACtB,SAAS;QACP,IAAI,CAAC;YACH,oDAAoD;YACpD,OAAO,IAAI,CAAC,SAAS,KAAK,IAAI,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC;QAC5D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,qBAAqB,CAAC,CAAC;YAC/C,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;CACF"}