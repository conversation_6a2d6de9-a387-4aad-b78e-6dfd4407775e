import { ResourceTemplate } from '@modelcontextprotocol/sdk/server/mcp.js';
export declare function createQRHistoryResource(): {
    name: string;
    template: ResourceTemplate;
    title: string;
    description: string;
    handler: (uri: URL, variables: {
        id?: string;
    }) => Promise<{
        contents: {
            uri: string;
            text: string;
            mimeType: string;
        }[];
    }>;
};
//# sourceMappingURL=qr-history.resource.d.ts.map