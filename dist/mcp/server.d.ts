import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { StreamableHTTPServerTransport } from '@modelcontextprotocol/sdk/server/streamableHttp.js';
export declare class QRMCPServer {
    private mcpServer;
    private transport;
    constructor();
    private initialize;
    private registerTools;
    private registerResources;
    connect(): Promise<void>;
    close(): Promise<void>;
    getTransport(): StreamableHTTPServerTransport;
    getMcpServer(): McpServer;
    isHealthy(): boolean;
}
//# sourceMappingURL=server.d.ts.map