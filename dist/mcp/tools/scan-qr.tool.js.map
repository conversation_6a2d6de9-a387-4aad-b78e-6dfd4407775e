{"version": 3, "file": "scan-qr.tool.js", "sourceRoot": "", "sources": ["../../../src/mcp/tools/scan-qr.tool.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AACxB,OAAO,EAAE,SAAS,EAAE,MAAM,8BAA8B,CAAC;AACzD,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAG/C,MAAM,UAAU,gBAAgB;IAC9B,MAAM,SAAS,GAAG,IAAI,SAAS,EAAE,CAAC;IAElC,OAAO;QACL,IAAI,EAAE,SAAS;QACf,WAAW,EAAE,iFAAiF;QAC9F,WAAW,EAAE;YACX,KAAK,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,yCAAyC,CAAC;YACrE,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC;YAC9E,OAAO,EAAE,CAAC,CAAC,MAAM,CAAC;gBAChB,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,YAAY,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC,CAAC,QAAQ,EAAE;gBACjF,gBAAgB,EAAE,CAAC,CAAC,MAAM,CAAC;oBACzB,GAAG,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;oBAC1B,KAAK,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;oBAC5B,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;iBAC5B,CAAC,CAAC,QAAQ,EAAE;aACd,CAAC,CAAC,QAAQ,EAAE;SACd;QACD,OAAO,EAAE,KAAK,EAAE,IAAS,EAAgB,EAAE;YACzC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,IAAI,CAAC;gBACH,MAAM,CAAC,IAAI,CAAC;oBACV,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO;oBAC1B,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM;iBAC/B,EAAE,4BAA4B,CAAC,CAAC;gBAEjC,iBAAiB;gBACjB,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAClD,OAAO;wBACL,OAAO,EAAE,CAAC;gCACR,IAAI,EAAE,MAAM;gCACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;oCACnB,OAAO,EAAE,KAAK;oCACd,KAAK,EAAE,uCAAuC;oCAC9C,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iCACpC,EAAE,IAAI,EAAE,CAAC,CAAC;6BACZ,CAAC;wBACF,OAAO,EAAE,IAAI;qBACd,CAAC;gBACJ,CAAC;gBAED,2BAA2B;gBAC3B,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,UAAU,CACvC,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,MAAM,IAAI,QAAQ,EACvB,IAAI,CAAC,OAAO,CACb,CAAC;gBAEF,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBAE9C,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;oBACnB,MAAM,CAAC,IAAI,CAAC;wBACV,cAAc;wBACd,UAAU,EAAE,MAAM,CAAC,IAAI,EAAE,MAAM;wBAC/B,OAAO,EAAE,MAAM,CAAC,OAAO;qBACxB,EAAE,yBAAyB,CAAC,CAAC;oBAE9B,OAAO;wBACL,OAAO,EAAE,CAAC;gCACR,IAAI,EAAE,MAAM;gCACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;oCACnB,OAAO,EAAE,IAAI;oCACb,IAAI,EAAE,MAAM,CAAC,IAAI;oCACjB,QAAQ,EAAE,MAAM,CAAC,QAAQ;oCACzB,OAAO,EAAE,MAAM,CAAC,OAAO;oCACvB,SAAS,EAAE,MAAM,CAAC,SAAS;oCAC3B,cAAc;iCACf,EAAE,IAAI,EAAE,CAAC,CAAC;6BACZ,CAAC;qBACH,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBACN,MAAM,CAAC,IAAI,CAAC,EAAE,cAAc,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,EAAE,qBAAqB,CAAC,CAAC;oBAE5E,OAAO;wBACL,OAAO,EAAE,CAAC;gCACR,IAAI,EAAE,MAAM;gCACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;oCACnB,OAAO,EAAE,KAAK;oCACd,KAAK,EAAE,MAAM,CAAC,KAAK;oCACnB,SAAS,EAAE,MAAM,CAAC,SAAS;oCAC3B,cAAc;iCACf,EAAE,IAAI,EAAE,CAAC,CAAC;6BACZ,CAAC;qBACH,CAAC;gBACJ,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBAC9C,MAAM,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,oBAAoB,CAAC,CAAC;gBAE9D,OAAO;oBACL,OAAO,EAAE,CAAC;4BACR,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;gCACnB,OAAO,EAAE,KAAK;gCACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,wBAAwB;gCACxE,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gCACnC,cAAc;6BACf,EAAE,IAAI,EAAE,CAAC,CAAC;yBACZ,CAAC;oBACF,OAAO,EAAE,IAAI;iBACd,CAAC;YACJ,CAAC;QACH,CAAC;KACF,CAAC;AACJ,CAAC;AAED,2CAA2C;AAC3C,SAAS,aAAa,CAAC,GAAW;IAChC,IAAI,CAAC;QACH,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,GAAG,CAAC;IAC/D,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED,uCAAuC;AACvC,SAAS,cAAc,CAAC,GAAW;IACjC,MAAM,YAAY,GAAG,+DAA+D,CAAC;IACrF,OAAO,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAChC,CAAC;AAED,wCAAwC;AACxC,MAAM,UAAU,wBAAwB;IACtC,MAAM,SAAS,GAAG,IAAI,SAAS,EAAE,CAAC;IAElC,OAAO;QACL,IAAI,EAAE,kBAAkB;QACxB,WAAW,EAAE,mFAAmF;QAChG,WAAW,EAAE;YACX,KAAK,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,yCAAyC,CAAC;YACrE,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC;YAC9E,OAAO,EAAE,CAAC,CAAC,MAAM,CAAC;gBAChB,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,YAAY,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC,CAAC,QAAQ,EAAE;gBACjF,gBAAgB,EAAE,CAAC,CAAC,MAAM,CAAC;oBACzB,GAAG,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;oBAC1B,KAAK,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;oBAC5B,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;iBAC5B,CAAC,CAAC,QAAQ,EAAE;aACd,CAAC,CAAC,QAAQ,EAAE;YACb,aAAa,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;YACnD,qBAAqB,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;SAC7D;QACD,OAAO,EAAE,KAAK,EAAE,IAAS,EAAgB,EAAE;YACzC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,IAAI,CAAC;gBACH,MAAM,CAAC,IAAI,CAAC;oBACV,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,aAAa,EAAE,IAAI,CAAC,aAAa;oBACjC,qBAAqB,EAAE,IAAI,CAAC,qBAAqB;iBAClD,EAAE,qCAAqC,CAAC,CAAC;gBAE1C,8BAA8B;gBAC9B,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;oBACvB,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;wBAC3D,OAAO;4BACL,OAAO,EAAE,CAAC;oCACR,IAAI,EAAE,MAAM;oCACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;wCACnB,OAAO,EAAE,KAAK;wCACd,KAAK,EAAE,uBAAuB;wCAC9B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;qCACpC,EAAE,IAAI,EAAE,CAAC,CAAC;iCACZ,CAAC;4BACF,OAAO,EAAE,IAAI;yBACd,CAAC;oBACJ,CAAC;oBAED,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;wBAC7D,OAAO;4BACL,OAAO,EAAE,CAAC;oCACR,IAAI,EAAE,MAAM;oCACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;wCACnB,OAAO,EAAE,KAAK;wCACd,KAAK,EAAE,yBAAyB;wCAChC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;qCACpC,EAAE,IAAI,EAAE,CAAC,CAAC;iCACZ,CAAC;4BACF,OAAO,EAAE,IAAI;yBACd,CAAC;oBACJ,CAAC;gBACH,CAAC;gBAED,2BAA2B;gBAC3B,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,UAAU,CACvC,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,MAAM,IAAI,QAAQ,EACvB,IAAI,CAAC,OAAO,CACb,CAAC;gBAEF,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBAE9C,OAAO;oBACL,OAAO,EAAE,CAAC;4BACR,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;gCACnB,GAAG,MAAM;gCACT,cAAc;gCACd,QAAQ,EAAE,IAAI;6BACf,EAAE,IAAI,EAAE,CAAC,CAAC;yBACZ,CAAC;oBACF,OAAO,EAAE,CAAC,MAAM,CAAC,OAAO;iBACzB,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBAC9C,MAAM,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,6BAA6B,CAAC,CAAC;gBAEvE,OAAO;oBACL,OAAO,EAAE,CAAC;4BACR,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;gCACnB,OAAO,EAAE,KAAK;gCACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,wBAAwB;gCACxE,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gCACnC,cAAc;gCACd,QAAQ,EAAE,IAAI;6BACf,EAAE,IAAI,EAAE,CAAC,CAAC;yBACZ,CAAC;oBACF,OAAO,EAAE,IAAI;iBACd,CAAC;YACJ,CAAC;QACH,CAAC;KACF,CAAC;AACJ,CAAC"}