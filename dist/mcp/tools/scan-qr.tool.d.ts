import { z } from 'zod';
export declare function createScanQRTool(): {
    name: string;
    description: string;
    inputSchema: {
        input: z.ZodString;
        format: z.ZodDefault<z.ZodOptional<z.ZodEnum<["base64", "filepath", "datauri"]>>>;
        options: z.<PERSON>od<PERSON>ptional<z.ZodObject<{
            inversionAttempts: z.ZodOptional<z.ZodEnum<["dontInvert", "onlyInvert", "attemptBoth"]>>;
            greyScaleWeights: z.ZodOptional<z.ZodObject<{
                red: z.ZodOptional<z.ZodNumber>;
                green: z.ZodOptional<z.ZodNumber>;
                blue: z.ZodOptional<z.ZodNumber>;
            }, "strip", z.ZodTypeAny, {
                red?: number | undefined;
                green?: number | undefined;
                blue?: number | undefined;
            }, {
                red?: number | undefined;
                green?: number | undefined;
                blue?: number | undefined;
            }>>;
        }, "strip", z.<PERSON><PERSON><PERSON><PERSON><PERSON>, {
            inversionAttempts?: "dontInvert" | "onlyInvert" | "attemptBoth" | undefined;
            greyScaleWeights?: {
                red?: number | undefined;
                green?: number | undefined;
                blue?: number | undefined;
            } | undefined;
        }, {
            inversionAttempts?: "dontInvert" | "onlyInvert" | "attemptBoth" | undefined;
            greyScaleWeights?: {
                red?: number | undefined;
                green?: number | undefined;
                blue?: number | undefined;
            } | undefined;
        }>>;
    };
    handler: (args: any) => Promise<any>;
};
export declare function createEnhancedScanQRTool(): {
    name: string;
    description: string;
    inputSchema: {
        input: z.ZodString;
        format: z.ZodDefault<z.ZodOptional<z.ZodEnum<["base64", "filepath", "datauri"]>>>;
        options: z.ZodOptional<z.ZodObject<{
            inversionAttempts: z.ZodOptional<z.ZodEnum<["dontInvert", "onlyInvert", "attemptBoth"]>>;
            greyScaleWeights: z.ZodOptional<z.ZodObject<{
                red: z.ZodOptional<z.ZodNumber>;
                green: z.ZodOptional<z.ZodNumber>;
                blue: z.ZodOptional<z.ZodNumber>;
            }, "strip", z.ZodTypeAny, {
                red?: number | undefined;
                green?: number | undefined;
                blue?: number | undefined;
            }, {
                red?: number | undefined;
                green?: number | undefined;
                blue?: number | undefined;
            }>>;
        }, "strip", z.ZodTypeAny, {
            inversionAttempts?: "dontInvert" | "onlyInvert" | "attemptBoth" | undefined;
            greyScaleWeights?: {
                red?: number | undefined;
                green?: number | undefined;
                blue?: number | undefined;
            } | undefined;
        }, {
            inversionAttempts?: "dontInvert" | "onlyInvert" | "attemptBoth" | undefined;
            greyScaleWeights?: {
                red?: number | undefined;
                green?: number | undefined;
                blue?: number | undefined;
            } | undefined;
        }>>;
        validateInput: z.ZodDefault<z.ZodOptional<z.ZodBoolean>>;
        useMultipleStrategies: z.ZodDefault<z.ZodOptional<z.ZodBoolean>>;
    };
    handler: (args: any) => Promise<any>;
};
//# sourceMappingURL=scan-qr.tool.d.ts.map