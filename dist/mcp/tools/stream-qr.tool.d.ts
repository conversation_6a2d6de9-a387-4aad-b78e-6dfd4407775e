import { z } from 'zod';
export declare function createStreamQRTool(): {
    name: string;
    description: string;
    inputSchema: {
        source: z.ZodE<PERSON><["camera", "file", "url"]>;
        sourceData: z.ZodOptional<z.ZodString>;
        options: z.<PERSON>ptional<z.ZodObject<{
            interval: z.Zod<PERSON>efault<z.ZodNumber>;
            duration: z.ZodOptional<z.ZodNumber>;
            format: z.ZodDefault<z.ZodEnum<["json", "text"]>>;
        }, "strip", z.ZodTypeAny, {
            format: "json" | "text";
            interval: number;
            duration?: number | undefined;
        }, {
            format?: "json" | "text" | undefined;
            interval?: number | undefined;
            duration?: number | undefined;
        }>>;
    };
    handler: (args: any) => Promise<any>;
};
//# sourceMappingURL=stream-qr.tool.d.ts.map