import { <PERSON><PERSON> } from 'jimp';
import { logger } from '../utils/logger.js';
export class ImageProcessor {
    async preprocessImage(buffer, options = {}) {
        try {
            logger.debug({ options }, 'Starting image preprocessing');
            let image = await Jimp.read(buffer);
            // Apply resize if specified
            if (options.resize) {
                const { width, height } = options.resize;
                image = image.resize({ w: width, h: height });
                logger.debug({ width, height }, 'Image resized');
            }
            // Apply grayscale conversion
            if (options.grayscale) {
                image = image.greyscale();
                logger.debug('Image converted to grayscale');
            }
            // Apply contrast adjustment
            if (options.contrast !== undefined) {
                image = image.contrast(options.contrast);
                logger.debug({ contrast: options.contrast }, 'Contrast adjusted');
            }
            // Apply brightness adjustment
            if (options.brightness !== undefined) {
                image = image.brightness(options.brightness);
                logger.debug({ brightness: options.brightness }, 'Brightness adjusted');
            }
            // Apply normalization
            if (options.normalize) {
                image = image.normalize();
                logger.debug('Image normalized');
            }
            const processedBuffer = await image.getBuffer('image/png');
            logger.debug('Image preprocessing completed');
            return processedBuffer;
        }
        catch (error) {
            logger.error({ error, options }, 'Error preprocessing image');
            throw error;
        }
    }
    async enhanceForQRDetection(buffer) {
        try {
            logger.debug('Enhancing image for QR detection');
            let image = await Jimp.read(buffer);
            // Apply QR-specific enhancements
            image = image
                .greyscale() // Convert to grayscale
                .contrast(0.3) // Increase contrast
                .normalize(); // Normalize histogram
            const enhancedBuffer = await image.getBuffer('image/png');
            logger.debug('QR detection enhancement completed');
            return enhancedBuffer;
        }
        catch (error) {
            logger.error({ error }, 'Error enhancing image for QR detection');
            throw error;
        }
    }
    async createThumbnail(buffer, size = 150) {
        try {
            logger.debug({ size }, 'Creating thumbnail');
            const image = await Jimp.read(buffer);
            const thumbnail = image.resize({ w: size, h: size });
            const thumbnailBuffer = await thumbnail.getBuffer('image/jpeg');
            logger.debug('Thumbnail created');
            return thumbnailBuffer;
        }
        catch (error) {
            logger.error({ error, size }, 'Error creating thumbnail');
            throw error;
        }
    }
    async getImageInfo(buffer) {
        try {
            const image = await Jimp.read(buffer);
            return {
                width: image.bitmap.width,
                height: image.bitmap.height,
                mimeType: image.mime || 'image/unknown',
                size: buffer.length
            };
        }
        catch (error) {
            logger.error({ error }, 'Error getting image info');
            throw error;
        }
    }
    async validateImage(buffer) {
        try {
            await Jimp.read(buffer);
            return true;
        }
        catch (error) {
            logger.debug({ error }, 'Image validation failed');
            return false;
        }
    }
    // Extract frames from animated GIF (placeholder for future implementation)
    async extractFramesFromGif(buffer) {
        try {
            logger.debug('Extracting frames from GIF');
            // For now, just return the original buffer as a single frame
            // In a full implementation, you would use a library like gif-frames
            const image = await Jimp.read(buffer);
            const frameBuffer = await image.getBuffer('image/png');
            logger.debug('GIF frame extraction completed (single frame)');
            return [frameBuffer];
        }
        catch (error) {
            logger.error({ error }, 'Error extracting frames from GIF');
            throw error;
        }
    }
    // Apply multiple enhancement strategies for difficult QR codes
    async applyEnhancementStrategies(buffer) {
        try {
            logger.debug('Applying multiple enhancement strategies');
            const strategies = [
                // Original image
                {},
                // High contrast
                { grayscale: true, contrast: 0.5, normalize: true },
                // Low contrast
                { grayscale: true, contrast: -0.2, normalize: true },
                // Brightness adjustments
                { grayscale: true, brightness: 0.2, normalize: true },
                { grayscale: true, brightness: -0.2, normalize: true },
                // Combined adjustments
                { grayscale: true, contrast: 0.3, brightness: 0.1, normalize: true }
            ];
            const enhancedImages = [];
            for (const strategy of strategies) {
                try {
                    const enhanced = await this.preprocessImage(buffer, strategy);
                    enhancedImages.push(enhanced);
                }
                catch (error) {
                    logger.warn({ error, strategy }, 'Enhancement strategy failed');
                }
            }
            logger.debug({ count: enhancedImages.length }, 'Enhancement strategies applied');
            return enhancedImages;
        }
        catch (error) {
            logger.error({ error }, 'Error applying enhancement strategies');
            throw error;
        }
    }
}
//# sourceMappingURL=image-processor.js.map