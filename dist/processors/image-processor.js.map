{"version": 3, "file": "image-processor.js", "sourceRoot": "", "sources": ["../../src/processors/image-processor.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,MAAM,CAAC;AAC5B,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAG5C,MAAM,OAAO,cAAc;IACzB,KAAK,CAAC,eAAe,CACnB,MAAc,EACd,UAA6B,EAAE;QAE/B,IAAI,CAAC;YACH,MAAM,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,EAAE,8BAA8B,CAAC,CAAC;YAE1D,IAAI,KAAK,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAEpC,4BAA4B;YAC5B,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;gBACnB,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC;gBACzC,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;gBAC9C,MAAM,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,eAAe,CAAC,CAAC;YACnD,CAAC;YAED,6BAA6B;YAC7B,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;gBACtB,KAAK,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;gBAC1B,MAAM,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAC;YAC/C,CAAC;YAED,4BAA4B;YAC5B,IAAI,OAAO,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;gBACnC,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gBACzC,MAAM,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,EAAE,mBAAmB,CAAC,CAAC;YACpE,CAAC;YAED,8BAA8B;YAC9B,IAAI,OAAO,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;gBACrC,KAAK,GAAG,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;gBAC7C,MAAM,CAAC,KAAK,CAAC,EAAE,UAAU,EAAE,OAAO,CAAC,UAAU,EAAE,EAAE,qBAAqB,CAAC,CAAC;YAC1E,CAAC;YAED,sBAAsB;YACtB,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;gBACtB,KAAK,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;gBAC1B,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;YACnC,CAAC;YAED,MAAM,eAAe,GAAG,MAAM,KAAK,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YAC3D,MAAM,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;YAE9C,OAAO,eAAe,CAAC;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,2BAA2B,CAAC,CAAC;YAC9D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,MAAc;QACxC,IAAI,CAAC;YACH,MAAM,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAC;YAEjD,IAAI,KAAK,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAEpC,iCAAiC;YACjC,KAAK,GAAG,KAAK;iBACV,SAAS,EAAE,CAAW,uBAAuB;iBAC7C,QAAQ,CAAC,GAAG,CAAC,CAAS,oBAAoB;iBAC1C,SAAS,EAAE,CAAC,CAAU,sBAAsB;YAE/C,MAAM,cAAc,GAAG,MAAM,KAAK,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YAC1D,MAAM,CAAC,KAAK,CAAC,oCAAoC,CAAC,CAAC;YAEnD,OAAO,cAAc,CAAC;QACxB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,wCAAwC,CAAC,CAAC;YAClE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,MAAc,EAAE,OAAe,GAAG;QACtD,IAAI,CAAC;YACH,MAAM,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,EAAE,oBAAoB,CAAC,CAAC;YAE7C,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACtC,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;YAErD,MAAM,eAAe,GAAG,MAAM,SAAS,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;YAChE,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;YAElC,OAAO,eAAe,CAAC;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,0BAA0B,CAAC,CAAC;YAC1D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,MAAc;QAM/B,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAEtC,OAAO;gBACL,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK;gBACzB,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,MAAM;gBAC3B,QAAQ,EAAE,KAAK,CAAC,IAAI,IAAI,eAAe;gBACvC,IAAI,EAAE,MAAM,CAAC,MAAM;aACpB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,0BAA0B,CAAC,CAAC;YACpD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,MAAc;QAChC,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACxB,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,yBAAyB,CAAC,CAAC;YACnD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,2EAA2E;IAC3E,KAAK,CAAC,oBAAoB,CAAC,MAAc;QACvC,IAAI,CAAC;YACH,MAAM,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;YAE3C,6DAA6D;YAC7D,oEAAoE;YACpE,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACtC,MAAM,WAAW,GAAG,MAAM,KAAK,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YAEvD,MAAM,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;YAC9D,OAAO,CAAC,WAAW,CAAC,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,kCAAkC,CAAC,CAAC;YAC5D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,+DAA+D;IAC/D,KAAK,CAAC,0BAA0B,CAAC,MAAc;QAC7C,IAAI,CAAC;YACH,MAAM,CAAC,KAAK,CAAC,0CAA0C,CAAC,CAAC;YAEzD,MAAM,UAAU,GAAwB;gBACtC,iBAAiB;gBACjB,EAAE;gBACF,gBAAgB;gBAChB,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE;gBACnD,eAAe;gBACf,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE;gBACpD,yBAAyB;gBACzB,EAAE,SAAS,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE;gBACrD,EAAE,SAAS,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE;gBACtD,uBAAuB;gBACvB,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,EAAE,UAAU,EAAE,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE;aACrE,CAAC;YAEF,MAAM,cAAc,GAAa,EAAE,CAAC;YAEpC,KAAK,MAAM,QAAQ,IAAI,UAAU,EAAE,CAAC;gBAClC,IAAI,CAAC;oBACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;oBAC9D,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAChC,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,MAAM,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE,6BAA6B,CAAC,CAAC;gBAClE,CAAC;YACH,CAAC;YAED,MAAM,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,cAAc,CAAC,MAAM,EAAE,EAAE,gCAAgC,CAAC,CAAC;YACjF,OAAO,cAAc,CAAC;QACxB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,uCAAuC,CAAC,CAAC;YACjE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF"}