import { ProcessingOptions } from '../types/index.js';
export declare class ImageProcessor {
    preprocessImage(buffer: Buffer, options?: ProcessingOptions): Promise<Buffer>;
    enhanceForQRDetection(buffer: Buffer): Promise<Buffer>;
    createThumbnail(buffer: Buffer, size?: number): Promise<Buffer>;
    getImageInfo(buffer: Buffer): Promise<{
        width: number;
        height: number;
        mimeType: string;
        size: number;
    }>;
    validateImage(buffer: Buffer): Promise<boolean>;
    extractFramesFromGif(buffer: Buffer): Promise<Buffer[]>;
    applyEnhancementStrategies(buffer: Buffer): Promise<Buffer[]>;
}
//# sourceMappingURL=image-processor.d.ts.map