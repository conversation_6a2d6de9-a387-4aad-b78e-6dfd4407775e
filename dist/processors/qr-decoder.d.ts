import { QRCodeResult } from '../types/index.js';
export declare class QRDecoder {
    decodeFromBuffer(buffer: Buffer): Promise<QRCodeResult | null>;
    decodeFromBase64(base64: string): Promise<QRCodeResult | null>;
    decodeFromPath(path: string): Promise<QRCodeResult | null>;
    decodeFromDataUri(dataUri: string): Promise<QRCodeResult | null>;
    decodeWithOptions(buffer: Buffer, options?: {
        inversionAttempts?: 'dontInvert' | 'onlyInvert' | 'attemptBoth';
        greyScaleWeights?: {
            red?: number;
            green?: number;
            blue?: number;
        };
    }): Promise<QRCodeResult | null>;
}
//# sourceMappingURL=qr-decoder.d.ts.map