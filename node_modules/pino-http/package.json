{"name": "pino-http", "version": "10.5.0", "description": "High-speed HTTP logger for Node.js", "main": "logger.js", "types": "index.d.ts", "dependencies": {"get-caller-file": "^2.0.5", "pino": "^9.0.0", "pino-std-serializers": "^7.0.0", "process-warning": "^5.0.0"}, "devDependencies": {"@fastify/pre-commit": "^2.0.2", "@types/node": "^22.0.0", "autocannon": "^8.0.0", "coveralls": "^3.0.0", "http-ndjson": "^3.1.0", "pino-http-print": "^3.1.0", "pino-pretty": "^13.0.0", "pre-commit": "^1.1.2", "split2": "^4.0.0", "standard": "^17.0.0", "tap": "^18.7.2", "ts-node": "^10.3.0", "tsd": "^0.32.0", "typescript": "~5.8.2"}, "scripts": {"benchmark": "bash ./scripts/benchmark-all", "test": "standard && tap test/*.js && npm run test-types", "fix": "standard --fix", "test-types": "tsc && tsd && ts-node *.test-d.ts "}, "author": "<PERSON>", "contributors": ["<PERSON> <<EMAIL>>"], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/pinojs/pino-http.git"}, "bugs": {"url": "https://github.com/pinojs/pino-http/issues"}, "homepage": "https://github.com/pinojs/pino-http#readme"}