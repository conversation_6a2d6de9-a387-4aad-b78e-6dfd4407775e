# strip-json-comments

> Strip comments from JSON. Lets you use comments in your JSON files!

This is now possible:

```js
{
	// Rainbows
	"unicorn": /* ❤ */ "cake"
}
```

It will replace single-line comments `//` and multi-line comments `/**/` with whitespace. This allows JSON error positions to remain as close as possible to the original source.

Also available as a [Gulp](https://github.com/sindresorhus/gulp-strip-json-comments)/[Grunt](https://github.com/sindresorhus/grunt-strip-json-comments)/[B<PERSON><PERSON><PERSON>](https://github.com/sindresorhus/broccoli-strip-json-comments) plugin.

## Install

```sh
npm install strip-json-comments
```

## Usage

```js
import stripJsonComments from 'strip-json-comments';

const json = `{
	// Rainbows
	"unicorn": /* ❤ */ "cake"
}`;

JSON.parse(stripJsonComments(json));
//=> {unicorn: 'cake'}
```

## API

### stripJsonComments(jsonString, options?)

#### jsonString

Type: `string`

Accepts a string with JSON and returns a string without comments.

#### options

Type: `object`

##### trailingCommas

Type: `boolean`\
Default: `false`

Strip trailing commas in addition to comments.

##### whitespace

Type: `boolean`\
Default: `true`

Replace comments and trailing commas with whitespace instead of stripping them entirely.

## Benchmark

```sh
npm run bench
```

## Related

- [strip-json-comments-cli](https://github.com/sindresorhus/strip-json-comments-cli) - CLI for this module
- [strip-css-comments](https://github.com/sindresorhus/strip-css-comments) - Strip comments from CSS
