{"name": "image-q", "version": "4.0.0", "description": "Image Quantization Library in **TypeScript** *(MIT Licensed)*", "license": "MIT", "contributors": [], "homepage": "https://github.com/ibezkrovnyi/image-quantization/tree/main/packages/image-q", "main": "./dist/cjs/image-q.cjs", "module": "./dist/esm/image-q.mjs", "types": "./dist/types/src/index.d.ts", "exports": {"types": "./dist/types/src/index.d.ts", "import": "./dist/esm/image-q.mjs", "require": "./dist/cjs/image-q.cjs", "default": "./dist/esm/image-q.mjs"}, "files": ["dist", "src"], "preferGlobal": false, "keywords": ["image", "palette", "quantization", "quantizer", "color", "neuquant", "rgbquant", "xiaolin wu", "cie94 distance", "ciede2000", "dithering", "floyd-steinberg"], "repository": {"type": "git", "url": "https://github.com/ibezkrovnyi/image-quantization"}, "bugs": {"url": "https://github.com/ibezkrovnyi/image-quantization/issues"}, "devDependencies": {"@types/pngjs": "3.3.0", "gh-pages": "3.2.3", "pngjs": "3.3.3", "typedoc": "0.22.10"}, "dependencies": {"@types/node": "16.9.1"}, "scripts": {"clean": "shx rm -rf ./node_modules/.cache", "build": "pnpm compile:types && pnpm compile:esm && pnpm compile:cjs && shx cp ../../README.md ./", "test": "jest", "gh-pages": "shx rm -rf ./docs && typedoc && gh-pages -d docs", "compile:types": "tsc -p tsconfig.compile.json", "compile:esm": "esbuild src/index.ts --outfile=dist/esm/image-q.mjs --tsconfig=tsconfig.compile.json --format=esm --platform=neutral --sourcemap --target=es2020,chrome58,node12 --bundle", "compile:cjs": "esbuild src/index.ts --outfile=dist/cjs/image-q.cjs --tsconfig=tsconfig.compile.json --format=cjs --platform=node --sourcemap --target=es2020,chrome58,node12 --bundle"}, "readme": "## image-q\n\nComplete Image Quantization Library in **TypeScript** _(MIT License)_\n\n[![Demo (outdated, use /packages/demo for up-to-date demo)](https://img.shields.io/badge/demo-online-brightgreen.svg)](https://ibezkrovnyi.github.io/image-quantization-demo/)\n[![GitHub](https://img.shields.io/badge/github-.com-brightgreen.svg)](https://github.com/ibezkrovnyi/image-quantization/tree/main/packages/image-q)\n[![NPM](https://badge.fury.io/js/image-q.svg)](https://www.npmjs.com/package/image-q)\n[![API)](https://img.shields.io/badge/API-Available-blue.svg)](http://ibezkrovnyi.github.io/image-quantization/)\n[![NPM License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)\n\n![quantization](packages/image-q/quantization.png)\n\n## Table of Contents\n\n- [<span style=\"color: red\">API Documentation and Usage</span>](http://ibezkrovnyi.github.io/image-quantization/)\n- [Introduction](#introduction)\n- [Features](#features)\n- [Todo](#todo)\n- [Breaking changes](#breaking-changes)\n- [Changelog](#changelog)\n- [Credits](#credits)\n- [References](#references)\n- [License](#license)\n\n## Introduction\n\nImage Color Number Reduction with alpha support using RGBQuant/NeuQuant/Xiaolin Wu's algorithms and Euclidean/Manhattan/CIEDE2000 color distance formulas in TypeScript\n\n## Features\n\n1. Platforms supported\n\n   - Browser (Chrome 7.0+, FireFox 4.0+, IE 10+, Opera 11.6+, Safari 5.1+)\n   - Node.js 6.0+\n\n2. API\n   - Basic API: sync and promise-based async\n   - Advanced API: sync and generator-based\n3. Builds\n\n   - `dist/cjs/image-q.js` - CommonJS\n   - `dist/esm/image-q.js` - ESM (ESNext)\n   - `dist/cjs/image-q.js` - UMD\n\n4. Import\n   - `HTMLImageElement`\n   - `HTMLCanvasElement`\n   - `NodeCanvas`\n   - `ImageData`\n   - `Array`\n   - `CanvasPixelArray`\n   - `Uint8Array`\n   - `Uint32Array`\n5. Color Distance\n\n   - `Euclidean` - 1/1/1/1 coefficients (originally used in Xiaolin Wu's Quantizer **WuQuant**)\n   - `EuclideanBT709NoAlpha` - BT.709 sRGB coefficients (originally used in **RGBQuant**)\n   - `EuclideanBT709` BT.709 sRGB coefficients + alpha support\n   - `Manhattan` - 1/1/1/1 coefficients (originally used in **NeuQuant**)\n   - `ManhattanBT709` - BT.709 sRGB coefficients\n   - `ManhattanNommyde` - see https://github.com/igor-bezkrovny/image-quantization/issues/4#issuecomment-234527620\n   - `CIEDE2000` - CIEDE2000 (very slow)\n   - `CIE94Textiles` - CIE94 implementation for textiles\n   - `CIE94GraphicArts` - CIE94 implementation for graphic arts\n   - `CMetric` - see http://www.compuphase.com/cmetric.htm\n   - `PNGQuant` - used in pngQuant tool\n\n6. Palette Quantizers\n   - `NeuQuant` (original code ported, integer calculations)\n   - `NeuQuantFloat` (floating-point calculations)\n   - `RGBQuant`\n   - `WuQuant`\n7. Image Quantizers\n\n   - `NearestColor`\n   - `ErrorDiffusionArray` - two modes of error propagation are supported: `xnview` and `gimp`\n     1. `FloydSteinberg`\n     2. `FalseFloydSteinberg`\n     3. `Stucki`\n     4. `Atkinson`\n     5. `Jarvis`\n     6. `Burkes`\n     7. `Sierra`\n     8. `TwoSierra`\n     9. `SierraLite`\n   - `ErrorDiffusionRiemersma` - Hilbert space-filling curve is used\n\n8. Output\n   - `Uint32Array`\n   - `Uint8Array`\n\n## Include `image-q` library into your project\n\n##### ES6 module\n\n```javascript\n// will import ESM (ESNext) or UMD version depending on your bundler/node\nimport * as iq from 'image-q';\n```\n\n##### CommonJS\n\n```javascript\nvar iq = require('image-q');\n```\n\n##### As a global variable (Browser)\n\n```html\n<script\n  src=\"<path-to image-q/dist/umd/image-q.js>\"\n  type=\"text/javascript\"\n  charset=\"utf-8\"\n></script>\n```\n\n## How to use\n\nPlease refer to [API Documentation and Usage](http://ibezkrovnyi.github.io/image-quantization/)\n\n## Breaking changes\n\n#### 2.1.1\n\n    + PaletteQuantizer#quantize => PaletteQuantizer#quantizeSync\n    + ImageQuantizer#quantize => ImageQuantizer#quantizeSync\n\n#### 2.0.1 - 2.0.4 (2018-02-22)\n\n    + EuclideanRgbQuantWOAlpha => EuclideanBT709NoAlpha\n    + EuclideanRgbQuantWithAlpha => EuclideanBT709\n    \t+ ManhattanSRGB => ManhattanBT709\n    \t+ IImageDitherer => AbstractImageQuantizer\n    \t+ IPaletteQuantizer => AbstractPaletteQuantizer\n    \t+ PointContainer.fromNodeCanvas => PointContainer.fromHTMLCanvasElement\n    \t+ PointContainer.fromArray => PointContainer.fromUint8Array\n    + PointContainer.fromBuffer (Node.js, new)\n    \t+ CMETRIC => CMetric\n    \t+ PNGQUANT => PNGQuant\n    \t+ SSIM Class => ssim function\n\n## TODO\n\n1. ~~notification about progress~~\n2. ~~riemersma dithering~~\n3. ordered dithering <-- is there anyone who needs it?\n4. readme update, more examples\n5. demo update (latest image-q npm version should be used in demo)\n\n## Changelog\n\n##### 4.0.0\n    + Test cases for different types of imports and requres added\n\n##### 4.0.0-alpha\n    + Try to solve exported bundle types problem. 'umd' bundle removed.\n\n##### 3.0.8\n    + Test case for issue #95 added\n\n##### 3.0.7\n    + Fixes #96: Fix minimumColorDistanceToDither (PR #97 by @pixelplanetdev)\n\n##### 3.0.6\n    + Fixes #95: \"Always empty result in certain webpack / babel configs\" (PR #98)\n\n##### 3.0.4\n    + Fixes issue \"Module not found: Can't resolve 'core-js/fn/set-immediate' in ...\"\n\n##### 3.0.0\n    + pnpm monorepo, esbuild for faster builds, typescript upgraded\n\n##### 2.1.1\n\n    + Basic (Simple) API implemented\n    + see breaking changes\n\n##### 2.0.5 (2018-02-23)\n\n    + @types/node moved to 'dependencies'\n\n##### 2.0.4 (2018-02-23)\n\n    + documentation added\n    + some refactorings/renames, see breaking changes\n\n##### 2.0.3 (2018-02-22)\n\n    + circular dependency removed\n\n##### 2.0.2 (2018-02-22)\n\n    + readme updated\n\n##### 2.0.1 (2018-02-22)\n\n    + progress tracking api (using es6 generators) added\n    + strinct lint rules (+code cleanup/renames)\n    + rollup (3 different versions - umd, cjs, esm + source maps + d.ts)\n    + latest TypeScript\n    + jest\n    + snapshot tests\n    + coverage (+coveralls)\n    + greenkeeper\n\n##### 1.1.1 (2016-08-28)\n\n    + CIEDE2000 - incorrect calculation fixed\n    + CIEDE2000 - alpha channel now has only 25% impact on color distance instead of 66%\n    + CIE94 - added 2 types (textiles and graphics art) according to spec\n    + CIE94 - alpha support added\n    + rgb2xyz, lab2xyz, xyz2rgb, xyz2lab - gamma correction\n    + lab2xyz, xyz2lab - refY should be 100 (1.00000) instead of 10 (0.10000)\n    + manhattan with new (Nommyde) coefficients added\n    + mocha tests added\n    + webpack integration\n    + image-q is now UMD module\n    + travis-ci integration\n    + typescript 2.0\n    + indentation with 4 spaces\n\n##### 0.1.4 (2015-06-24)\n\n    + Refactoring\n    + Riemersma dithering added (Hilbert Curve)\n    + Readme.md updated\n    + build.cmd updated\n\n##### 0.1.3 (2015-06-16)\n\n    + NeuQuant is fixed (again) according to original Anthony Dekker source code (all values should be integer)\n    + Error Diffusion Dithering is now calculates error like XNVIEW\n    + Refactoring\n\n##### 0.1.2 (2015-06-16)\n\n    + Documentation generation fixed\n    + File name case problem fixed\n\n##### 0.1.1 (2015-06-16)\n\n    + Auto-generated documentation added\n    + Refactoring\n\n##### 0.1.0 (2015-06-16)\n\n    + Code cleanup, removed unnecessary files\n\n##### 0.0.5 (2015-06-16)\n\n    + PNGQuant color distance added, need to check its quality\n    + CIEDE2000 and CIE94 fixed for use in NeuQuant\n    + NeuQuant is fixed according to original Anthony Dekker source code (all values should be integer)\n    + Code refactoring and cleanup\n    * We have some slowdown because of red/green/blue/alpha normalization according to white point per each calculateRaw/calculateNormalized call\n\n##### 0.0.4 (2015-06-15)\n\n    + CIEDE2000 color distance equation optimized (original CIEDE2000 equation is available as class `CIEDE2000_Original`)\n\n##### 0.0.3b (2015-06-11)\n\n    + CMetric color distance fixed\n\n##### 0.0.3a (2015-06-11)\n\n    + Cleanup\n    + Draft of CMetric color distance added\n\n##### 0.0.2 (2015-06-10)\n\n    + rgb2xyz & xyz2lab fixed. CIEDE2000 works much better now.\n    + CIE94 distance formula added. More investigation is needed.\n\n##### 0.0.1\n\n    + Initial\n\n## Credits\n\nThanks to Leon Sorokin for information share and his original RGBQuant!\n\n## References\n\n- Palette Quantization Algorithms\n\n  1.  [RGBQuant (Leon Sorokin)](https://github.com/leeoniya/RgbQuant.js) `JavaScript`\n  2.  [NeuQuant (Johan Nordberg)](https://github.com/jnordberg/gif.js/blob/master/src/TypedNeuQuant.js) `TypeScript`\n  3.  [NeuQuant (Tim Oxley)](https://github.com/timoxley/neuquant) `JavaScript`\n  4.  [NeuQuant (Devon Govett)](https://github.com/devongovett/neuquant) `JavaScript`\n  5.  [NeuQuant32 (Stuart Coyle)](https://github.com/stuart/pngnq/blob/master/src/neuquant32.c) `C`\n  6.  [Xiaolin Wu (Xiaolin Wu)](http://www.ece.mcmaster.ca/~xwu/cq.c) `C`\n  7.  [Xiaolin Wu (Smart-K8)](http://www.codeproject.com/Articles/66341/A-Simple-Yet-Quite-Powerful-Palette-Quantizer-in-C) `C#`\n  8.  Xiaolin Wu w/ Alpha (Matt Wrock) [How to add Alpha](https://code.msdn.microsoft.com/windowsdesktop/Convert-32-bit-PNGs-to-81ef8c81/view/SourceCode#content), [Source Code](https://nquant.codeplex.com) `C#`\n  9.  [MedianCut (mwcz)](https://github.com/mwcz/median-cut-js) `GPLv3`\n\n- Image Quantization Algorithms\n\n  1.  [All (ImageMagik doc)](http://www.imagemagick.org/Usage/quantize/#dither)\n  2.  [Error Diffusion dithering (Tanner Helland)](http://www.tannerhelland.com/4660/dithering-eleven-algorithms-source-code)\n  3.  [Riemersma dithering](http://www.compuphase.com/riemer.htm) `TODO: Check License`\n  4.  [Ordered dithering (Joel Yliluoma)](http://bisqwit.iki.fi/story/howto/dither/jy)\n\n- Color Distance Formulas\n\n  [Calculator + Info](http://colorizer.org/)\n\n  1.  Euclidean Distance\n  2.  Manhattan Distance\n  3.  CIE94 Distance\n      - [Source Code (Iulius Curt)](https://github.com/iuliux/CIE94.js)\n  4.  CIEDE2000\n      - [Math and Test Data Table (PDF)](http://www.ece.rochester.edu/~gsharma/ciede2000/ciede2000noteCRNA.pdf)\n      - [Info](http://www.ece.rochester.edu/~gsharma/ciede2000/)\n      - [Source Code (Greg Fiumara)](https://github.com/gfiumara/CIEDE2000) `C`\n      - [Source Code (THEjoezack)](https://github.com/THEjoezack/ColorMine/blob/master/ColorMine/ColorSpaces/Comparisons/CieDe2000Comparison.cs) `C#`\n      - [Online Calculator](http://colormine.org/delta-e-calculator/cie2000)\n  5.  Euclidean Distance w/o Alpha (RGBQuant)\n  6.  Euclidean Distance w/o sRGB coefficients (Xiaolin Wu Quant)\n  7.  Manhattan Distance w/o sRGB coefficients (NeuQuant)\n  8.  [CMetric](http://www.compuphase.com/cmetric.htm) `DRAFT!`\n\n- Color conversion formulas\n\n  1.  [Pseudo-code](http://www.easyrgb.com/?X=MATH)\n\n> Be sure to fix rgb2xyz/xyz2lab. Issue is with strange part of code: `r = r > 0.04045 ? ...`. Check http://en.wikipedia.org/wiki/Lab_color_space\n\n- Image Quality Assessment\n\n  1.  [SSIM info](http://en.wikipedia.org/wiki/Structural_similarity)\n  2.  [SSIM (Rhys-e)](https://github.com/rhys-e/structural-similarity) `Java` `License: MIT`\n  3.  PSNR ? TBD\n  4.  MSE ? TBD\n\n- Other\n\n  1.  [HUSL (Boronine) - info](http://www.husl-colors.org)\n  2.  [HUSL (Boronine) - code](https://github.com/husl-colors/husl)\n  3.  [Color Image Quantization for Frame Buffer Display](https://www.cs.cmu.edu/~ph/ciq_thesis)\n  4.  [K-Means](http://arxiv.org/pdf/1101.0395.pdf)\n  5.  [Efficient Color Quantization by Hierarchical Clustering Algorithms](ftp://cs.joensuu.fi/pub/Theses/2005_MSc_Hautamaki_Ville.pdf)\n  6.  http://www.codeproject.com/Articles/66341/A-Simple-Yet-Quite-Powerful-Palette-Quantizer-in-C\n\n## License\n\n[MIT](LICENSE)\n"}