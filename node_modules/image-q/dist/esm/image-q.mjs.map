{"version": 3, "sources": ["../../src/constants/index.ts", "../../src/constants/bt709.ts", "../../src/conversion/index.ts", "../../src/conversion/rgb2xyz.ts", "../../src/utils/arithmetic.ts", "../../src/conversion/rgb2hsl.ts", "../../src/conversion/xyz2lab.ts", "../../src/conversion/rgb2lab.ts", "../../src/conversion/lab2xyz.ts", "../../src/conversion/xyz2rgb.ts", "../../src/conversion/lab2rgb.ts", "../../src/distance/index.ts", "../../src/distance/distanceCalculator.ts", "../../src/distance/cie94.ts", "../../src/distance/ciede2000.ts", "../../src/distance/cmetric.ts", "../../src/distance/euclidean.ts", "../../src/distance/manhattan.ts", "../../src/distance/pngQuant.ts", "../../src/palette/index.ts", "../../src/palette/paletteQuantizer.ts", "../../src/utils/point.ts", "../../src/utils/pointContainer.ts", "../../src/utils/palette.ts", "../../src/utils/index.ts", "../../src/utils/hueStatistics.ts", "../../src/utils/progressTracker.ts", "../../src/palette/neuquant/neuquant.ts", "../../src/palette/neuquant/neuquantFloat.ts", "../../src/palette/rgbquant/colorHistogram.ts", "../../src/palette/rgbquant/rgbquant.ts", "../../src/palette/wu/wuQuant.ts", "../../src/image/index.ts", "../../src/image/imageQuantizer.ts", "../../src/image/nearestColor.ts", "../../src/image/array.ts", "../../src/image/spaceFillingCurves/hilbertCurve.ts", "../../src/image/riemersma.ts", "../../src/quality/index.ts", "../../src/quality/ssim.ts", "../../src/basicAPI.ts", "../../src/index.ts"], "sourcesContent": ["/**\n * @preserve\n * Copyright 2015-2018 <PERSON>\n * All rights reserved. (MIT Licensed)\n *\n * constants.ts - part of Image Quantization Library\n */\nimport * as bt709 from './bt709';\n\nexport { bt709 };\n", "/**\n * @preserve\n * Copyright 2015-2018 <PERSON>\n * All rights reserved. (MIT Licensed)\n *\n * constants.ts - part of Image Quantization Library\n */\n\n/**\n * sRGB (based on ITU-R Recommendation BT.709)\n * http://en.wikipedia.org/wiki/SRGB\n */\nexport enum Y {\n  RED = 0.2126,\n  GREEN = 0.7152,\n  BLUE = 0.0722,\n  WHITE = 1,\n}\n\nexport enum x {\n  RED = 0.64,\n  GREEN = 0.3,\n  BLUE = 0.15,\n  WHITE = 0.3127,\n}\n\nexport enum y {\n  RED = 0.33,\n  GREEN = 0.6,\n  BLUE = 0.06,\n  WHITE = 0.329,\n}\n", "/**\n * @preserve\n * Copyright 2015-2018 <PERSON>\n * All rights reserved. (MIT Licensed)\n *\n * iq.ts - Image Quantization Library\n */\nexport { rgb2xyz } from './rgb2xyz';\nexport { rgb2hsl } from './rgb2hsl';\nexport { rgb2lab } from './rgb2lab';\nexport { lab2xyz } from './lab2xyz';\nexport { lab2rgb } from './lab2rgb';\nexport { xyz2lab } from './xyz2lab';\nexport { xyz2rgb } from './xyz2rgb';\n", "/**\n * @preserve\n * Copyright 2015-2018 <PERSON>\n * All rights reserved. (MIT Licensed)\n *\n * rgb2xyz.ts - part of Image Quantization Library\n */\nfunction correctGamma(n: number) {\n  return n > 0.04045 ? ((n + 0.055) / 1.055) ** 2.4 : n / 12.92;\n}\n\nexport function rgb2xyz(r: number, g: number, b: number) {\n  // gamma correction, see https://en.wikipedia.org/wiki/SRGB#The_reverse_transformation\n  r = correctGamma(r / 255);\n  g = correctGamma(g / 255);\n  b = correctGamma(b / 255);\n\n  // Observer. = 2°, Illuminant = D65\n  return {\n    x: r * 0.4124 + g * 0.3576 + b * 0.1805,\n    y: r * 0.2126 + g * 0.7152 + b * 0.0722,\n    z: r * 0.0193 + g * 0.1192 + b * 0.9505,\n  };\n}\n", "export function degrees2radians(n: number) {\n  return n * (Math.PI / 180);\n}\n\nexport function max3(a: number, b: number, c: number) {\n  let m = a;\n  if (m < b) m = b;\n  if (m < c) m = c;\n  return m;\n}\n\nexport function min3(a: number, b: number, c: number) {\n  let m = a;\n  if (m > b) m = b;\n  if (m > c) m = c;\n  return m;\n}\n\nexport function intInRange(value: number, low: number, high: number) {\n  if (value > high) value = high;\n  if (value < low) value = low;\n  return value | 0;\n}\n\nexport function inRange0to255Rounded(n: number) {\n  n = Math.round(n);\n  if (n > 255) n = 255;\n  else if (n < 0) n = 0;\n  return n;\n}\n\nexport function inRange0to255(n: number) {\n  if (n > 255) n = 255;\n  else if (n < 0) n = 0;\n  return n;\n}\n\nexport function stableSort<T>(\n  arrayToSort: T[],\n  callback: (a: T, b: T) => number,\n) {\n  const type = typeof arrayToSort[0];\n  let sorted: T[];\n\n  if (type === 'number' || type === 'string') {\n    const ord = Object.create(null);\n    for (let i = 0, l = arrayToSort.length; i < l; i++) {\n      const val = arrayToSort[i] as unknown as string;\n      if (ord[val] || ord[val] === 0) continue;\n      ord[val] = i;\n    }\n\n    sorted = arrayToSort.sort((a, b) => callback(a, b) || ord[a] - ord[b]);\n  } else {\n    const ord2 = arrayToSort.slice(0);\n    sorted = arrayToSort.sort(\n      (a, b) => callback(a, b) || ord2.indexOf(a) - ord2.indexOf(b),\n    );\n  }\n\n  return sorted;\n}\n", "/**\n * @preserve\n * Copyright 2015-2018 <PERSON>\n * All rights reserved. (MIT Licensed)\n *\n * rgb2hsl.ts - part of Image Quantization Library\n */\nimport { min3, max3 } from '../utils/arithmetic';\n\n/**\n * Calculate HSL from RGB\n * Hue is in degrees [0..360]\n * Lightness: [0..1]\n * Saturation: [0..1]\n * http://web.archive.org/web/20060914040436/http://local.wasp.uwa.edu.au/~pbourke/colour/hsl/\n */\nexport function rgb2hsl(r: number, g: number, b: number) {\n  const min = min3(r, g, b);\n  const max = max3(r, g, b);\n  const delta = max - min;\n  const l = (min + max) / 510;\n\n  let s = 0;\n  if (l > 0 && l < 1) s = delta / (l < 0.5 ? max + min : 510 - max - min);\n\n  let h = 0;\n  if (delta > 0) {\n    if (max === r) {\n      h = (g - b) / delta;\n    } else if (max === g) {\n      h = 2 + (b - r) / delta;\n    } else {\n      h = 4 + (r - g) / delta;\n    }\n\n    h *= 60;\n    if (h < 0) h += 360;\n  }\n  return { h, s, l };\n}\n", "/**\n * @preserve\n * Copyright 2015-2018 <PERSON>\n * All rights reserved. (MIT Licensed)\n *\n * xyz2lab.ts - part of Image Quantization Library\n */\nconst refX = 0.95047; // ref_X =  95.047   Observer= 2°, Illuminant= D65\nconst refY = 1.0; // ref_Y = 100.000\nconst refZ = 1.08883; // ref_Z = 108.883\n\nfunction pivot(n: number) {\n  return n > 0.008856 ? n ** (1 / 3) : 7.787 * n + 16 / 116;\n}\n\nexport function xyz2lab(x: number, y: number, z: number) {\n  x = pivot(x / refX);\n  y = pivot(y / refY);\n  z = pivot(z / refZ);\n\n  if (116 * y - 16 < 0) throw new Error('xxx');\n  return {\n    L: Math.max(0, 116 * y - 16),\n    a: 500 * (x - y),\n    b: 200 * (y - z),\n  };\n}\n", "/**\n * @preserve\n * Copyright 2015-2018 <PERSON>\n * All rights reserved. (MIT Licensed)\n *\n * rgb2lab.ts - part of Image Quantization Library\n */\nimport { rgb2xyz } from './rgb2xyz';\nimport { xyz2lab } from './xyz2lab';\n\nexport function rgb2lab(r: number, g: number, b: number) {\n  const xyz = rgb2xyz(r, g, b);\n  return xyz2lab(xyz.x, xyz.y, xyz.z);\n}\n", "/**\n * @preserve\n * Copyright 2015-2018 <PERSON>\n * All rights reserved. (MIT Licensed)\n *\n * lab2xyz.ts - part of Image Quantization Library\n */\nconst refX = 0.95047; // ref_X =  95.047   Observer= 2°, Illuminant = D65\nconst refY = 1.0; // ref_Y = 100.000\nconst refZ = 1.08883; // ref_Z = 108.883\n\nfunction pivot(n: number) {\n  return n > 0.206893034 ? n ** 3 : (n - 16 / 116) / 7.787;\n}\n\nexport function lab2xyz(L: number, a: number, b: number) {\n  const y = (L + 16) / 116;\n  const x = a / 500 + y;\n  const z = y - b / 200;\n\n  return {\n    x: refX * pivot(x),\n    y: refY * pivot(y),\n    z: refZ * pivot(z),\n  };\n}\n", "/**\n * @preserve\n * Copyright 2015-2018 <PERSON>\n * All rights reserved. (MIT Licensed)\n *\n * xyz2rgb.ts - part of Image Quantization Library\n */\nimport { inRange0to255Rounded } from '../utils/arithmetic';\n\n// gamma correction, see https://en.wikipedia.org/wiki/SRGB#The_reverse_transformation\nfunction correctGamma(n: number) {\n  return n > 0.0031308 ? 1.055 * n ** (1 / 2.4) - 0.055 : 12.92 * n;\n}\n\nexport function xyz2rgb(x: number, y: number, z: number) {\n  // Observer. = 2°, Illuminant = D65\n  const r = correctGamma(x * 3.2406 + y * -1.5372 + z * -0.4986);\n  const g = correctGamma(x * -0.9689 + y * 1.8758 + z * 0.0415);\n  const b = correctGamma(x * 0.0557 + y * -0.204 + z * 1.057);\n\n  return {\n    r: inRange0to255Rounded(r * 255),\n    g: inRange0to255Rounded(g * 255),\n    b: inRange0to255Rounded(b * 255),\n  };\n}\n", "/**\n * @preserve\n * Copyright 2015-2018 <PERSON>\n * All rights reserved. (MIT Licensed)\n *\n * lab2rgb.ts - part of Image Quantization Library\n */\nimport { lab2xyz } from './lab2xyz';\nimport { xyz2rgb } from './xyz2rgb';\n\nexport function lab2rgb(L: number, a: number, b: number) {\n  const xyz = lab2xyz(L, a, b);\n  return xyz2rgb(xyz.x, xyz.y, xyz.z);\n}\n", "/**\n * @preserve\n * Copyright 2015-2018 <PERSON>\n * All rights reserved. (MIT Licensed)\n *\n * iq.ts - Image Quantization Library\n */\nexport { AbstractDistanceCalculator } from './distanceCalculator';\nexport { CIE94Textiles, CIE94GraphicArts } from './cie94';\nexport { CIEDE2000 } from './ciede2000';\nexport { CMetric } from './cmetric';\nexport {\n  AbstractEuclidean,\n  Euclidean,\n  EuclideanBT709NoAlpha,\n  EuclideanBT709,\n} from './euclidean';\nexport {\n  AbstractManhattan,\n  Manhattan,\n  ManhattanBT709,\n  ManhattanNommyde,\n} from './manhattan';\nexport { PNGQuant } from './pngQuant';\n", "/**\n * @preserve\n * Copyright 2015-2018 <PERSON>\n * All rights reserved. (MIT Licensed)\n *\n * common.ts - part of Image Quantization Library\n */\nimport { PointRGBA } from '../utils/point';\n\nexport abstract class AbstractDistanceCalculator {\n  protected _maxDistance!: number;\n  protected _whitePoint!: {\n    r: number;\n    g: number;\n    b: number;\n    a: number;\n  };\n\n  constructor() {\n    this._setDefaults();\n\n    // set default maximal color component deltas (255 - 0 = 255)\n    this.setWhitePoint(255, 255, 255, 255);\n  }\n\n  setWhitePoint(r: number, g: number, b: number, a: number) {\n    this._whitePoint = {\n      r: r > 0 ? 255 / r : 0,\n      g: g > 0 ? 255 / g : 0,\n      b: b > 0 ? 255 / b : 0,\n      a: a > 0 ? 255 / a : 0,\n    };\n    this._maxDistance = this.calculateRaw(r, g, b, a, 0, 0, 0, 0);\n  }\n\n  calculateNormalized(colorA: PointRGBA, colorB: PointRGBA) {\n    return (\n      this.calculateRaw(\n        colorA.r,\n        colorA.g,\n        colorA.b,\n        colorA.a,\n        colorB.r,\n        colorB.g,\n        colorB.b,\n        colorB.a,\n      ) / this._maxDistance\n    );\n  }\n\n  /**\n   * Calculate raw distance (non-normalized)\n   */\n  abstract calculateRaw(\n    r1: number,\n    g1: number,\n    b1: number,\n    a1: number,\n    r2: number,\n    g2: number,\n    b2: number,\n    a2: number,\n  ): number;\n\n  protected abstract _setDefaults(): void;\n}\n", "/**\n * @preserve\n * Copyright 2015-2018 <PERSON>\n * All rights reserved. (MIT Licensed)\n *\n * cie94.ts - part of Image Quantization Library\n */\nimport { AbstractDistanceCalculator } from './distanceCalculator';\nimport { rgb2lab } from '../conversion/rgb2lab';\nimport { inRange0to255 } from '../utils/arithmetic';\n\n/**\n * CIE94 method of delta-e\n * http://en.wikipedia.org/wiki/Color_difference#CIE94\n */\nexport abstract class AbstractCIE94 extends AbstractDistanceCalculator {\n  /**\n   * Weight in distance: 0.25\n   * Max DeltaE: 100\n   * Max DeltaA: 255\n   */\n  declare protected _kA: number;\n  declare protected _Kl: number;\n  declare protected _K1: number;\n  declare protected _K2: number;\n\n  calculateRaw(\n    r1: number,\n    g1: number,\n    b1: number,\n    a1: number,\n    r2: number,\n    g2: number,\n    b2: number,\n    a2: number,\n  ) {\n    const lab1 = rgb2lab(\n      inRange0to255(r1 * this._whitePoint.r),\n      inRange0to255(g1 * this._whitePoint.g),\n      inRange0to255(b1 * this._whitePoint.b),\n    );\n    const lab2 = rgb2lab(\n      inRange0to255(r2 * this._whitePoint.r),\n      inRange0to255(g2 * this._whitePoint.g),\n      inRange0to255(b2 * this._whitePoint.b),\n    );\n\n    const dL = lab1.L - lab2.L;\n    const dA = lab1.a - lab2.a;\n    const dB = lab1.b - lab2.b;\n    const c1 = Math.sqrt(lab1.a * lab1.a + lab1.b * lab1.b);\n    const c2 = Math.sqrt(lab2.a * lab2.a + lab2.b * lab2.b);\n    const dC = c1 - c2;\n\n    let deltaH = dA * dA + dB * dB - dC * dC;\n    deltaH = deltaH < 0 ? 0 : Math.sqrt(deltaH);\n\n    const dAlpha = (a2 - a1) * this._whitePoint.a * this._kA;\n\n    // TODO: add alpha channel support\n    return Math.sqrt(\n      (dL / this._Kl) ** 2 +\n        (dC / (1.0 + this._K1 * c1)) ** 2 +\n        (deltaH / (1.0 + this._K2 * c1)) ** 2 +\n        dAlpha ** 2,\n    );\n  }\n}\n\nexport class CIE94Textiles extends AbstractCIE94 {\n  protected _setDefaults() {\n    this._Kl = 2.0;\n    this._K1 = 0.048;\n    this._K2 = 0.014;\n    this._kA = (0.25 * 50) / 255;\n  }\n}\n\nexport class CIE94GraphicArts extends AbstractCIE94 {\n  protected _setDefaults() {\n    this._Kl = 1.0;\n    this._K1 = 0.045;\n    this._K2 = 0.015;\n    this._kA = (0.25 * 100) / 255;\n  }\n}\n", "/**\n * @preserve\n * Copyright 2015-2018 <PERSON>\n * All rights reserved. (MIT Licensed)\n *\n * ciede2000.ts - part of Image Quantization Library\n */\nimport { AbstractDistanceCalculator } from './distanceCalculator';\nimport { rgb2lab } from '../conversion/rgb2lab';\nimport { degrees2radians, inRange0to255 } from '../utils/arithmetic';\n\n/**\n * CIEDE2000 algorithm - Adapted from <PERSON> et al's MATLAB implementation at\n * http://www.ece.rochester.edu/~gsharma/ciede2000/\n */\nexport class CIEDE2000 extends AbstractDistanceCalculator {\n  /**\n   * Weight in distance: 0.25\n   * Max DeltaE: 100\n   * Max DeltaA: 255\n   */\n  private static readonly _kA = (0.25 * 100) / 255;\n  private static readonly _pow25to7 = 25 ** 7;\n  private static readonly _deg360InRad = degrees2radians(360);\n  private static readonly _deg180InRad = degrees2radians(180);\n  private static readonly _deg30InRad = degrees2radians(30);\n  private static readonly _deg6InRad = degrees2radians(6);\n  private static readonly _deg63InRad = degrees2radians(63);\n  private static readonly _deg275InRad = degrees2radians(275);\n  private static readonly _deg25InRad = degrees2radians(25);\n\n  protected _setDefaults() {}\n\n  private static _calculatehp(b: number, ap: number) {\n    const hp = Math.atan2(b, ap);\n    if (hp >= 0) return hp;\n    return hp + CIEDE2000._deg360InRad;\n  }\n\n  private static _calculateRT(ahp: number, aCp: number) {\n    const aCp_to_7 = aCp ** 7.0;\n    const R_C = 2.0 * Math.sqrt(aCp_to_7 / (aCp_to_7 + CIEDE2000._pow25to7)); // 25^7\n    const delta_theta =\n      CIEDE2000._deg30InRad *\n      Math.exp(\n        -(((ahp - CIEDE2000._deg275InRad) / CIEDE2000._deg25InRad) ** 2.0),\n      );\n    return -Math.sin(2.0 * delta_theta) * R_C;\n  }\n\n  private static _calculateT(ahp: number) {\n    return (\n      1.0 -\n      0.17 * Math.cos(ahp - CIEDE2000._deg30InRad) +\n      0.24 * Math.cos(ahp * 2.0) +\n      0.32 * Math.cos(ahp * 3.0 + CIEDE2000._deg6InRad) -\n      0.2 * Math.cos(ahp * 4.0 - CIEDE2000._deg63InRad)\n    );\n  }\n\n  private static _calculate_ahp(\n    C1pC2p: number,\n    h_bar: number,\n    h1p: number,\n    h2p: number,\n  ) {\n    const hpSum = h1p + h2p;\n    if (C1pC2p === 0) return hpSum;\n    if (h_bar <= CIEDE2000._deg180InRad) return hpSum / 2.0;\n    if (hpSum < CIEDE2000._deg360InRad) {\n      return (hpSum + CIEDE2000._deg360InRad) / 2.0;\n    }\n    return (hpSum - CIEDE2000._deg360InRad) / 2.0;\n  }\n\n  private static _calculate_dHp(\n    C1pC2p: number,\n    h_bar: number,\n    h2p: number,\n    h1p: number,\n  ) {\n    let dhp;\n    if (C1pC2p === 0) {\n      dhp = 0;\n    } else if (h_bar <= CIEDE2000._deg180InRad) {\n      dhp = h2p - h1p;\n    } else if (h2p <= h1p) {\n      dhp = h2p - h1p + CIEDE2000._deg360InRad;\n    } else {\n      dhp = h2p - h1p - CIEDE2000._deg360InRad;\n    }\n    return 2.0 * Math.sqrt(C1pC2p) * Math.sin(dhp / 2.0);\n  }\n\n  calculateRaw(\n    r1: number,\n    g1: number,\n    b1: number,\n    a1: number,\n    r2: number,\n    g2: number,\n    b2: number,\n    a2: number,\n  ) {\n    const lab1 = rgb2lab(\n      inRange0to255(r1 * this._whitePoint.r),\n      inRange0to255(g1 * this._whitePoint.g),\n      inRange0to255(b1 * this._whitePoint.b),\n    );\n    const lab2 = rgb2lab(\n      inRange0to255(r2 * this._whitePoint.r),\n      inRange0to255(g2 * this._whitePoint.g),\n      inRange0to255(b2 * this._whitePoint.b),\n    );\n    const dA = (a2 - a1) * this._whitePoint.a * CIEDE2000._kA;\n    const dE2 = this.calculateRawInLab(lab1, lab2);\n\n    return Math.sqrt(dE2 + dA * dA);\n  }\n\n  calculateRawInLab(\n    Lab1: { L: number; a: number; b: number },\n    Lab2: { L: number; a: number; b: number },\n  ) {\n    // Get L,a,b values for color 1\n    const L1 = Lab1.L;\n    const a1 = Lab1.a;\n    const b1 = Lab1.b;\n\n    // Get L,a,b values for color 2\n    const L2 = Lab2.L;\n    const a2 = Lab2.a;\n    const b2 = Lab2.b;\n\n    // Calculate Cprime1, Cprime2, Cabbar\n    const C1 = Math.sqrt(a1 * a1 + b1 * b1);\n    const C2 = Math.sqrt(a2 * a2 + b2 * b2);\n    const pow_a_C1_C2_to_7 = ((C1 + C2) / 2.0) ** 7.0;\n\n    const G =\n      0.5 *\n      (1.0 -\n        Math.sqrt(pow_a_C1_C2_to_7 / (pow_a_C1_C2_to_7 + CIEDE2000._pow25to7))); // 25^7\n    const a1p = (1.0 + G) * a1;\n    const a2p = (1.0 + G) * a2;\n\n    const C1p = Math.sqrt(a1p * a1p + b1 * b1);\n    const C2p = Math.sqrt(a2p * a2p + b2 * b2);\n    const C1pC2p = C1p * C2p;\n\n    // Angles in Degree.\n    const h1p = CIEDE2000._calculatehp(b1, a1p);\n    const h2p = CIEDE2000._calculatehp(b2, a2p);\n    const h_bar = Math.abs(h1p - h2p);\n    const dLp = L2 - L1;\n    const dCp = C2p - C1p;\n    const dHp = CIEDE2000._calculate_dHp(C1pC2p, h_bar, h2p, h1p);\n    const ahp = CIEDE2000._calculate_ahp(C1pC2p, h_bar, h1p, h2p);\n\n    const T = CIEDE2000._calculateT(ahp);\n\n    const aCp = (C1p + C2p) / 2.0;\n    const aLp_minus_50_square = ((L1 + L2) / 2.0 - 50.0) ** 2.0;\n    const S_L =\n      1.0 +\n      (0.015 * aLp_minus_50_square) / Math.sqrt(20.0 + aLp_minus_50_square);\n    const S_C = 1.0 + 0.045 * aCp;\n    const S_H = 1.0 + 0.015 * T * aCp;\n\n    const R_T = CIEDE2000._calculateRT(ahp, aCp);\n\n    const dLpSL = dLp / S_L; // S_L * kL, where kL is 1.0\n    const dCpSC = dCp / S_C; // S_C * kC, where kC is 1.0\n    const dHpSH = dHp / S_H; // S_H * kH, where kH is 1.0\n\n    return dLpSL ** 2 + dCpSC ** 2 + dHpSH ** 2 + R_T * dCpSC * dHpSH;\n  }\n}\n", "/**\n * @preserve\n * Copyright 2015-2018 <PERSON>\n * All rights reserved. (MIT Licensed)\n *\n * cmetric.ts - part of Image Quantization Library\n */\nimport { AbstractDistanceCalculator } from './distanceCalculator';\n\n/**\n * TODO: Name it: http://www.compuphase.com/cmetric.htm\n */\nexport class CMetric extends AbstractDistanceCalculator {\n  calculateRaw(\n    r1: number,\n    g1: number,\n    b1: number,\n    a1: number,\n    r2: number,\n    g2: number,\n    b2: number,\n    a2: number,\n  ) {\n    const rmean = ((r1 + r2) / 2) * this._whitePoint.r;\n    const r = (r1 - r2) * this._whitePoint.r;\n    const g = (g1 - g2) * this._whitePoint.g;\n    const b = (b1 - b2) * this._whitePoint.b;\n    const dE =\n      (((512 + rmean) * r * r) >> 8) +\n      4 * g * g +\n      (((767 - rmean) * b * b) >> 8);\n    const dA = (a2 - a1) * this._whitePoint.a;\n\n    return Math.sqrt(dE + dA * dA);\n  }\n\n  protected _setDefaults() {}\n}\n", "/**\n * @preserve\n * Copyright 2015-2018 <PERSON>\n * All rights reserved. (MIT Licensed)\n *\n * euclidean.ts - part of Image Quantization Library\n */\nimport { AbstractDistanceCalculator } from './distanceCalculator';\nimport { Y } from '../constants/bt709';\n\n/**\n * Euclidean color distance\n */\nexport abstract class AbstractEuclidean extends AbstractDistanceCalculator {\n  declare protected _kR: number;\n  declare protected _kG: number;\n  declare protected _kB: number;\n  declare protected _kA: number;\n\n  calculateRaw(\n    r1: number,\n    g1: number,\n    b1: number,\n    a1: number,\n    r2: number,\n    g2: number,\n    b2: number,\n    a2: number,\n  ) {\n    const dR = r2 - r1;\n    const dG = g2 - g1;\n    const dB = b2 - b1;\n    const dA = a2 - a1;\n    return Math.sqrt(\n      this._kR * dR * dR +\n        this._kG * dG * dG +\n        this._kB * dB * dB +\n        this._kA * dA * dA,\n    );\n  }\n}\n\nexport class Euclidean extends AbstractEuclidean {\n  protected _setDefaults() {\n    this._kR = 1;\n    this._kG = 1;\n    this._kB = 1;\n    this._kA = 1;\n  }\n}\n\n/**\n * Euclidean color distance (RGBQuant modification w Alpha)\n */\nexport class EuclideanBT709 extends AbstractEuclidean {\n  protected _setDefaults() {\n    this._kR = Y.RED;\n    this._kG = Y.GREEN;\n    this._kB = Y.BLUE;\n    // TODO: what is the best coefficient below?\n    this._kA = 1;\n  }\n}\n\n/**\n * Euclidean color distance (RGBQuant modification w/o Alpha)\n */\nexport class EuclideanBT709NoAlpha extends AbstractEuclidean {\n  protected _setDefaults() {\n    this._kR = Y.RED;\n    this._kG = Y.GREEN;\n    this._kB = Y.BLUE;\n    this._kA = 0;\n  }\n}\n", "/**\n * @preserve\n * Copyright 2015-2018 <PERSON>\n * All rights reserved. (MIT Licensed)\n *\n * manhattanNeuQuant.ts - part of Image Quantization Library\n */\nimport { AbstractDistanceCalculator } from './distanceCalculator';\nimport { Y } from '../constants/bt709';\n\n/**\n * Manhattan distance (NeuQuant modification) - w/o sRGB coefficients\n */\nexport abstract class AbstractManhattan extends AbstractDistanceCalculator {\n  declare protected _kR: number;\n  declare  protected _kG: number;\n  declare protected _kB: number;\n  declare protected _kA: number;\n\n  calculateRaw(\n    r1: number,\n    g1: number,\n    b1: number,\n    a1: number,\n    r2: number,\n    g2: number,\n    b2: number,\n    a2: number,\n  ) {\n    let dR = r2 - r1;\n    let dG = g2 - g1;\n    let dB = b2 - b1;\n    let dA = a2 - a1;\n    if (dR < 0) dR = 0 - dR;\n    if (dG < 0) dG = 0 - dG;\n    if (dB < 0) dB = 0 - dB;\n    if (dA < 0) dA = 0 - dA;\n\n    return this._kR * dR + this._kG * dG + this._kB * dB + this._kA * dA;\n  }\n}\n\nexport class Manhattan extends AbstractManhattan {\n  protected _setDefaults() {\n    this._kR = 1;\n    this._kG = 1;\n    this._kB = 1;\n    this._kA = 1;\n  }\n}\n\n/**\n * Manhattan distance (Nommyde modification)\n * https://github.com/igor-bezkrovny/image-quantization/issues/4#issuecomment-235155320\n */\nexport class ManhattanNommyde extends AbstractManhattan {\n  protected _setDefaults() {\n    this._kR = 0.4984;\n    this._kG = 0.8625;\n    this._kB = 0.2979;\n    // TODO: what is the best coefficient below?\n    this._kA = 1;\n  }\n}\n\n/**\n * Manhattan distance (sRGB coefficients)\n */\nexport class ManhattanBT709 extends AbstractManhattan {\n  protected _setDefaults() {\n    this._kR = Y.RED;\n    this._kG = Y.GREEN;\n    this._kB = Y.BLUE;\n    // TODO: what is the best coefficient below?\n    this._kA = 1;\n  }\n}\n", "/**\n * @preserve\n * Copyright 2015-2018 <PERSON>\n * All rights reserved. (MIT Licensed)\n *\n * pngQuant.ts - part of Image Quantization Library\n */\nimport { AbstractDistanceCalculator } from './distanceCalculator';\n\n/**\n * TODO: check quality of this distance equation\n * TODO: ask author for usage rights\n * taken from:\n * {@link http://stackoverflow.com/questions/4754506/color-similarity-distance-in-rgba-color-space/8796867#8796867}\n * {@link https://github.com/pornel/pngquant/blob/cc39b47799a7ff2ef17b529f9415ff6e6b213b8f/lib/pam.h#L148}\n */\nexport class PNGQuant extends AbstractDistanceCalculator {\n  /**\n   * Author's comments\n   * px_b.rgb = px.rgb + 0*(1-px.a) // blend px on black\n   * px_b.a   = px.a   + 1*(1-px.a)\n   * px_w.rgb = px.rgb + 1*(1-px.a) // blend px on white\n   * px_w.a   = px.a   + 1*(1-px.a)\n   *\n   * px_b.rgb = px.rgb              // difference same as in opaque RGB\n   * px_b.a   = 1\n   * px_w.rgb = px.rgb - px.a       // difference simplifies to formula below\n   * px_w.a   = 1\n   *\n   * (px.rgb - px.a) - (py.rgb - py.a)\n   * (px.rgb - py.rgb) + (py.a - px.a)\n   *\n   */\n  calculateRaw(\n    r1: number,\n    g1: number,\n    b1: number,\n    a1: number,\n    r2: number,\n    g2: number,\n    b2: number,\n    a2: number,\n  ) {\n    const alphas = (a2 - a1) * this._whitePoint.a;\n    return (\n      this._colordifferenceCh(\n        r1 * this._whitePoint.r,\n        r2 * this._whitePoint.r,\n        alphas,\n      ) +\n      this._colordifferenceCh(\n        g1 * this._whitePoint.g,\n        g2 * this._whitePoint.g,\n        alphas,\n      ) +\n      this._colordifferenceCh(\n        b1 * this._whitePoint.b,\n        b2 * this._whitePoint.b,\n        alphas,\n      )\n    );\n  }\n\n  private _colordifferenceCh(x: number, y: number, alphas: number) {\n    // maximum of channel blended on white, and blended on black\n    // premultiplied alpha and backgrounds 0/1 shorten the formula\n    const black = x - y;\n    const white = black + alphas;\n\n    return black * black + white * white;\n  }\n\n  protected _setDefaults() {}\n}\n", "/**\n * @preserve\n * Copyright 2015-2018 <PERSON>\n * All rights reserved. (MIT Licensed)\n *\n * iq.ts - Image Quantization Library\n */\nexport { AbstractPaletteQuantizer } from './paletteQuantizer';\nexport { NeuQuant } from './neuquant/neuquant';\nexport { NeuQuantFloat } from './neuquant/neuquantFloat';\nexport { RGBQuant } from './rgbquant/rgbquant';\nexport { ColorHistogram } from './rgbquant/colorHistogram';\nexport { WuQuant, WuColorCube } from './wu/wuQuant';\nexport { PaletteQuantizerYieldValue } from './paletteQuantizerYieldValue';\n", "/**\n * @preserve\n * Copyright 2015-2018 <PERSON>\n * All rights reserved. (MIT Licensed)\n *\n * common.ts - part of Image Quantization Library\n */\nimport { PointContainer } from '../utils/pointContainer';\nimport { PaletteQuantizerYieldValue } from './paletteQuantizerYieldValue';\n\nexport abstract class AbstractPaletteQuantizer {\n  abstract sample(pointContainer: PointContainer): void;\n  abstract quantize(): IterableIterator<PaletteQuantizerYieldValue>;\n\n  quantizeSync() {\n    for (const value of this.quantize()) {\n      if (value.palette) {\n        return value.palette;\n      }\n    }\n\n    throw new Error('unreachable');\n  }\n}\n", "/**\n * @preserve\n * Copyright 2015-2018 <PERSON>\n * All rights reserved. (MIT Licensed)\n *\n * point.ts - part of Image Quantization Library\n */\nimport { Y } from '../constants/bt709';\n\nexport interface PointRGBA {\n  r: number;\n  g: number;\n  b: number;\n  a: number;\n}\n\n/**\n * v8 optimized class\n * 1) \"constructor\" should have initialization with worst types\n * 2) \"set\" should have |0 / >>> 0\n */\nexport class Point implements PointRGBA {\n  r: number;\n  g: number;\n  b: number;\n  a: number;\n  uint32: number;\n  rgba: number[]; // TODO: better name is quadruplet or quad may be?\n  // Lab : { L : number; a : number; b : number };\n\n  static createByQuadruplet(quadruplet: number[]) {\n    const point = new Point();\n\n    point.r = quadruplet[0] | 0;\n    point.g = quadruplet[1] | 0;\n    point.b = quadruplet[2] | 0;\n    point.a = quadruplet[3] | 0;\n    point._loadUINT32();\n    point._loadQuadruplet();\n    // point._loadLab();\n    return point;\n  }\n\n  static createByRGBA(red: number, green: number, blue: number, alpha: number) {\n    const point = new Point();\n\n    point.r = red | 0;\n    point.g = green | 0;\n    point.b = blue | 0;\n    point.a = alpha | 0;\n    point._loadUINT32();\n    point._loadQuadruplet();\n    // point._loadLab();\n    return point;\n  }\n\n  static createByUint32(uint32: number) {\n    const point = new Point();\n\n    point.uint32 = uint32 >>> 0;\n    point._loadRGBA();\n    point._loadQuadruplet();\n    // point._loadLab();\n    return point;\n  }\n\n  constructor() {\n    this.uint32 = -1 >>> 0;\n    this.r = this.g = this.b = this.a = 0;\n    this.rgba = new Array(4);\n    this.rgba[0] = 0;\n    this.rgba[1] = 0;\n    this.rgba[2] = 0;\n    this.rgba[3] = 0;\n    /*\n     this.Lab = {\n     L : 0.0,\n     a : 0.0,\n     b : 0.0\n     };\n     */\n  }\n\n  from(point: Point) {\n    this.r = point.r;\n    this.g = point.g;\n    this.b = point.b;\n    this.a = point.a;\n    this.uint32 = point.uint32;\n    this.rgba[0] = point.r;\n    this.rgba[1] = point.g;\n    this.rgba[2] = point.b;\n    this.rgba[3] = point.a;\n\n    /*\n     this.Lab.L = point.Lab.L;\n     this.Lab.a = point.Lab.a;\n     this.Lab.b = point.Lab.b;\n     */\n  }\n\n  /*\n   * TODO:\n   Luminance from RGB:\n\n   Luminance (standard for certain colour spaces): (0.2126*R + 0.7152*G + 0.0722*B) [1]\n   Luminance (perceived option 1): (0.299*R + 0.587*G + 0.114*B) [2]\n   Luminance (perceived option 2, slower to calculate):  sqrt( 0.241*R^2 + 0.691*G^2 + 0.068*B^2 ) ? sqrt( 0.299*R^2 + 0.587*G^2 + 0.114*B^2 ) (thanks to @MatthewHerbst) [http://alienryderflex.com/hsp.html]\n   */\n  getLuminosity(useAlphaChannel: boolean) {\n    let r = this.r;\n    let g = this.g;\n    let b = this.b;\n\n    if (useAlphaChannel) {\n      r = Math.min(255, 255 - this.a + (this.a * r) / 255);\n      g = Math.min(255, 255 - this.a + (this.a * g) / 255);\n      b = Math.min(255, 255 - this.a + (this.a * b) / 255);\n    }\n\n    // var luma = this.r * Point._RED_COEFFICIENT + this.g * Point._GREEN_COEFFICIENT + this.b * Point._BLUE_COEFFICIENT;\n\n    /*\n     if(useAlphaChannel) {\n     luma = (luma * (255 - this.a)) / 255;\n     }\n     */\n\n    return r * Y.RED + g * Y.GREEN + b * Y.BLUE;\n  }\n\n  private _loadUINT32() {\n    this.uint32 =\n      ((this.a << 24) | (this.b << 16) | (this.g << 8) | this.r) >>> 0;\n  }\n\n  private _loadRGBA() {\n    this.r = this.uint32 & 0xff;\n    this.g = (this.uint32 >>> 8) & 0xff;\n    this.b = (this.uint32 >>> 16) & 0xff;\n    this.a = (this.uint32 >>> 24) & 0xff;\n  }\n\n  private _loadQuadruplet() {\n    this.rgba[0] = this.r;\n    this.rgba[1] = this.g;\n    this.rgba[2] = this.b;\n    this.rgba[3] = this.a;\n\n    /*\n     var xyz = rgb2xyz(this.r, this.g, this.b);\n     var lab = xyz2lab(xyz.x, xyz.y, xyz.z);\n     this.lab.l = lab.l;\n     this.lab.a = lab.a;\n     this.lab.b = lab.b;\n     */\n  }\n\n  /*\n   private _loadLab() : void {\n   var Lab = Color.Conversion.rgb2lab(this.r, this.g, this.b);\n   this.Lab.L = Lab.L;\n   this.Lab.a = Lab.a;\n   this.Lab.b = Lab.b;\n   }\n   */\n}\n", "/**\n * @preserve\n * Copyright 2015-2018 <PERSON>\n * All rights reserved. (MIT Licensed)\n *\n * pointContainer.ts - part of Image Quantization Library\n */\nimport { Point } from './point';\n\n/**\n * v8 optimizations done.\n * fromXXX methods are static to move out polymorphic code from class instance itself.\n */\nexport class PointContainer {\n  private readonly _pointArray: Point[];\n  private _width: number;\n  private _height: number;\n\n  constructor() {\n    this._width = 0;\n    this._height = 0;\n    this._pointArray = [];\n  }\n\n  getWidth() {\n    return this._width;\n  }\n\n  getHeight() {\n    return this._height;\n  }\n\n  setWidth(width: number) {\n    this._width = width;\n  }\n\n  setHeight(height: number) {\n    this._height = height;\n  }\n\n  getPointArray() {\n    return this._pointArray;\n  }\n\n  clone() {\n    const clone = new PointContainer();\n    clone._width = this._width;\n    clone._height = this._height;\n\n    for (let i = 0, l = this._pointArray.length; i < l; i++) {\n      clone._pointArray[i] = Point.createByUint32(\n        this._pointArray[i].uint32 | 0,\n      ); // \"| 0\" is added for v8 optimization\n    }\n\n    return clone;\n  }\n\n  toUint32Array() {\n    const l = this._pointArray.length;\n    const uint32Array = new Uint32Array(l);\n\n    for (let i = 0; i < l; i++) {\n      uint32Array[i] = this._pointArray[i].uint32;\n    }\n\n    return uint32Array;\n  }\n\n  toUint8Array() {\n    return new Uint8Array(this.toUint32Array().buffer);\n  }\n\n  static fromHTMLImageElement(img: HTMLImageElement) {\n    const width = img.naturalWidth;\n    const height = img.naturalHeight;\n\n    const canvas = document.createElement('canvas');\n    canvas.width = width;\n    canvas.height = height;\n\n    const ctx = canvas.getContext('2d')!;\n    ctx.drawImage(img, 0, 0, width, height, 0, 0, width, height);\n\n    return PointContainer.fromHTMLCanvasElement(canvas);\n  }\n\n  static fromHTMLCanvasElement(canvas: HTMLCanvasElement) {\n    const width = canvas.width;\n    const height = canvas.height;\n\n    const ctx = canvas.getContext('2d')!;\n    const imgData = ctx.getImageData(0, 0, width, height);\n\n    return PointContainer.fromImageData(imgData);\n  }\n\n  static fromImageData(imageData: ImageData) {\n    const width = imageData.width;\n    const height = imageData.height;\n\n    return PointContainer.fromUint8Array(imageData.data, width, height);\n  }\n\n  static fromUint8Array(\n    uint8Array: number[] | Uint8Array | Uint8ClampedArray,\n    width: number,\n    height: number,\n  ) {\n    switch (Object.prototype.toString.call(uint8Array)) {\n      case '[object Uint8ClampedArray]':\n      case '[object Uint8Array]':\n        break;\n\n      default:\n        uint8Array = new Uint8Array(uint8Array);\n    }\n\n    const uint32Array = new Uint32Array((uint8Array as Uint8Array).buffer);\n    return PointContainer.fromUint32Array(uint32Array, width, height);\n  }\n\n  static fromUint32Array(\n    uint32Array: Uint32Array,\n    width: number,\n    height: number,\n  ) {\n    const container = new PointContainer();\n\n    container._width = width;\n    container._height = height;\n\n    for (let i = 0, l = uint32Array.length; i < l; i++) {\n      container._pointArray[i] = Point.createByUint32(uint32Array[i] | 0); // \"| 0\" is added for v8 optimization\n    }\n\n    return container;\n  }\n\n  static fromBuffer(buffer: Buffer, width: number, height: number) {\n    const uint32Array = new Uint32Array(\n      buffer.buffer,\n      buffer.byteOffset,\n      buffer.byteLength / Uint32Array.BYTES_PER_ELEMENT,\n    );\n    return PointContainer.fromUint32Array(uint32Array, width, height);\n  }\n}\n", "/**\n * @preserve\n * Copyright 2015-2018 <PERSON>\n * All rights reserved. (MIT Licensed)\n *\n * palette.ts - part of Image Quantization Library\n */\n\nimport { Point } from './point';\nimport { PointContainer } from './pointContainer';\nimport { AbstractDistanceCalculator } from '../distance/distanceCalculator';\nimport { rgb2hsl } from '../conversion/rgb2hsl';\n\n// TODO: make paletteArray via pointContainer, so, export will be available via pointContainer.exportXXX\n\nconst hueGroups = 10;\n\nexport function hueGroup(hue: number, segmentsNumber: number) {\n  const maxHue = 360;\n  const seg = maxHue / segmentsNumber;\n  const half = seg / 2;\n\n  for (let i = 1, mid = seg - half; i < segmentsNumber; i++, mid += seg) {\n    if (hue >= mid && hue < mid + seg) return i;\n  }\n  return 0;\n}\n\nexport class Palette {\n  private readonly _pointContainer: PointContainer;\n  private readonly _pointArray: Point[] = [];\n  private _i32idx: { [key: string]: number } = {};\n\n  constructor() {\n    this._pointContainer = new PointContainer();\n    this._pointContainer.setHeight(1);\n    this._pointArray = this._pointContainer.getPointArray();\n  }\n\n  add(color: Point) {\n    this._pointArray.push(color);\n    this._pointContainer.setWidth(this._pointArray.length);\n  }\n\n  has(color: Point) {\n    for (let i = this._pointArray.length - 1; i >= 0; i--) {\n      if (color.uint32 === this._pointArray[i].uint32) return true;\n    }\n\n    return false;\n  }\n\n  // TOTRY: use HUSL - http://boronine.com/husl/ http://www.husl-colors.org/ https://github.com/husl-colors/husl\n  getNearestColor(\n    colorDistanceCalculator: AbstractDistanceCalculator,\n    color: Point,\n  ) {\n    return this._pointArray[\n      this._getNearestIndex(colorDistanceCalculator, color) | 0\n    ];\n  }\n\n  getPointContainer() {\n    return this._pointContainer;\n  }\n\n  // TOTRY: use HUSL - http://boronine.com/husl/\n  /*\n   public nearestIndexByUint32(i32) {\n   var idx : number = this._nearestPointFromCache(\"\" + i32);\n   if (idx >= 0) return idx;\n\n   var min = 1000,\n   rgb = [\n   (i32 & 0xff),\n   (i32 >>> 8) & 0xff,\n   (i32 >>> 16) & 0xff,\n   (i32 >>> 24) & 0xff\n   ],\n   len = this._pointArray.length;\n\n   idx = 0;\n   for (var i = 0; i < len; i++) {\n   var dist = Utils.distEuclidean(rgb, this._pointArray[i].rgba);\n\n   if (dist < min) {\n   min = dist;\n   idx = i;\n   }\n   }\n\n   this._i32idx[i32] = idx;\n   return idx;\n   }\n   */\n\n  private _nearestPointFromCache(key: string) {\n    return typeof this._i32idx[key] === 'number' ? this._i32idx[key] : -1;\n  }\n\n  private _getNearestIndex(\n    colorDistanceCalculator: AbstractDistanceCalculator,\n    point: Point,\n  ) {\n    let idx = this._nearestPointFromCache('' + point.uint32);\n    if (idx >= 0) return idx;\n\n    let minimalDistance = Number.MAX_VALUE;\n\n    idx = 0;\n    for (let i = 0, l = this._pointArray.length; i < l; i++) {\n      const p = this._pointArray[i];\n      const distance = colorDistanceCalculator.calculateRaw(\n        point.r,\n        point.g,\n        point.b,\n        point.a,\n        p.r,\n        p.g,\n        p.b,\n        p.a,\n      );\n\n      if (distance < minimalDistance) {\n        minimalDistance = distance;\n        idx = i;\n      }\n    }\n\n    this._i32idx[point.uint32] = idx;\n    return idx;\n  }\n\n  /*\n   public reduce(histogram : ColorHistogram, colors : number) {\n   if (this._pointArray.length > colors) {\n   var idxi32 = histogram.getImportanceSortedColorsIDXI32();\n\n   // quantize histogram to existing palette\n   var keep = [], uniqueColors = 0, idx, pruned = false;\n\n   for (var i = 0, len = idxi32.length; i < len; i++) {\n   // palette length reached, unset all remaining colors (sparse palette)\n   if (uniqueColors >= colors) {\n   this.prunePal(keep);\n   pruned = true;\n   break;\n   } else {\n   idx = this.nearestIndexByUint32(idxi32[i]);\n   if (keep.indexOf(idx) < 0) {\n   keep.push(idx);\n   uniqueColors++;\n   }\n   }\n   }\n\n   if (!pruned) {\n   this.prunePal(keep);\n   }\n   }\n   }\n\n   // TODO: check usage, not tested!\n   public prunePal(keep : number[]) {\n   var colors = this._pointArray.length;\n   for (var colorIndex = colors - 1; colorIndex >= 0; colorIndex--) {\n   if (keep.indexOf(colorIndex) < 0) {\n\n   if(colorIndex + 1 < colors) {\n   this._pointArray[ colorIndex ] = this._pointArray [ colors - 1 ];\n   }\n   --colors;\n   //this._pointArray[colorIndex] = null;\n   }\n   }\n   console.log(\"colors pruned: \" + (this._pointArray.length - colors));\n   this._pointArray.length = colors;\n   this._i32idx = {};\n   }\n   */\n\n  // TODO: group very low lum and very high lum colors\n  // TODO: pass custom sort order\n  // TODO: sort criteria function should be placed to HueStats class\n  sort() {\n    this._i32idx = {};\n    this._pointArray.sort((a: Point, b: Point) => {\n      const hslA = rgb2hsl(a.r, a.g, a.b);\n      const hslB = rgb2hsl(b.r, b.g, b.b);\n\n      // sort all grays + whites together\n      const hueA =\n        a.r === a.g && a.g === a.b ? 0 : 1 + hueGroup(hslA.h, hueGroups);\n      const hueB =\n        b.r === b.g && b.g === b.b ? 0 : 1 + hueGroup(hslB.h, hueGroups);\n      /*\n       var hueA = (a.r === a.g && a.g === a.b) ? 0 : 1 + Utils.hueGroup(hslA.h, hueGroups);\n       var hueB = (b.r === b.g && b.g === b.b) ? 0 : 1 + Utils.hueGroup(hslB.h, hueGroups);\n       */\n\n      const hueDiff = hueB - hueA;\n      if (hueDiff) return -hueDiff;\n\n      /*\n       var lumDiff = Utils.lumGroup(+hslB.l.toFixed(2)) - Utils.lumGroup(+hslA.l.toFixed(2));\n       if (lumDiff) return -lumDiff;\n       */\n      const lA = a.getLuminosity(true);\n      const lB = b.getLuminosity(true);\n\n      if (lB - lA !== 0) return lB - lA;\n\n      const satDiff = ((hslB.s * 100) | 0) - ((hslA.s * 100) | 0);\n      if (satDiff) return -satDiff;\n\n      return 0;\n    });\n  }\n}\n", "/**\n * @preserve\n * Copyright 2015-2018 <PERSON>\n * All rights reserved. (MIT Licensed)\n *\n * iq.ts - Image Quantization Library\n */\nimport * as arithmetic from './arithmetic';\nimport { HueStatistics } from './hueStatistics';\nimport { Palette } from './palette';\nimport { Point } from './point';\nimport { PointContainer } from './pointContainer';\nimport { ProgressTracker } from './progressTracker';\n\nexport {\n  Point,\n  PointContainer,\n  Palette,\n  HueStatistics,\n  ProgressTracker,\n  arithmetic,\n};\n", "/**\n * @preserve\n * Copyright 2015-2018 <PERSON>\n * All rights reserved. (MIT Licensed)\n *\n * hueStatistics.ts - part of Image Quantization Library\n */\nimport { rgb2hsl } from '../conversion/rgb2hsl';\nimport { hueGroup } from './palette';\n\nclass HueGroup {\n  num = 0;\n  cols: number[] = [];\n}\n\nexport class HueStatistics {\n  private _numGroups: number;\n  private _minCols: number;\n  private _stats: HueGroup[];\n  private _groupsFull: number;\n\n  constructor(numGroups: number, minCols: number) {\n    this._numGroups = numGroups;\n    this._minCols = minCols;\n    this._stats = [];\n\n    for (let i = 0; i <= numGroups; i++) {\n      this._stats[i] = new HueGroup();\n    }\n\n    this._groupsFull = 0;\n  }\n\n  check(i32: number) {\n    if (this._groupsFull === this._numGroups + 1) {\n      this.check = () => {};\n    }\n\n    const r = i32 & 0xff;\n    const g = (i32 >>> 8) & 0x<PERSON>;\n    const b = (i32 >>> 16) & 0xff;\n    const hg =\n      r === g && g === b\n        ? 0\n        : 1 + hueGroup(rgb2hsl(r, g, b).h, this._numGroups);\n    const gr = this._stats[hg];\n    const min = this._minCols;\n\n    gr.num++;\n\n    if (gr.num > min) {\n      return;\n    }\n    if (gr.num === min) {\n      this._groupsFull++;\n    }\n\n    if (gr.num <= min) {\n      this._stats[hg].cols.push(i32);\n    }\n  }\n\n  injectIntoDictionary(histG: Record<string, number>) {\n    for (let i = 0; i <= this._numGroups; i++) {\n      if (this._stats[i].num <= this._minCols) {\n        this._stats[i].cols.forEach((col: number) => {\n          if (!histG[col]) {\n            histG[col] = 1;\n          } else {\n            histG[col]++;\n          }\n        });\n      }\n    }\n  }\n\n  injectIntoArray(histG: string[]) {\n    for (let i = 0; i <= this._numGroups; i++) {\n      if (this._stats[i].num <= this._minCols) {\n        this._stats[i].cols.forEach((col: unknown) => {\n          if (histG.indexOf(col as string) === -1) {\n            histG.push(col as string);\n          }\n        });\n      }\n    }\n  }\n}\n", "export class ProgressTracker {\n  static readonly steps = 100;\n\n  progress: number;\n\n  private _step: number;\n  private _range: number;\n  private _last: number;\n  private _progressRange: number;\n\n  constructor(valueRange: number, progressRange: number) {\n    this._range = valueRange;\n    this._progressRange = progressRange;\n    this._step = Math.max(1, (this._range / (ProgressTracker.steps + 1)) | 0);\n    this._last = -this._step;\n    this.progress = 0;\n  }\n\n  shouldNotify(current: number) {\n    if (current - this._last >= this._step) {\n      this._last = current;\n      this.progress = Math.min(\n        (this._progressRange * this._last) / this._range,\n        this._progressRange,\n      );\n      return true;\n    }\n\n    return false;\n  }\n}\n", "/*\n * NeuQuant Neural-Net Quantization Algorithm\n * ------------------------------------------\n *\n * Copyright (c) 1994 <PERSON>\n *\n * NEUQUANT Neural-Net quantization algorithm by <PERSON>, 1994. See\n * \"Kohonen neural networks for optimal colour quantization\" in \"Network:\n * Computation in Neural Systems\" Vol. 5 (1994) pp 351-367. for a discussion of\n * the algorithm.\n *\n * Any party obtaining a copy of these files from the author, directly or\n * indirectly, is granted, free of charge, a full and unrestricted irrevocable,\n * world-wide, paid up, royalty-free, nonexclusive right and license to deal in\n * this software and documentation files (the \"Software\"), including without\n * limitation the rights to use, copy, modify, merge, publish, distribute,\n * sublicense, and/or sell copies of the Software, and to permit persons who\n * receive copies from any such party to do so, with the only requirement being\n * that this copyright notice remain intact.\n */\n\n/**\n * @preserve TypeScript port:\n * Copyright 2015-2018 <PERSON>\n * All rights reserved. (MIT Licensed)\n *\n * neuquant.ts - part of Image Quantization Library\n */\n\nimport { Palette } from '../../utils/palette';\nimport { Point } from '../../utils/point';\nimport { PointContainer } from '../../utils/pointContainer';\nimport { AbstractDistanceCalculator } from '../../distance/distanceCalculator';\nimport { AbstractPaletteQuantizer } from '../paletteQuantizer';\nimport { PaletteQuantizerYieldValue } from '../paletteQuantizerYieldValue';\nimport { ProgressTracker } from '../../utils';\n\n// bias for colour values\nconst networkBiasShift = 3;\n\nclass Neuron {\n  r: number;\n  g: number;\n  b: number;\n  a: number;\n\n  constructor(defaultValue: number) {\n    this.r = this.g = this.b = this.a = defaultValue;\n  }\n\n  /**\n   * There is a fix in original NEUQUANT by Anthony Dekker (http://members.ozemail.com.au/~dekker/NEUQUANT.HTML)\n   * @example\n   * r = Math.min(255, (neuron.r + (1 << (networkBiasShift - 1))) >> networkBiasShift);\n   */\n  toPoint() {\n    return Point.createByRGBA(\n      this.r >> networkBiasShift,\n      this.g >> networkBiasShift,\n      this.b >> networkBiasShift,\n      this.a >> networkBiasShift,\n    );\n  }\n\n  subtract(r: number, g: number, b: number, a: number) {\n    this.r -= r | 0;\n    this.g -= g | 0;\n    this.b -= b | 0;\n    this.a -= a | 0;\n  }\n\n  /*\n   public subtract(r : number, g : number, b : number, a : number) : void {\n   this.r = (-r + this.r) | 0;\n   this.g = (-g + this.g) | 0;\n   this.b = (-b + this.b) | 0;\n   this.a = (-a + this.a) | 0;\n\n   this.r -= r;\n   this.g -= g;\n   this.b -= b;\n   this.a -= a;\n\n   this.r -= r | 0;\n   this.g -= g | 0;\n   this.b -= b | 0;\n   this.a -= a | 0;\n   }\n   */\n}\n\nexport class NeuQuant extends AbstractPaletteQuantizer {\n  /*\n   four primes near 500 - assume no image has a length so large\n   that it is divisible by all four primes\n   */\n  private static readonly _prime1 = 499;\n  private static readonly _prime2 = 491;\n  private static readonly _prime3 = 487;\n  private static readonly _prime4 = 503;\n  private static readonly _minpicturebytes = NeuQuant._prime4;\n\n  // no. of learning cycles\n  private static readonly _nCycles = 100;\n\n  // defs for freq and bias\n  private static readonly _initialBiasShift = 16;\n\n  // bias for fractions\n  private static readonly _initialBias = 1 << NeuQuant._initialBiasShift;\n  private static readonly _gammaShift = 10;\n\n  // gamma = 1024\n  // TODO: why gamma is never used?\n  // private static _gamma : number     = (1 << NeuQuant._gammaShift);\n  private static readonly _betaShift = 10;\n  private static readonly _beta = NeuQuant._initialBias >> NeuQuant._betaShift;\n\n  // beta = 1/1024\n  private static readonly _betaGamma =\n    NeuQuant._initialBias << (NeuQuant._gammaShift - NeuQuant._betaShift);\n\n  /*\n   * for 256 cols, radius starts\n   */\n  private static readonly _radiusBiasShift = 6;\n\n  // at 32.0 biased by 6 bits\n  private static readonly _radiusBias = 1 << NeuQuant._radiusBiasShift;\n\n  // and decreases by a factor of 1/30 each cycle\n  private static readonly _radiusDecrease = 30;\n\n  /* defs for decreasing alpha factor */\n\n  // alpha starts at 1.0\n  private static readonly _alphaBiasShift = 10;\n\n  // biased by 10 bits\n  private static readonly _initAlpha = 1 << NeuQuant._alphaBiasShift;\n\n  /* radBias and alphaRadBias used for radpower calculation */\n  private static readonly _radBiasShift = 8;\n  private static readonly _radBias = 1 << NeuQuant._radBiasShift;\n  private static readonly _alphaRadBiasShift =\n    NeuQuant._alphaBiasShift + NeuQuant._radBiasShift;\n  private static readonly _alphaRadBias = 1 << NeuQuant._alphaRadBiasShift;\n\n  private _pointArray: Point[];\n  private readonly _networkSize: number;\n  private _network!: Neuron[];\n\n  /** sampling factor 1..30 */\n  private readonly _sampleFactor!: number;\n  private _radPower!: number[];\n\n  // bias and freq arrays for learning\n  private _freq!: number[];\n\n  /* for network lookup - really 256 */\n  private _bias!: number[];\n  private readonly _distance: AbstractDistanceCalculator;\n\n  constructor(\n    colorDistanceCalculator: AbstractDistanceCalculator,\n    colors = 256,\n  ) {\n    super();\n    this._distance = colorDistanceCalculator;\n    this._pointArray = [];\n    this._sampleFactor = 1;\n    this._networkSize = colors;\n\n    this._distance.setWhitePoint(\n      255 << networkBiasShift,\n      255 << networkBiasShift,\n      255 << networkBiasShift,\n      255 << networkBiasShift,\n    );\n  }\n\n  sample(pointContainer: PointContainer) {\n    this._pointArray = this._pointArray.concat(pointContainer.getPointArray());\n  }\n\n  *quantize(): IterableIterator<PaletteQuantizerYieldValue> {\n    this._init();\n\n    yield* this._learn();\n\n    yield {\n      palette: this._buildPalette(),\n      progress: 100,\n    };\n  }\n\n  private _init() {\n    this._freq = [];\n    this._bias = [];\n    this._radPower = [];\n    this._network = [];\n    for (let i = 0; i < this._networkSize; i++) {\n      this._network[i] = new Neuron(\n        ((i << (networkBiasShift + 8)) / this._networkSize) | 0,\n      );\n\n      // 1/this._networkSize\n      this._freq[i] = (NeuQuant._initialBias / this._networkSize) | 0;\n      this._bias[i] = 0;\n    }\n  }\n\n  /**\n   * Main Learning Loop\n   */\n  private *_learn() {\n    let sampleFactor = this._sampleFactor;\n    const pointsNumber = this._pointArray.length;\n    if (pointsNumber < NeuQuant._minpicturebytes) sampleFactor = 1;\n\n    const alphadec = (30 + (sampleFactor - 1) / 3) | 0;\n    const pointsToSample = (pointsNumber / sampleFactor) | 0;\n\n    let delta = (pointsToSample / NeuQuant._nCycles) | 0;\n    let alpha = NeuQuant._initAlpha;\n    let radius = (this._networkSize >> 3) * NeuQuant._radiusBias;\n\n    let rad = radius >> NeuQuant._radiusBiasShift;\n    if (rad <= 1) rad = 0;\n\n    for (let i = 0; i < rad; i++) {\n      this._radPower[i] =\n        (alpha * (((rad * rad - i * i) * NeuQuant._radBias) / (rad * rad))) >>>\n        0;\n    }\n\n    let step;\n    if (pointsNumber < NeuQuant._minpicturebytes) {\n      step = 1;\n    } else if (pointsNumber % NeuQuant._prime1 !== 0) {\n      step = NeuQuant._prime1;\n    } else if (pointsNumber % NeuQuant._prime2 !== 0) {\n      step = NeuQuant._prime2;\n    } else if (pointsNumber % NeuQuant._prime3 !== 0) {\n      step = NeuQuant._prime3;\n    } else {\n      step = NeuQuant._prime4;\n    }\n\n    const tracker = new ProgressTracker(pointsToSample, 99);\n    for (let i = 0, pointIndex = 0; i < pointsToSample; ) {\n      if (tracker.shouldNotify(i)) {\n        yield {\n          progress: tracker.progress,\n        };\n      }\n\n      const point = this._pointArray[pointIndex];\n      const b = point.b << networkBiasShift;\n      const g = point.g << networkBiasShift;\n      const r = point.r << networkBiasShift;\n      const a = point.a << networkBiasShift;\n      const neuronIndex = this._contest(b, g, r, a);\n\n      this._alterSingle(alpha, neuronIndex, b, g, r, a);\n      if (rad !== 0) this._alterNeighbour(rad, neuronIndex, b, g, r, a);\n\n      /* alter neighbours */\n      pointIndex += step;\n      if (pointIndex >= pointsNumber) pointIndex -= pointsNumber;\n      i++;\n\n      if (delta === 0) delta = 1;\n\n      if (i % delta === 0) {\n        alpha -= (alpha / alphadec) | 0;\n        radius -= (radius / NeuQuant._radiusDecrease) | 0;\n        rad = radius >> NeuQuant._radiusBiasShift;\n\n        if (rad <= 1) rad = 0;\n        for (let j = 0; j < rad; j++) {\n          this._radPower[j] =\n            (alpha *\n              (((rad * rad - j * j) * NeuQuant._radBias) / (rad * rad))) >>>\n            0;\n        }\n      }\n    }\n  }\n\n  private _buildPalette() {\n    const palette = new Palette();\n\n    this._network.forEach((neuron) => {\n      palette.add(neuron.toPoint());\n    });\n\n    palette.sort();\n    return palette;\n  }\n\n  /**\n   * Move adjacent neurons by precomputed alpha*(1-((i-j)^2/[r]^2)) in radpower[|i-j|]\n   */\n  private _alterNeighbour(\n    rad: number,\n    i: number,\n    b: number,\n    g: number,\n    r: number,\n    al: number,\n  ) {\n    let lo = i - rad;\n    if (lo < -1) lo = -1;\n\n    let hi = i + rad;\n    if (hi > this._networkSize) hi = this._networkSize;\n\n    let j = i + 1;\n    let k = i - 1;\n    let m = 1;\n\n    while (j < hi || k > lo) {\n      const a = this._radPower[m++] / NeuQuant._alphaRadBias;\n      if (j < hi) {\n        const p = this._network[j++];\n        p.subtract(a * (p.r - r), a * (p.g - g), a * (p.b - b), a * (p.a - al));\n      }\n\n      if (k > lo) {\n        const p = this._network[k--];\n        p.subtract(a * (p.r - r), a * (p.g - g), a * (p.b - b), a * (p.a - al));\n      }\n    }\n  }\n\n  /**\n   * Move neuron i towards biased (b,g,r) by factor alpha\n   */\n  private _alterSingle(\n    alpha: number,\n    i: number,\n    b: number,\n    g: number,\n    r: number,\n    a: number,\n  ) {\n    alpha /= NeuQuant._initAlpha;\n\n    /* alter hit neuron */\n    const n = this._network[i];\n    n.subtract(\n      alpha * (n.r - r),\n      alpha * (n.g - g),\n      alpha * (n.b - b),\n      alpha * (n.a - a),\n    );\n  }\n\n  /**\n   * Search for biased BGR values\n   * description:\n   *    finds closest neuron (min dist) and updates freq\n   *    finds best neuron (min dist-bias) and returns position\n   *    for frequently chosen neurons, freq[i] is high and bias[i] is negative\n   *    bias[i] = _gamma*((1/this._networkSize)-freq[i])\n   *\n   * Original distance equation:\n   *        dist = abs(dR) + abs(dG) + abs(dB)\n   */\n  private _contest(b: number, g: number, r: number, a: number) {\n    const multiplier = (255 * 4) << networkBiasShift;\n\n    let bestd = ~(1 << 31);\n    let bestbiasd = bestd;\n    let bestpos = -1;\n    let bestbiaspos = bestpos;\n\n    for (let i = 0; i < this._networkSize; i++) {\n      const n = this._network[i];\n      const dist =\n        (this._distance.calculateNormalized(n, { r, g, b, a }) * multiplier) |\n        0;\n\n      if (dist < bestd) {\n        bestd = dist;\n        bestpos = i;\n      }\n\n      const biasdist =\n        dist -\n        (this._bias[i] >> (NeuQuant._initialBiasShift - networkBiasShift));\n      if (biasdist < bestbiasd) {\n        bestbiasd = biasdist;\n        bestbiaspos = i;\n      }\n      const betafreq = this._freq[i] >> NeuQuant._betaShift;\n      this._freq[i] -= betafreq;\n      this._bias[i] += betafreq << NeuQuant._gammaShift;\n    }\n    this._freq[bestpos] += NeuQuant._beta;\n    this._bias[bestpos] -= NeuQuant._betaGamma;\n    return bestbiaspos;\n  }\n}\n", "/*\n * NeuQuantFloat Neural-Net Quantization Algorithm\n * ------------------------------------------\n *\n * Copyright (c) 1994 <PERSON>\n *\n * NEUQUANT Neural-Net quantization algorithm by <PERSON>, 1994. See\n * \"Kohonen neural networks for optimal colour quantization\" in \"Network:\n * Computation in Neural Systems\" Vol. 5 (1994) pp 351-367. for a discussion of\n * the algorithm.\n *\n * Any party obtaining a copy of these files from the author, directly or\n * indirectly, is granted, free of charge, a full and unrestricted irrevocable,\n * world-wide, paid up, royalty-free, nonexclusive right and license to deal in\n * this software and documentation files (the \"Software\"), including without\n * limitation the rights to use, copy, modify, merge, publish, distribute,\n * sublicense, and/or sell copies of the Software, and to permit persons who\n * receive copies from any such party to do so, with the only requirement being\n * that this copyright notice remain intact.\n */\n/**\n * @preserve TypeScript port:\n * Copyright 2015-2018 <PERSON>\n * All rights reserved. (MIT Licensed)\n *\n * neuquant.ts - part of Image Quantization Library\n */\nimport { Palette } from '../../utils/palette';\nimport { Point } from '../../utils/point';\nimport { PointContainer } from '../../utils/pointContainer';\nimport { AbstractDistanceCalculator } from '../../distance/distanceCalculator';\nimport { AbstractPaletteQuantizer } from '../paletteQuantizer';\nimport { PaletteQuantizerYieldValue } from '../paletteQuantizerYieldValue';\nimport { ProgressTracker } from '../../utils';\n\n// bias for colour values\nconst networkBiasShift = 3;\n\nclass NeuronFloat {\n  r: number;\n  g: number;\n  b: number;\n  a: number;\n\n  constructor(defaultValue: number) {\n    this.r = this.g = this.b = this.a = defaultValue;\n  }\n\n  /**\n   * There is a fix in original NEUQUANT by Anthony Dekker (http://members.ozemail.com.au/~dekker/NEUQUANT.HTML)\n   * @example\n   * r = Math.min(255, (neuron.r + (1 << (networkBiasShift - 1))) >> networkBiasShift);\n   */\n  toPoint() {\n    return Point.createByRGBA(\n      this.r >> networkBiasShift,\n      this.g >> networkBiasShift,\n      this.b >> networkBiasShift,\n      this.a >> networkBiasShift,\n    );\n  }\n\n  subtract(r: number, g: number, b: number, a: number) {\n    this.r -= r;\n    this.g -= g;\n    this.b -= b;\n    this.a -= a;\n  }\n}\n\nexport class NeuQuantFloat extends AbstractPaletteQuantizer {\n  /*\n   four primes near 500 - assume no image has a length so large\n   that it is divisible by all four primes\n   */\n  private static readonly _prime1 = 499;\n  private static readonly _prime2 = 491;\n  private static readonly _prime3 = 487;\n  private static readonly _prime4 = 503;\n  private static readonly _minpicturebytes = NeuQuantFloat._prime4;\n\n  // no. of learning cycles\n  private static readonly _nCycles = 100;\n\n  // defs for freq and bias\n  private static readonly _initialBiasShift = 16;\n\n  // bias for fractions\n  private static readonly _initialBias = 1 << NeuQuantFloat._initialBiasShift;\n  private static readonly _gammaShift = 10;\n\n  // gamma = 1024\n  // TODO: why gamma is never used?\n  // private static _gamma : number     = (1 << NeuQuantFloat._gammaShift);\n  private static readonly _betaShift = 10;\n  private static readonly _beta =\n    NeuQuantFloat._initialBias >> NeuQuantFloat._betaShift;\n\n  // beta = 1/1024\n  private static readonly _betaGamma =\n    NeuQuantFloat._initialBias <<\n    (NeuQuantFloat._gammaShift - NeuQuantFloat._betaShift);\n\n  /*\n   * for 256 cols, radius starts\n   */\n  private static readonly _radiusBiasShift = 6;\n\n  // at 32.0 biased by 6 bits\n  private static readonly _radiusBias = 1 << NeuQuantFloat._radiusBiasShift;\n\n  // and decreases by a factor of 1/30 each cycle\n  private static readonly _radiusDecrease = 30;\n\n  /* defs for decreasing alpha factor */\n\n  // alpha starts at 1.0\n  private static readonly _alphaBiasShift = 10;\n\n  // biased by 10 bits\n  private static readonly _initAlpha = 1 << NeuQuantFloat._alphaBiasShift;\n\n  /* radBias and alphaRadBias used for radpower calculation */\n  private static readonly _radBiasShift = 8;\n  private static readonly _radBias = 1 << NeuQuantFloat._radBiasShift;\n  private static readonly _alphaRadBiasShift =\n    NeuQuantFloat._alphaBiasShift + NeuQuantFloat._radBiasShift;\n  private static readonly _alphaRadBias = 1 << NeuQuantFloat._alphaRadBiasShift;\n\n  private _pointArray!: Point[];\n  private readonly _networkSize!: number;\n  private _network!: NeuronFloat[];\n\n  /** sampling factor 1..30 */\n  private readonly _sampleFactor!: number;\n  private _radPower!: number[];\n\n  // bias and freq arrays for learning\n  private _freq!: number[];\n\n  /* for network lookup - really 256 */\n  private _bias!: number[];\n  private readonly _distance: AbstractDistanceCalculator;\n\n  constructor(\n    colorDistanceCalculator: AbstractDistanceCalculator,\n    colors = 256,\n  ) {\n    super();\n    this._distance = colorDistanceCalculator;\n    this._pointArray = [];\n    this._sampleFactor = 1;\n    this._networkSize = colors;\n\n    this._distance.setWhitePoint(\n      255 << networkBiasShift,\n      255 << networkBiasShift,\n      255 << networkBiasShift,\n      255 << networkBiasShift,\n    );\n  }\n\n  sample(pointContainer: PointContainer) {\n    this._pointArray = this._pointArray.concat(pointContainer.getPointArray());\n  }\n\n  *quantize() {\n    this._init();\n    yield* this._learn();\n\n    yield {\n      palette: this._buildPalette(),\n      progress: 100,\n    };\n  }\n\n  private _init() {\n    this._freq = [];\n    this._bias = [];\n    this._radPower = [];\n    this._network = [];\n    for (let i = 0; i < this._networkSize; i++) {\n      this._network[i] = new NeuronFloat(\n        (i << (networkBiasShift + 8)) / this._networkSize,\n      );\n\n      // 1/this._networkSize\n      this._freq[i] = NeuQuantFloat._initialBias / this._networkSize;\n      this._bias[i] = 0;\n    }\n  }\n\n  /**\n   * Main Learning Loop\n   */\n  private *_learn(): IterableIterator<PaletteQuantizerYieldValue> {\n    let sampleFactor = this._sampleFactor;\n\n    const pointsNumber = this._pointArray.length;\n    if (pointsNumber < NeuQuantFloat._minpicturebytes) sampleFactor = 1;\n\n    const alphadec = 30 + (sampleFactor - 1) / 3;\n    const pointsToSample = pointsNumber / sampleFactor;\n\n    let delta = (pointsToSample / NeuQuantFloat._nCycles) | 0;\n    let alpha = NeuQuantFloat._initAlpha;\n    let radius = (this._networkSize >> 3) * NeuQuantFloat._radiusBias;\n\n    let rad = radius >> NeuQuantFloat._radiusBiasShift;\n    if (rad <= 1) rad = 0;\n\n    for (let i = 0; i < rad; i++) {\n      this._radPower[i] =\n        alpha * (((rad * rad - i * i) * NeuQuantFloat._radBias) / (rad * rad));\n    }\n\n    let step;\n    if (pointsNumber < NeuQuantFloat._minpicturebytes) {\n      step = 1;\n    } else if (pointsNumber % NeuQuantFloat._prime1 !== 0) {\n      step = NeuQuantFloat._prime1;\n    } else if (pointsNumber % NeuQuantFloat._prime2 !== 0) {\n      step = NeuQuantFloat._prime2;\n    } else if (pointsNumber % NeuQuantFloat._prime3 !== 0) {\n      step = NeuQuantFloat._prime3;\n    } else {\n      step = NeuQuantFloat._prime4;\n    }\n\n    const tracker = new ProgressTracker(pointsToSample, 99);\n    for (let i = 0, pointIndex = 0; i < pointsToSample; ) {\n      if (tracker.shouldNotify(i)) {\n        yield {\n          progress: tracker.progress,\n        };\n      }\n\n      const point = this._pointArray[pointIndex];\n      const b = point.b << networkBiasShift;\n      const g = point.g << networkBiasShift;\n      const r = point.r << networkBiasShift;\n      const a = point.a << networkBiasShift;\n      const neuronIndex = this._contest(b, g, r, a);\n\n      this._alterSingle(alpha, neuronIndex, b, g, r, a);\n      if (rad !== 0) this._alterNeighbour(rad, neuronIndex, b, g, r, a);\n\n      /* alter neighbours */\n      pointIndex += step;\n      if (pointIndex >= pointsNumber) pointIndex -= pointsNumber;\n      i++;\n\n      if (delta === 0) delta = 1;\n\n      if (i % delta === 0) {\n        alpha -= alpha / alphadec;\n        radius -= radius / NeuQuantFloat._radiusDecrease;\n        rad = radius >> NeuQuantFloat._radiusBiasShift;\n\n        if (rad <= 1) rad = 0;\n        for (let j = 0; j < rad; j++) {\n          this._radPower[j] =\n            alpha *\n            (((rad * rad - j * j) * NeuQuantFloat._radBias) / (rad * rad));\n        }\n      }\n    }\n  }\n\n  private _buildPalette() {\n    const palette = new Palette();\n\n    this._network.forEach((neuron) => {\n      palette.add(neuron.toPoint());\n    });\n\n    palette.sort();\n    return palette;\n  }\n\n  /**\n   * Move adjacent neurons by precomputed alpha*(1-((i-j)^2/[r]^2)) in radpower[|i-j|]\n   */\n  private _alterNeighbour(\n    rad: number,\n    i: number,\n    b: number,\n    g: number,\n    r: number,\n    al: number,\n  ) {\n    let lo = i - rad;\n    if (lo < -1) lo = -1;\n\n    let hi = i + rad;\n    if (hi > this._networkSize) hi = this._networkSize;\n\n    let j = i + 1;\n    let k = i - 1;\n    let m = 1;\n\n    while (j < hi || k > lo) {\n      const a = this._radPower[m++] / NeuQuantFloat._alphaRadBias;\n      if (j < hi) {\n        const p = this._network[j++];\n        p.subtract(a * (p.r - r), a * (p.g - g), a * (p.b - b), a * (p.a - al));\n      }\n\n      if (k > lo) {\n        const p = this._network[k--];\n        p.subtract(a * (p.r - r), a * (p.g - g), a * (p.b - b), a * (p.a - al));\n      }\n    }\n  }\n\n  /**\n   * Move neuron i towards biased (b,g,r) by factor alpha\n   */\n  private _alterSingle(\n    alpha: number,\n    i: number,\n    b: number,\n    g: number,\n    r: number,\n    a: number,\n  ) {\n    alpha /= NeuQuantFloat._initAlpha;\n\n    /* alter hit neuron */\n    const n = this._network[i];\n    n.subtract(\n      alpha * (n.r - r),\n      alpha * (n.g - g),\n      alpha * (n.b - b),\n      alpha * (n.a - a),\n    );\n  }\n\n  /**\n   * Search for biased BGR values\n   * description:\n   *    finds closest neuron (min dist) and updates freq\n   *    finds best neuron (min dist-bias) and returns position\n   *    for frequently chosen neurons, freq[i] is high and bias[i] is negative\n   *    bias[i] = _gamma*((1/this._networkSize)-freq[i])\n   *\n   * Original distance equation:\n   *        dist = abs(dR) + abs(dG) + abs(dB)\n   */\n  private _contest(b: number, g: number, r: number, al: number) {\n    const multiplier = (255 * 4) << networkBiasShift;\n\n    let bestd = ~(1 << 31);\n    let bestbiasd = bestd;\n    let bestpos = -1;\n    let bestbiaspos = bestpos;\n\n    for (let i = 0; i < this._networkSize; i++) {\n      const n = this._network[i];\n      const dist =\n        this._distance.calculateNormalized(n, { r, g, b, a: al }) * multiplier;\n\n      if (dist < bestd) {\n        bestd = dist;\n        bestpos = i;\n      }\n\n      const biasdist =\n        dist -\n        (this._bias[i] >> (NeuQuantFloat._initialBiasShift - networkBiasShift));\n      if (biasdist < bestbiasd) {\n        bestbiasd = biasdist;\n        bestbiaspos = i;\n      }\n      const betafreq = this._freq[i] >> NeuQuantFloat._betaShift;\n      this._freq[i] -= betafreq;\n      this._bias[i] += betafreq << NeuQuantFloat._gammaShift;\n    }\n    this._freq[bestpos] += NeuQuantFloat._beta;\n    this._bias[bestpos] -= NeuQuantFloat._betaGamma;\n    return bestbiaspos;\n  }\n}\n", "/*\n * Copyright (c) 2015, <PERSON>\n * All rights reserved. (MIT Licensed)\n *\n * ColorHistogram.js - an image quantization lib\n */\n\n/**\n * @preserve TypeScript port:\n * Copyright 2015-2018 <PERSON>\n * All rights reserved. (MIT Licensed)\n *\n * colorHistogram.ts - part of Image Quantization Library\n */\nimport { HueStatistics } from '../../utils/hueStatistics';\nimport { PointContainer } from '../../utils/pointContainer';\nimport { stableSort } from '../../utils/arithmetic';\n\ninterface Box {\n  x: number;\n  y: number;\n  h: number;\n  w: number;\n}\n\nexport class ColorHistogram {\n  private static _boxSize = [64, 64];\n  private static _boxPixels = 2;\n  private static _hueGroups = 10;\n\n  // 1 = by global population, 2 = subregion population threshold\n  private _method: number;\n\n  // HueStatistics instance\n  private _hueStats: HueStatistics;\n\n  private _histogram: { [color: string]: number };\n\n  // # of highest-frequency colors to start with for palette reduction\n  private _initColors: number;\n\n  // if > 0, enables hues stats and min-color retention per group\n  private _minHueCols: number;\n\n  constructor(method: number, colors: number) {\n    // 1 = by global population, 2 = subregion population threshold\n    this._method = method;\n\n    // if > 0, enables hues stats and min-color retention per group\n    this._minHueCols = colors << 2; // opts.minHueCols || 0;\n\n    // # of highest-frequency colors to start with for palette reduction\n    this._initColors = colors << 2;\n\n    // HueStatistics instance\n    this._hueStats = new HueStatistics(\n      ColorHistogram._hueGroups,\n      this._minHueCols,\n    );\n\n    this._histogram = Object.create(null);\n  }\n\n  sample(pointContainer: PointContainer) {\n    switch (this._method) {\n      case 1:\n        this._colorStats1D(pointContainer);\n        break;\n      case 2:\n        this._colorStats2D(pointContainer);\n        break;\n    }\n  }\n\n  getImportanceSortedColorsIDXI32() {\n    // TODO: fix typing issue in stableSort func\n    const sorted = stableSort(\n      Object.keys(this._histogram),\n      (a, b) => this._histogram[b] - this._histogram[a],\n    );\n    if (sorted.length === 0) {\n      return [];\n    }\n\n    let idxi32;\n    switch (this._method) {\n      case 1:\n        const initialColorsLimit = Math.min(sorted.length, this._initColors);\n        const last = sorted[initialColorsLimit - 1];\n        const freq = this._histogram[last];\n\n        idxi32 = sorted.slice(0, initialColorsLimit);\n\n        // add any cut off colors with same freq as last\n        let pos = initialColorsLimit;\n        const len = sorted.length;\n        while (pos < len && this._histogram[sorted[pos]] === freq) {\n          idxi32.push(sorted[pos++]);\n        }\n\n        // inject min huegroup colors\n        this._hueStats.injectIntoArray(idxi32);\n        break;\n\n      case 2:\n        idxi32 = sorted;\n        break;\n\n      default:\n        // TODO: rethink errors\n        throw new Error('Incorrect method');\n    }\n\n    // int32-ify values\n    return idxi32.map((v) => +v);\n  }\n\n  // global top-population\n  private _colorStats1D(pointContainer: PointContainer) {\n    const histG = this._histogram;\n    const pointArray = pointContainer.getPointArray();\n    const len = pointArray.length;\n\n    for (let i = 0; i < len; i++) {\n      const col = pointArray[i].uint32;\n\n      // collect hue stats\n      this._hueStats.check(col);\n\n      if (col in histG) {\n        histG[col]++;\n      } else {\n        histG[col] = 1;\n      }\n    }\n  }\n\n  // population threshold within subregions\n  // FIXME: this can over-reduce (few/no colors same?), need a way to keep\n  // important colors that dont ever reach local thresholds (gradients?)\n  private _colorStats2D(pointContainer: PointContainer) {\n    const width = pointContainer.getWidth();\n    const height = pointContainer.getHeight();\n    const pointArray = pointContainer.getPointArray();\n\n    const boxW = ColorHistogram._boxSize[0];\n    const boxH = ColorHistogram._boxSize[1];\n    const area = boxW * boxH;\n    const boxes = this._makeBoxes(width, height, boxW, boxH);\n    const histG = this._histogram;\n\n    boxes.forEach((box) => {\n      let effc = Math.round((box.w * box.h) / area) * ColorHistogram._boxPixels;\n      if (effc < 2) effc = 2;\n\n      const histL: Record<string, number> = {};\n      this._iterateBox(box, width, (i) => {\n        const col = pointArray[i].uint32;\n\n        // collect hue stats\n        this._hueStats.check(col);\n\n        if (col in histG) {\n          histG[col]++;\n        } else if (col in histL) {\n          if (++histL[col] >= effc) {\n            histG[col] = histL[col];\n          }\n        } else {\n          histL[col] = 1;\n        }\n      });\n    });\n\n    // inject min huegroup colors\n    this._hueStats.injectIntoDictionary(histG);\n  }\n\n  // iterates @bbox within a parent rect of width @wid; calls @fn, passing index within parent\n  private _iterateBox(bbox: Box, wid: number, fn: (i: number) => void) {\n    const b = bbox;\n    const i0 = b.y * wid + b.x;\n    const i1 = (b.y + b.h - 1) * wid + (b.x + b.w - 1);\n    const incr = wid - b.w + 1;\n\n    let cnt = 0;\n    let i = i0;\n\n    do {\n      fn.call(this, i);\n      i += ++cnt % b.w === 0 ? incr : 1;\n    } while (i <= i1);\n  }\n\n  /**\n   *    partitions a rectangle of width x height into\n   *    array of boxes stepX x stepY (or less)\n   */\n  private _makeBoxes(\n    width: number,\n    height: number,\n    stepX: number,\n    stepY: number,\n  ) {\n    const wrem = width % stepX;\n    const hrem = height % stepY;\n    const xend = width - wrem;\n    const yend = height - hrem;\n    const boxesArray = [];\n\n    for (let y = 0; y < height; y += stepY) {\n      for (let x = 0; x < width; x += stepX) {\n        boxesArray.push({\n          x,\n          y,\n          w: x === xend ? wrem : stepX,\n          h: y === yend ? hrem : stepY,\n        });\n      }\n    }\n\n    return boxesArray;\n  }\n}\n", "/*\n * Copyright (c) 2015, <PERSON>\n * All rights reserved. (MIT Licensed)\n *\n * RGBQuant.js - an image quantization lib\n */\n\n/**\n * @preserve TypeScript port:\n * Copyright 2015-2018 <PERSON>\n * All rights reserved. (MIT Licensed)\n *\n * rgbquant.ts - part of Image Quantization Library\n */\n\nimport { Palette } from '../../utils/palette';\nimport { Point } from '../../utils/point';\nimport { PointContainer } from '../../utils/pointContainer';\nimport { AbstractDistanceCalculator } from '../../distance/distanceCalculator';\nimport { ColorHistogram } from './colorHistogram';\nimport { AbstractPaletteQuantizer } from '../paletteQuantizer';\nimport { PaletteQuantizerYieldValue } from '../paletteQuantizerYieldValue';\nimport { stableSort } from '../../utils/arithmetic';\nimport { ProgressTracker } from '../../utils';\n\nclass RemovedColor {\n  readonly index: number;\n  readonly color: Point;\n  readonly distance: number;\n\n  constructor(index: number, color: Point, distance: number) {\n    this.index = index;\n    this.color = color;\n    this.distance = distance;\n  }\n}\n\n// TODO: make input/output image and input/output palettes with instances of class Point only!\nexport class RGBQuant extends AbstractPaletteQuantizer {\n  // desired final palette size\n  private readonly _colors: number;\n\n  // color-distance threshold for initial reduction pass\n  private readonly _initialDistance: number;\n\n  // subsequent passes threshold\n  private readonly _distanceIncrement: number;\n\n  // accumulated histogram\n  private readonly _histogram: ColorHistogram;\n  private readonly _distance: AbstractDistanceCalculator;\n\n  constructor(\n    colorDistanceCalculator: AbstractDistanceCalculator,\n    colors = 256,\n    method = 2,\n  ) {\n    super();\n    this._distance = colorDistanceCalculator;\n    // desired final palette size\n    this._colors = colors;\n\n    // histogram to accumulate\n    this._histogram = new ColorHistogram(method, colors);\n\n    this._initialDistance = 0.01;\n    this._distanceIncrement = 0.005;\n  }\n\n  // gathers histogram info\n  sample(image: PointContainer) {\n    /*\n     var pointArray = image.getPointArray(), max = [0, 0, 0, 0], min = [255, 255, 255, 255];\n\n     for (var i = 0, l = pointArray.length; i < l; i++) {\n     var color = pointArray[i];\n     for (var componentIndex = 0; componentIndex < 4; componentIndex++) {\n     if (max[componentIndex] < color.rgba[componentIndex]) max[componentIndex] = color.rgba[componentIndex];\n     if (min[componentIndex] > color.rgba[componentIndex]) min[componentIndex] = color.rgba[componentIndex];\n     }\n     }\n     var rd = max[0] - min[0], gd = max[1] - min[1], bd = max[2] - min[2], ad = max[3] - min[3];\n     this._distance.setWhitePoint(rd, gd, bd, ad);\n\n     this._initialDistance = (Math.sqrt(rd * rd + gd * gd + bd * bd + ad * ad) / Math.sqrt(255 * 255 + 255 * 255 + 255 * 255)) * 0.01;\n     */\n\n    this._histogram.sample(image);\n  }\n\n  // reduces histogram to palette, remaps & memoizes reduced colors\n  *quantize() {\n    const idxi32 = this._histogram.getImportanceSortedColorsIDXI32();\n    if (idxi32.length === 0) {\n      throw new Error('No colors in image');\n    }\n\n    yield* this._buildPalette(idxi32);\n  }\n\n  // reduces similar colors from an importance-sorted Uint32 rgba array\n  private *_buildPalette(\n    idxi32: number[],\n  ): IterableIterator<PaletteQuantizerYieldValue> {\n    // reduce histogram to create initial palette\n    // build full rgb palette\n    const palette = new Palette();\n    const colorArray = palette.getPointContainer().getPointArray();\n    const usageArray = new Array(idxi32.length);\n\n    for (let i = 0; i < idxi32.length; i++) {\n      colorArray.push(Point.createByUint32(idxi32[i]));\n      usageArray[i] = 1;\n    }\n\n    const len = colorArray.length;\n    const memDist = [];\n\n    let palLen = len;\n    let thold = this._initialDistance;\n\n    // palette already at or below desired length\n    const tracker = new ProgressTracker(palLen - this._colors, 99);\n    while (palLen > this._colors) {\n      memDist.length = 0;\n\n      // iterate palette\n      for (let i = 0; i < len; i++) {\n        if (tracker.shouldNotify(len - palLen)) {\n          yield {\n            progress: tracker.progress,\n          };\n        }\n\n        if (usageArray[i] === 0) continue;\n        const pxi = colorArray[i];\n        // if (!pxi) continue;\n\n        for (let j = i + 1; j < len; j++) {\n          if (usageArray[j] === 0) continue;\n          const pxj = colorArray[j];\n          // if (!pxj) continue;\n\n          const dist = this._distance.calculateNormalized(pxi, pxj);\n          if (dist < thold) {\n            // store index,rgb,dist\n            memDist.push(new RemovedColor(j, pxj, dist));\n            usageArray[j] = 0;\n            palLen--;\n          }\n        }\n      }\n      // palette reduction pass\n      // console.log(\"palette length: \" + palLen);\n\n      // if palette is still much larger than target, increment by larger initDist\n      thold +=\n        palLen > this._colors * 3\n          ? this._initialDistance\n          : this._distanceIncrement;\n    }\n\n    // if palette is over-reduced, re-add removed colors with largest distances from last round\n    if (palLen < this._colors) {\n      // sort descending\n      stableSort(memDist, (a, b) => b.distance - a.distance);\n\n      let k = 0;\n      while (palLen < this._colors && k < memDist.length) {\n        const removedColor = memDist[k];\n        // re-inject rgb into final palette\n        usageArray[removedColor.index] = 1;\n        palLen++;\n        k++;\n      }\n    }\n\n    let colors = colorArray.length;\n    for (let colorIndex = colors - 1; colorIndex >= 0; colorIndex--) {\n      if (usageArray[colorIndex] === 0) {\n        if (colorIndex !== colors - 1) {\n          colorArray[colorIndex] = colorArray[colors - 1];\n        }\n        --colors;\n      }\n    }\n    colorArray.length = colors;\n\n    palette.sort();\n\n    yield {\n      palette,\n      progress: 100,\n    };\n  }\n}\n", "/**\n * @preserve\n * Copyright 2015-2018 <PERSON>\n * All rights reserved. (MIT Licensed)\n *\n * wuQuant.ts - part of Image Quantization Library\n */\nimport { Palette } from '../../utils/palette';\nimport { Point } from '../../utils/point';\nimport { PointContainer } from '../../utils/pointContainer';\nimport { AbstractDistanceCalculator } from '../../distance/distanceCalculator';\nimport { AbstractPaletteQuantizer } from '../paletteQuantizer';\nimport { PaletteQuantizerYieldValue } from '../paletteQuantizerYieldValue';\nimport { ProgressTracker } from '../../utils';\n\nfunction createArray1D(dimension1: number) {\n  const a = [];\n  for (let k = 0; k < dimension1; k++) {\n    a[k] = 0;\n  }\n  return a;\n}\n\nfunction createArray4D(\n  dimension1: number,\n  dimension2: number,\n  dimension3: number,\n  dimension4: number,\n): number[][][][] {\n  const a = new Array(dimension1);\n  for (let i = 0; i < dimension1; i++) {\n    a[i] = new Array(dimension2);\n    for (let j = 0; j < dimension2; j++) {\n      a[i][j] = new Array(dimension3);\n      for (let k = 0; k < dimension3; k++) {\n        a[i][j][k] = new Array(dimension4);\n        for (let l = 0; l < dimension4; l++) {\n          a[i][j][k][l] = 0;\n        }\n      }\n    }\n  }\n  return a;\n}\n\nfunction createArray3D(\n  dimension1: number,\n  dimension2: number,\n  dimension3: number,\n): number[][][] {\n  const a = new Array(dimension1);\n  for (let i = 0; i < dimension1; i++) {\n    a[i] = new Array(dimension2);\n    for (let j = 0; j < dimension2; j++) {\n      a[i][j] = new Array(dimension3);\n      for (let k = 0; k < dimension3; k++) {\n        a[i][j][k] = 0;\n      }\n    }\n  }\n  return a;\n}\n\nfunction fillArray3D<T>(\n  a: T[][][],\n  dimension1: number,\n  dimension2: number,\n  dimension3: number,\n  value: T,\n) {\n  for (let i = 0; i < dimension1; i++) {\n    a[i] = [];\n    for (let j = 0; j < dimension2; j++) {\n      a[i][j] = [];\n      for (let k = 0; k < dimension3; k++) {\n        a[i][j][k] = value;\n      }\n    }\n  }\n}\n\nfunction fillArray1D<T>(a: T[], dimension1: number, value: T) {\n  for (let i = 0; i < dimension1; i++) {\n    a[i] = value;\n  }\n}\n\nexport class WuColorCube {\n  redMinimum!: number;\n  redMaximum!: number;\n  greenMinimum!: number;\n  greenMaximum!: number;\n  blueMinimum!: number;\n  blueMaximum!: number;\n  volume!: number;\n  alphaMinimum!: number;\n  alphaMaximum!: number;\n}\n\nexport class WuQuant extends AbstractPaletteQuantizer {\n  private static readonly _alpha = 3;\n  private static readonly _red = 2;\n  private static readonly _green = 1;\n  private static readonly _blue = 0;\n\n  private _reds!: number[];\n  private _greens!: number[];\n  private _blues!: number[];\n  private _alphas!: number[];\n  private _sums!: number[];\n\n  private _weights!: number[][][][];\n  private _momentsRed!: number[][][][];\n  private _momentsGreen!: number[][][][];\n  private _momentsBlue!: number[][][][];\n  private _momentsAlpha!: number[][][][];\n  private _moments!: number[][][][];\n  private _table!: number[];\n  private _pixels!: Point[];\n\n  private _cubes!: WuColorCube[];\n  private _colors!: number;\n\n  private _significantBitsPerChannel!: number;\n  private _maxSideIndex!: number;\n  private _alphaMaxSideIndex!: number;\n  private _sideSize!: number;\n  private _alphaSideSize!: number;\n\n  private readonly _distance: AbstractDistanceCalculator;\n\n  constructor(\n    colorDistanceCalculator: AbstractDistanceCalculator,\n    colors = 256,\n    significantBitsPerChannel = 5,\n  ) {\n    super();\n    this._distance = colorDistanceCalculator;\n    this._setQuality(significantBitsPerChannel);\n    this._initialize(colors);\n  }\n\n  sample(image: PointContainer) {\n    const pointArray = image.getPointArray();\n\n    for (let i = 0, l = pointArray.length; i < l; i++) {\n      this._addColor(pointArray[i]);\n    }\n\n    this._pixels = this._pixels.concat(pointArray);\n  }\n\n  *quantize() {\n    yield* this._preparePalette();\n\n    const palette = new Palette();\n\n    // generates palette\n    for (let paletteIndex = 0; paletteIndex < this._colors; paletteIndex++) {\n      if (this._sums[paletteIndex] > 0) {\n        const sum = this._sums[paletteIndex];\n        const r = this._reds[paletteIndex] / sum;\n        const g = this._greens[paletteIndex] / sum;\n        const b = this._blues[paletteIndex] / sum;\n        const a = this._alphas[paletteIndex] / sum;\n\n        const color = Point.createByRGBA(r | 0, g | 0, b | 0, a | 0);\n        palette.add(color);\n      }\n    }\n\n    palette.sort();\n\n    yield {\n      palette,\n      progress: 100,\n    };\n  }\n\n  private *_preparePalette() {\n    // preprocess the colors\n    yield* this._calculateMoments();\n\n    let next = 0;\n    const volumeVariance = createArray1D(this._colors);\n\n    // processes the cubes\n    for (let cubeIndex = 1; cubeIndex < this._colors; ++cubeIndex) {\n      // if cut is possible; make it\n      if (this._cut(this._cubes[next], this._cubes[cubeIndex])) {\n        volumeVariance[next] =\n          this._cubes[next].volume > 1\n            ? this._calculateVariance(this._cubes[next])\n            : 0.0;\n        volumeVariance[cubeIndex] =\n          this._cubes[cubeIndex].volume > 1\n            ? this._calculateVariance(this._cubes[cubeIndex])\n            : 0.0;\n      } else {\n        // the cut was not possible, revert the index\n        volumeVariance[next] = 0.0;\n        cubeIndex--;\n      }\n\n      next = 0;\n      let temp = volumeVariance[0];\n\n      for (let index = 1; index <= cubeIndex; ++index) {\n        if (volumeVariance[index] > temp) {\n          temp = volumeVariance[index];\n          next = index;\n        }\n      }\n\n      if (temp <= 0.0) {\n        this._colors = cubeIndex + 1;\n        break;\n      }\n    }\n\n    const lookupRed = [];\n    const lookupGreen = [];\n    const lookupBlue = [];\n    const lookupAlpha = [];\n\n    // precalculates lookup tables\n    for (let k = 0; k < this._colors; ++k) {\n      const weight = WuQuant._volume(this._cubes[k], this._weights);\n\n      if (weight > 0) {\n        lookupRed[k] =\n          (WuQuant._volume(this._cubes[k], this._momentsRed) / weight) | 0;\n        lookupGreen[k] =\n          (WuQuant._volume(this._cubes[k], this._momentsGreen) / weight) | 0;\n        lookupBlue[k] =\n          (WuQuant._volume(this._cubes[k], this._momentsBlue) / weight) | 0;\n        lookupAlpha[k] =\n          (WuQuant._volume(this._cubes[k], this._momentsAlpha) / weight) | 0;\n      } else {\n        lookupRed[k] = 0;\n        lookupGreen[k] = 0;\n        lookupBlue[k] = 0;\n        lookupAlpha[k] = 0;\n      }\n    }\n\n    this._reds = createArray1D(this._colors + 1);\n    this._greens = createArray1D(this._colors + 1);\n    this._blues = createArray1D(this._colors + 1);\n    this._alphas = createArray1D(this._colors + 1);\n    this._sums = createArray1D(this._colors + 1);\n\n    // scans and adds colors\n    for (let index = 0, l = this._pixels.length; index < l; index++) {\n      const color = this._pixels[index];\n\n      const match = -1;\n\n      let bestMatch = match;\n      let bestDistance = Number.MAX_VALUE;\n\n      for (let lookup = 0; lookup < this._colors; lookup++) {\n        const foundRed = lookupRed[lookup];\n        const foundGreen = lookupGreen[lookup];\n        const foundBlue = lookupBlue[lookup];\n        const foundAlpha = lookupAlpha[lookup];\n\n        const distance = this._distance.calculateRaw(\n          foundRed,\n          foundGreen,\n          foundBlue,\n          foundAlpha,\n          color.r,\n          color.g,\n          color.b,\n          color.a,\n        );\n\n        if (distance < bestDistance) {\n          bestDistance = distance;\n          bestMatch = lookup;\n        }\n      }\n\n      this._reds[bestMatch] += color.r;\n      this._greens[bestMatch] += color.g;\n      this._blues[bestMatch] += color.b;\n      this._alphas[bestMatch] += color.a;\n      this._sums[bestMatch]++;\n    }\n  }\n\n  private _addColor(color: Point) {\n    const bitsToRemove = 8 - this._significantBitsPerChannel;\n    const indexRed = (color.r >> bitsToRemove) + 1;\n    const indexGreen = (color.g >> bitsToRemove) + 1;\n    const indexBlue = (color.b >> bitsToRemove) + 1;\n    const indexAlpha = (color.a >> bitsToRemove) + 1;\n\n    // if(color.a > 10) {\n    this._weights[indexAlpha][indexRed][indexGreen][indexBlue]++;\n    this._momentsRed[indexAlpha][indexRed][indexGreen][indexBlue] += color.r;\n    this._momentsGreen[indexAlpha][indexRed][indexGreen][indexBlue] += color.g;\n    this._momentsBlue[indexAlpha][indexRed][indexGreen][indexBlue] += color.b;\n    this._momentsAlpha[indexAlpha][indexRed][indexGreen][indexBlue] += color.a;\n    this._moments[indexAlpha][indexRed][indexGreen][indexBlue] +=\n      this._table[color.r] +\n      this._table[color.g] +\n      this._table[color.b] +\n      this._table[color.a];\n    // }\n  }\n\n  /**\n   * Converts the histogram to a series of _moments.\n   */\n  private *_calculateMoments(): IterableIterator<PaletteQuantizerYieldValue> {\n    const area: number[] = [];\n    const areaRed: number[] = [];\n    const areaGreen: number[] = [];\n    const areaBlue: number[] = [];\n    const areaAlpha: number[] = [];\n    const area2: number[] = [];\n\n    const xarea = createArray3D(this._sideSize, this._sideSize, this._sideSize);\n    const xareaRed = createArray3D(\n      this._sideSize,\n      this._sideSize,\n      this._sideSize,\n    );\n    const xareaGreen = createArray3D(\n      this._sideSize,\n      this._sideSize,\n      this._sideSize,\n    );\n    const xareaBlue = createArray3D(\n      this._sideSize,\n      this._sideSize,\n      this._sideSize,\n    );\n    const xareaAlpha = createArray3D(\n      this._sideSize,\n      this._sideSize,\n      this._sideSize,\n    );\n    const xarea2 = createArray3D(\n      this._sideSize,\n      this._sideSize,\n      this._sideSize,\n    );\n\n    let trackerProgress = 0;\n    const tracker = new ProgressTracker(\n      this._alphaMaxSideIndex * this._maxSideIndex,\n      99,\n    );\n\n    for (\n      let alphaIndex = 1;\n      alphaIndex <= this._alphaMaxSideIndex;\n      ++alphaIndex\n    ) {\n      fillArray3D<number>(\n        xarea,\n        this._sideSize,\n        this._sideSize,\n        this._sideSize,\n        0,\n      );\n      fillArray3D<number>(\n        xareaRed,\n        this._sideSize,\n        this._sideSize,\n        this._sideSize,\n        0,\n      );\n      fillArray3D<number>(\n        xareaGreen,\n        this._sideSize,\n        this._sideSize,\n        this._sideSize,\n        0,\n      );\n      fillArray3D<number>(\n        xareaBlue,\n        this._sideSize,\n        this._sideSize,\n        this._sideSize,\n        0,\n      );\n      fillArray3D<number>(\n        xareaAlpha,\n        this._sideSize,\n        this._sideSize,\n        this._sideSize,\n        0,\n      );\n      fillArray3D<number>(\n        xarea2,\n        this._sideSize,\n        this._sideSize,\n        this._sideSize,\n        0,\n      );\n\n      for (\n        let redIndex = 1;\n        redIndex <= this._maxSideIndex;\n        ++redIndex, ++trackerProgress\n      ) {\n        if (tracker.shouldNotify(trackerProgress)) {\n          yield {\n            progress: tracker.progress,\n          };\n        }\n\n        fillArray1D<number>(area, this._sideSize, 0);\n        fillArray1D<number>(areaRed, this._sideSize, 0);\n        fillArray1D<number>(areaGreen, this._sideSize, 0);\n        fillArray1D<number>(areaBlue, this._sideSize, 0);\n        fillArray1D<number>(areaAlpha, this._sideSize, 0);\n        fillArray1D<number>(area2, this._sideSize, 0);\n\n        for (\n          let greenIndex = 1;\n          greenIndex <= this._maxSideIndex;\n          ++greenIndex\n        ) {\n          let line = 0;\n          let lineRed = 0;\n          let lineGreen = 0;\n          let lineBlue = 0;\n          let lineAlpha = 0;\n          let line2 = 0.0;\n\n          for (\n            let blueIndex = 1;\n            blueIndex <= this._maxSideIndex;\n            ++blueIndex\n          ) {\n            line += this._weights[alphaIndex][redIndex][greenIndex][blueIndex];\n            lineRed +=\n              this._momentsRed[alphaIndex][redIndex][greenIndex][blueIndex];\n            lineGreen +=\n              this._momentsGreen[alphaIndex][redIndex][greenIndex][blueIndex];\n            lineBlue +=\n              this._momentsBlue[alphaIndex][redIndex][greenIndex][blueIndex];\n            lineAlpha +=\n              this._momentsAlpha[alphaIndex][redIndex][greenIndex][blueIndex];\n            line2 += this._moments[alphaIndex][redIndex][greenIndex][blueIndex];\n\n            area[blueIndex] += line;\n            areaRed[blueIndex] += lineRed;\n            areaGreen[blueIndex] += lineGreen;\n            areaBlue[blueIndex] += lineBlue;\n            areaAlpha[blueIndex] += lineAlpha;\n            area2[blueIndex] += line2;\n\n            xarea[redIndex][greenIndex][blueIndex] =\n              xarea[redIndex - 1][greenIndex][blueIndex] + area[blueIndex];\n            xareaRed[redIndex][greenIndex][blueIndex] =\n              xareaRed[redIndex - 1][greenIndex][blueIndex] +\n              areaRed[blueIndex];\n            xareaGreen[redIndex][greenIndex][blueIndex] =\n              xareaGreen[redIndex - 1][greenIndex][blueIndex] +\n              areaGreen[blueIndex];\n            xareaBlue[redIndex][greenIndex][blueIndex] =\n              xareaBlue[redIndex - 1][greenIndex][blueIndex] +\n              areaBlue[blueIndex];\n            xareaAlpha[redIndex][greenIndex][blueIndex] =\n              xareaAlpha[redIndex - 1][greenIndex][blueIndex] +\n              areaAlpha[blueIndex];\n            xarea2[redIndex][greenIndex][blueIndex] =\n              xarea2[redIndex - 1][greenIndex][blueIndex] + area2[blueIndex];\n\n            this._weights[alphaIndex][redIndex][greenIndex][blueIndex] =\n              this._weights[alphaIndex - 1][redIndex][greenIndex][blueIndex] +\n              xarea[redIndex][greenIndex][blueIndex];\n            this._momentsRed[alphaIndex][redIndex][greenIndex][blueIndex] =\n              this._momentsRed[alphaIndex - 1][redIndex][greenIndex][\n                blueIndex\n              ] + xareaRed[redIndex][greenIndex][blueIndex];\n            this._momentsGreen[alphaIndex][redIndex][greenIndex][blueIndex] =\n              this._momentsGreen[alphaIndex - 1][redIndex][greenIndex][\n                blueIndex\n              ] + xareaGreen[redIndex][greenIndex][blueIndex];\n            this._momentsBlue[alphaIndex][redIndex][greenIndex][blueIndex] =\n              this._momentsBlue[alphaIndex - 1][redIndex][greenIndex][\n                blueIndex\n              ] + xareaBlue[redIndex][greenIndex][blueIndex];\n            this._momentsAlpha[alphaIndex][redIndex][greenIndex][blueIndex] =\n              this._momentsAlpha[alphaIndex - 1][redIndex][greenIndex][\n                blueIndex\n              ] + xareaAlpha[redIndex][greenIndex][blueIndex];\n            this._moments[alphaIndex][redIndex][greenIndex][blueIndex] =\n              this._moments[alphaIndex - 1][redIndex][greenIndex][blueIndex] +\n              xarea2[redIndex][greenIndex][blueIndex];\n          }\n        }\n      }\n    }\n  }\n\n  /**\n   * Computes the volume of the cube in a specific moment.\n   */\n  private static _volumeFloat(cube: WuColorCube, moment: number[][][][]) {\n    return (\n      moment[cube.alphaMaximum][cube.redMaximum][cube.greenMaximum][\n        cube.blueMaximum\n      ] -\n      moment[cube.alphaMaximum][cube.redMaximum][cube.greenMinimum][\n        cube.blueMaximum\n      ] -\n      moment[cube.alphaMaximum][cube.redMinimum][cube.greenMaximum][\n        cube.blueMaximum\n      ] +\n      moment[cube.alphaMaximum][cube.redMinimum][cube.greenMinimum][\n        cube.blueMaximum\n      ] -\n      moment[cube.alphaMinimum][cube.redMaximum][cube.greenMaximum][\n        cube.blueMaximum\n      ] +\n      moment[cube.alphaMinimum][cube.redMaximum][cube.greenMinimum][\n        cube.blueMaximum\n      ] +\n      moment[cube.alphaMinimum][cube.redMinimum][cube.greenMaximum][\n        cube.blueMaximum\n      ] -\n      moment[cube.alphaMinimum][cube.redMinimum][cube.greenMinimum][\n        cube.blueMaximum\n      ] -\n      (moment[cube.alphaMaximum][cube.redMaximum][cube.greenMaximum][\n        cube.blueMinimum\n      ] -\n        moment[cube.alphaMinimum][cube.redMaximum][cube.greenMaximum][\n          cube.blueMinimum\n        ] -\n        moment[cube.alphaMaximum][cube.redMaximum][cube.greenMinimum][\n          cube.blueMinimum\n        ] +\n        moment[cube.alphaMinimum][cube.redMaximum][cube.greenMinimum][\n          cube.blueMinimum\n        ] -\n        moment[cube.alphaMaximum][cube.redMinimum][cube.greenMaximum][\n          cube.blueMinimum\n        ] +\n        moment[cube.alphaMinimum][cube.redMinimum][cube.greenMaximum][\n          cube.blueMinimum\n        ] +\n        moment[cube.alphaMaximum][cube.redMinimum][cube.greenMinimum][\n          cube.blueMinimum\n        ] -\n        moment[cube.alphaMinimum][cube.redMinimum][cube.greenMinimum][\n          cube.blueMinimum\n        ])\n    );\n  }\n\n  /**\n   * Computes the volume of the cube in a specific moment.\n   */\n  private static _volume(cube: WuColorCube, moment: number[][][][]) {\n    return WuQuant._volumeFloat(cube, moment) | 0;\n  }\n\n  /**\n   * Splits the cube in given position][and color direction.\n   */\n  private static _top(\n    cube: WuColorCube,\n    direction: number,\n    position: number,\n    moment: number[][][][],\n  ) {\n    let result;\n    switch (direction) {\n      case WuQuant._alpha:\n        result =\n          moment[position][cube.redMaximum][cube.greenMaximum][\n            cube.blueMaximum\n          ] -\n          moment[position][cube.redMaximum][cube.greenMinimum][\n            cube.blueMaximum\n          ] -\n          moment[position][cube.redMinimum][cube.greenMaximum][\n            cube.blueMaximum\n          ] +\n          moment[position][cube.redMinimum][cube.greenMinimum][\n            cube.blueMaximum\n          ] -\n          (moment[position][cube.redMaximum][cube.greenMaximum][\n            cube.blueMinimum\n          ] -\n            moment[position][cube.redMaximum][cube.greenMinimum][\n              cube.blueMinimum\n            ] -\n            moment[position][cube.redMinimum][cube.greenMaximum][\n              cube.blueMinimum\n            ] +\n            moment[position][cube.redMinimum][cube.greenMinimum][\n              cube.blueMinimum\n            ]);\n        break;\n\n      case WuQuant._red:\n        result =\n          moment[cube.alphaMaximum][position][cube.greenMaximum][\n            cube.blueMaximum\n          ] -\n          moment[cube.alphaMaximum][position][cube.greenMinimum][\n            cube.blueMaximum\n          ] -\n          moment[cube.alphaMinimum][position][cube.greenMaximum][\n            cube.blueMaximum\n          ] +\n          moment[cube.alphaMinimum][position][cube.greenMinimum][\n            cube.blueMaximum\n          ] -\n          (moment[cube.alphaMaximum][position][cube.greenMaximum][\n            cube.blueMinimum\n          ] -\n            moment[cube.alphaMaximum][position][cube.greenMinimum][\n              cube.blueMinimum\n            ] -\n            moment[cube.alphaMinimum][position][cube.greenMaximum][\n              cube.blueMinimum\n            ] +\n            moment[cube.alphaMinimum][position][cube.greenMinimum][\n              cube.blueMinimum\n            ]);\n        break;\n\n      case WuQuant._green:\n        result =\n          moment[cube.alphaMaximum][cube.redMaximum][position][\n            cube.blueMaximum\n          ] -\n          moment[cube.alphaMaximum][cube.redMinimum][position][\n            cube.blueMaximum\n          ] -\n          moment[cube.alphaMinimum][cube.redMaximum][position][\n            cube.blueMaximum\n          ] +\n          moment[cube.alphaMinimum][cube.redMinimum][position][\n            cube.blueMaximum\n          ] -\n          (moment[cube.alphaMaximum][cube.redMaximum][position][\n            cube.blueMinimum\n          ] -\n            moment[cube.alphaMaximum][cube.redMinimum][position][\n              cube.blueMinimum\n            ] -\n            moment[cube.alphaMinimum][cube.redMaximum][position][\n              cube.blueMinimum\n            ] +\n            moment[cube.alphaMinimum][cube.redMinimum][position][\n              cube.blueMinimum\n            ]);\n        break;\n\n      case WuQuant._blue:\n        result =\n          moment[cube.alphaMaximum][cube.redMaximum][cube.greenMaximum][\n            position\n          ] -\n          moment[cube.alphaMaximum][cube.redMaximum][cube.greenMinimum][\n            position\n          ] -\n          moment[cube.alphaMaximum][cube.redMinimum][cube.greenMaximum][\n            position\n          ] +\n          moment[cube.alphaMaximum][cube.redMinimum][cube.greenMinimum][\n            position\n          ] -\n          (moment[cube.alphaMinimum][cube.redMaximum][cube.greenMaximum][\n            position\n          ] -\n            moment[cube.alphaMinimum][cube.redMaximum][cube.greenMinimum][\n              position\n            ] -\n            moment[cube.alphaMinimum][cube.redMinimum][cube.greenMaximum][\n              position\n            ] +\n            moment[cube.alphaMinimum][cube.redMinimum][cube.greenMinimum][\n              position\n            ]);\n        break;\n      default:\n        throw new Error('impossible');\n    }\n\n    return result | 0;\n  }\n\n  /**\n   * Splits the cube in a given color direction at its minimum.\n   */\n  private static _bottom(\n    cube: WuColorCube,\n    direction: number,\n    moment: number[][][][],\n  ) {\n    switch (direction) {\n      case WuQuant._alpha:\n        return (\n          -moment[cube.alphaMinimum][cube.redMaximum][cube.greenMaximum][\n            cube.blueMaximum\n          ] +\n          moment[cube.alphaMinimum][cube.redMaximum][cube.greenMinimum][\n            cube.blueMaximum\n          ] +\n          moment[cube.alphaMinimum][cube.redMinimum][cube.greenMaximum][\n            cube.blueMaximum\n          ] -\n          moment[cube.alphaMinimum][cube.redMinimum][cube.greenMinimum][\n            cube.blueMaximum\n          ] -\n          (-moment[cube.alphaMinimum][cube.redMaximum][cube.greenMaximum][\n            cube.blueMinimum\n          ] +\n            moment[cube.alphaMinimum][cube.redMaximum][cube.greenMinimum][\n              cube.blueMinimum\n            ] +\n            moment[cube.alphaMinimum][cube.redMinimum][cube.greenMaximum][\n              cube.blueMinimum\n            ] -\n            moment[cube.alphaMinimum][cube.redMinimum][cube.greenMinimum][\n              cube.blueMinimum\n            ])\n        );\n\n      case WuQuant._red:\n        return (\n          -moment[cube.alphaMaximum][cube.redMinimum][cube.greenMaximum][\n            cube.blueMaximum\n          ] +\n          moment[cube.alphaMaximum][cube.redMinimum][cube.greenMinimum][\n            cube.blueMaximum\n          ] +\n          moment[cube.alphaMinimum][cube.redMinimum][cube.greenMaximum][\n            cube.blueMaximum\n          ] -\n          moment[cube.alphaMinimum][cube.redMinimum][cube.greenMinimum][\n            cube.blueMaximum\n          ] -\n          (-moment[cube.alphaMaximum][cube.redMinimum][cube.greenMaximum][\n            cube.blueMinimum\n          ] +\n            moment[cube.alphaMaximum][cube.redMinimum][cube.greenMinimum][\n              cube.blueMinimum\n            ] +\n            moment[cube.alphaMinimum][cube.redMinimum][cube.greenMaximum][\n              cube.blueMinimum\n            ] -\n            moment[cube.alphaMinimum][cube.redMinimum][cube.greenMinimum][\n              cube.blueMinimum\n            ])\n        );\n\n      case WuQuant._green:\n        return (\n          -moment[cube.alphaMaximum][cube.redMaximum][cube.greenMinimum][\n            cube.blueMaximum\n          ] +\n          moment[cube.alphaMaximum][cube.redMinimum][cube.greenMinimum][\n            cube.blueMaximum\n          ] +\n          moment[cube.alphaMinimum][cube.redMaximum][cube.greenMinimum][\n            cube.blueMaximum\n          ] -\n          moment[cube.alphaMinimum][cube.redMinimum][cube.greenMinimum][\n            cube.blueMaximum\n          ] -\n          (-moment[cube.alphaMaximum][cube.redMaximum][cube.greenMinimum][\n            cube.blueMinimum\n          ] +\n            moment[cube.alphaMaximum][cube.redMinimum][cube.greenMinimum][\n              cube.blueMinimum\n            ] +\n            moment[cube.alphaMinimum][cube.redMaximum][cube.greenMinimum][\n              cube.blueMinimum\n            ] -\n            moment[cube.alphaMinimum][cube.redMinimum][cube.greenMinimum][\n              cube.blueMinimum\n            ])\n        );\n\n      case WuQuant._blue:\n        return (\n          -moment[cube.alphaMaximum][cube.redMaximum][cube.greenMaximum][\n            cube.blueMinimum\n          ] +\n          moment[cube.alphaMaximum][cube.redMaximum][cube.greenMinimum][\n            cube.blueMinimum\n          ] +\n          moment[cube.alphaMaximum][cube.redMinimum][cube.greenMaximum][\n            cube.blueMinimum\n          ] -\n          moment[cube.alphaMaximum][cube.redMinimum][cube.greenMinimum][\n            cube.blueMinimum\n          ] -\n          (-moment[cube.alphaMinimum][cube.redMaximum][cube.greenMaximum][\n            cube.blueMinimum\n          ] +\n            moment[cube.alphaMinimum][cube.redMaximum][cube.greenMinimum][\n              cube.blueMinimum\n            ] +\n            moment[cube.alphaMinimum][cube.redMinimum][cube.greenMaximum][\n              cube.blueMinimum\n            ] -\n            moment[cube.alphaMinimum][cube.redMinimum][cube.greenMinimum][\n              cube.blueMinimum\n            ])\n        );\n\n      default:\n        // TODO: why here is return 0, and in this._top there is no default at all (now it is throw error)?\n        return 0;\n    }\n  }\n\n  /**\n   * Calculates statistical variance for a given cube.\n   */\n  private _calculateVariance(cube: WuColorCube) {\n    const volumeRed = WuQuant._volume(cube, this._momentsRed);\n    const volumeGreen = WuQuant._volume(cube, this._momentsGreen);\n    const volumeBlue = WuQuant._volume(cube, this._momentsBlue);\n    const volumeAlpha = WuQuant._volume(cube, this._momentsAlpha);\n    const volumeMoment = WuQuant._volumeFloat(cube, this._moments);\n    const volumeWeight = WuQuant._volume(cube, this._weights);\n    const distance =\n      volumeRed * volumeRed +\n      volumeGreen * volumeGreen +\n      volumeBlue * volumeBlue +\n      volumeAlpha * volumeAlpha;\n\n    return volumeMoment - distance / volumeWeight;\n  }\n\n  /**\n   * Finds the optimal (maximal) position for the cut.\n   */\n  private _maximize(\n    cube: WuColorCube,\n    direction: number,\n    first: number,\n    last: number,\n    wholeRed: number,\n    wholeGreen: number,\n    wholeBlue: number,\n    wholeAlpha: number,\n    wholeWeight: number,\n  ) {\n    const bottomRed = WuQuant._bottom(cube, direction, this._momentsRed) | 0;\n    const bottomGreen =\n      WuQuant._bottom(cube, direction, this._momentsGreen) | 0;\n    const bottomBlue = WuQuant._bottom(cube, direction, this._momentsBlue) | 0;\n    const bottomAlpha =\n      WuQuant._bottom(cube, direction, this._momentsAlpha) | 0;\n    const bottomWeight = WuQuant._bottom(cube, direction, this._weights) | 0;\n\n    let result = 0.0;\n    let cutPosition = -1;\n\n    for (let position = first; position < last; ++position) {\n      // determines the cube cut at a certain position\n      let halfRed =\n        bottomRed + WuQuant._top(cube, direction, position, this._momentsRed);\n      let halfGreen =\n        bottomGreen +\n        WuQuant._top(cube, direction, position, this._momentsGreen);\n      let halfBlue =\n        bottomBlue + WuQuant._top(cube, direction, position, this._momentsBlue);\n      let halfAlpha =\n        bottomAlpha +\n        WuQuant._top(cube, direction, position, this._momentsAlpha);\n      let halfWeight =\n        bottomWeight + WuQuant._top(cube, direction, position, this._weights);\n\n      // the cube cannot be cut at bottom (this would lead to empty cube)\n      if (halfWeight !== 0) {\n        let halfDistance =\n          halfRed * halfRed +\n          halfGreen * halfGreen +\n          halfBlue * halfBlue +\n          halfAlpha * halfAlpha;\n        let temp = halfDistance / halfWeight;\n\n        halfRed = wholeRed - halfRed;\n        halfGreen = wholeGreen - halfGreen;\n        halfBlue = wholeBlue - halfBlue;\n        halfAlpha = wholeAlpha - halfAlpha;\n        halfWeight = wholeWeight - halfWeight;\n\n        if (halfWeight !== 0) {\n          halfDistance =\n            halfRed * halfRed +\n            halfGreen * halfGreen +\n            halfBlue * halfBlue +\n            halfAlpha * halfAlpha;\n          temp += halfDistance / halfWeight;\n\n          if (temp > result) {\n            result = temp;\n            cutPosition = position;\n          }\n        }\n      }\n    }\n\n    return { max: result, position: cutPosition };\n  }\n\n  // Cuts a cube with another one.\n  private _cut(first: WuColorCube, second: WuColorCube) {\n    let direction;\n\n    const wholeRed = WuQuant._volume(first, this._momentsRed);\n    const wholeGreen = WuQuant._volume(first, this._momentsGreen);\n    const wholeBlue = WuQuant._volume(first, this._momentsBlue);\n    const wholeAlpha = WuQuant._volume(first, this._momentsAlpha);\n    const wholeWeight = WuQuant._volume(first, this._weights);\n\n    const red = this._maximize(\n      first,\n      WuQuant._red,\n      first.redMinimum + 1,\n      first.redMaximum,\n      wholeRed,\n      wholeGreen,\n      wholeBlue,\n      wholeAlpha,\n      wholeWeight,\n    );\n    const green = this._maximize(\n      first,\n      WuQuant._green,\n      first.greenMinimum + 1,\n      first.greenMaximum,\n      wholeRed,\n      wholeGreen,\n      wholeBlue,\n      wholeAlpha,\n      wholeWeight,\n    );\n    const blue = this._maximize(\n      first,\n      WuQuant._blue,\n      first.blueMinimum + 1,\n      first.blueMaximum,\n      wholeRed,\n      wholeGreen,\n      wholeBlue,\n      wholeAlpha,\n      wholeWeight,\n    );\n    const alpha = this._maximize(\n      first,\n      WuQuant._alpha,\n      first.alphaMinimum + 1,\n      first.alphaMaximum,\n      wholeRed,\n      wholeGreen,\n      wholeBlue,\n      wholeAlpha,\n      wholeWeight,\n    );\n\n    if (\n      alpha.max >= red.max &&\n      alpha.max >= green.max &&\n      alpha.max >= blue.max\n    ) {\n      direction = WuQuant._alpha;\n\n      // cannot split empty cube\n      if (alpha.position < 0) return false;\n    } else if (\n      red.max >= alpha.max &&\n      red.max >= green.max &&\n      red.max >= blue.max\n    ) {\n      direction = WuQuant._red;\n    } else if (\n      green.max >= alpha.max &&\n      green.max >= red.max &&\n      green.max >= blue.max\n    ) {\n      direction = WuQuant._green;\n    } else {\n      direction = WuQuant._blue;\n    }\n\n    second.redMaximum = first.redMaximum;\n    second.greenMaximum = first.greenMaximum;\n    second.blueMaximum = first.blueMaximum;\n    second.alphaMaximum = first.alphaMaximum;\n\n    // cuts in a certain direction\n    switch (direction) {\n      case WuQuant._red:\n        second.redMinimum = first.redMaximum = red.position;\n        second.greenMinimum = first.greenMinimum;\n        second.blueMinimum = first.blueMinimum;\n        second.alphaMinimum = first.alphaMinimum;\n        break;\n\n      case WuQuant._green:\n        second.greenMinimum = first.greenMaximum = green.position;\n        second.redMinimum = first.redMinimum;\n        second.blueMinimum = first.blueMinimum;\n        second.alphaMinimum = first.alphaMinimum;\n        break;\n\n      case WuQuant._blue:\n        second.blueMinimum = first.blueMaximum = blue.position;\n        second.redMinimum = first.redMinimum;\n        second.greenMinimum = first.greenMinimum;\n        second.alphaMinimum = first.alphaMinimum;\n        break;\n\n      case WuQuant._alpha:\n        second.alphaMinimum = first.alphaMaximum = alpha.position;\n        second.blueMinimum = first.blueMinimum;\n        second.redMinimum = first.redMinimum;\n        second.greenMinimum = first.greenMinimum;\n        break;\n    }\n\n    // determines the volumes after cut\n    first.volume =\n      (first.redMaximum - first.redMinimum) *\n      (first.greenMaximum - first.greenMinimum) *\n      (first.blueMaximum - first.blueMinimum) *\n      (first.alphaMaximum - first.alphaMinimum);\n    second.volume =\n      (second.redMaximum - second.redMinimum) *\n      (second.greenMaximum - second.greenMinimum) *\n      (second.blueMaximum - second.blueMinimum) *\n      (second.alphaMaximum - second.alphaMinimum);\n\n    // the cut was successful\n    return true;\n  }\n\n  private _initialize(colors: number) {\n    this._colors = colors;\n\n    // creates all the _cubes\n    this._cubes = [];\n\n    // initializes all the _cubes\n    for (let cubeIndex = 0; cubeIndex < colors; cubeIndex++) {\n      this._cubes[cubeIndex] = new WuColorCube();\n    }\n\n    // resets the reference minimums\n    this._cubes[0].redMinimum = 0;\n    this._cubes[0].greenMinimum = 0;\n    this._cubes[0].blueMinimum = 0;\n    this._cubes[0].alphaMinimum = 0;\n\n    // resets the reference maximums\n    this._cubes[0].redMaximum = this._maxSideIndex;\n    this._cubes[0].greenMaximum = this._maxSideIndex;\n    this._cubes[0].blueMaximum = this._maxSideIndex;\n    this._cubes[0].alphaMaximum = this._alphaMaxSideIndex;\n\n    this._weights = createArray4D(\n      this._alphaSideSize,\n      this._sideSize,\n      this._sideSize,\n      this._sideSize,\n    );\n    this._momentsRed = createArray4D(\n      this._alphaSideSize,\n      this._sideSize,\n      this._sideSize,\n      this._sideSize,\n    );\n    this._momentsGreen = createArray4D(\n      this._alphaSideSize,\n      this._sideSize,\n      this._sideSize,\n      this._sideSize,\n    );\n    this._momentsBlue = createArray4D(\n      this._alphaSideSize,\n      this._sideSize,\n      this._sideSize,\n      this._sideSize,\n    );\n    this._momentsAlpha = createArray4D(\n      this._alphaSideSize,\n      this._sideSize,\n      this._sideSize,\n      this._sideSize,\n    );\n    this._moments = createArray4D(\n      this._alphaSideSize,\n      this._sideSize,\n      this._sideSize,\n      this._sideSize,\n    );\n\n    this._table = [];\n    for (let tableIndex = 0; tableIndex < 256; ++tableIndex) {\n      this._table[tableIndex] = tableIndex * tableIndex;\n    }\n\n    this._pixels = [];\n  }\n\n  private _setQuality(significantBitsPerChannel = 5) {\n    this._significantBitsPerChannel = significantBitsPerChannel;\n    this._maxSideIndex = 1 << this._significantBitsPerChannel;\n    this._alphaMaxSideIndex = this._maxSideIndex;\n\n    this._sideSize = this._maxSideIndex + 1;\n    this._alphaSideSize = this._alphaMaxSideIndex + 1;\n  }\n}\n", "/**\n * @preserve\n * Copyright 2015-2018 <PERSON>\n * All rights reserved. (MIT Licensed)\n *\n * iq.ts - Image Quantization Library\n */\n\nexport { AbstractImageQuantizer } from './imageQuantizer';\nexport { ImageQuantizerYieldValue } from './imageQuantizerYieldValue';\nexport { NearestColor } from './nearestColor';\nexport { ErrorDiffusionArray, ErrorDiffusionArrayKernel } from './array';\nexport { ErrorDiffusionRiemersma } from './riemersma';\n", "/**\n * @preserve\n * Copyright 2015-2018 <PERSON>\n * All rights reserved. (MIT Licensed)\n *\n * common.ts - part of Image Quantization Library\n */\nimport { PointContainer } from '../utils/pointContainer';\nimport { Palette } from '../utils/palette';\nimport { ImageQuantizerYieldValue } from './imageQuantizerYieldValue';\n\nexport abstract class AbstractImageQuantizer {\n  abstract quantize(\n    pointContainer: PointContainer,\n    palette: Palette,\n  ): IterableIterator<ImageQuantizerYieldValue>;\n\n  quantizeSync(pointContainer: PointContainer, palette: Palette) {\n    for (const value of this.quantize(pointContainer, palette)) {\n      if (value.pointContainer) {\n        return value.pointContainer;\n      }\n    }\n\n    throw new Error('unreachable');\n  }\n}\n", "/**\n * @preserve\n * Copyright 2015-2018 <PERSON>\n * All rights reserved. (MIT Licensed)\n *\n * nearestColor.ts - part of Image Quantization Library\n */\nimport { AbstractImageQuantizer } from './imageQuantizer';\nimport { AbstractDistanceCalculator } from '../distance/distanceCalculator';\nimport { PointContainer } from '../utils/pointContainer';\nimport { Palette } from '../utils/palette';\nimport { ImageQuantizerYieldValue } from './imageQuantizerYieldValue';\nimport { ProgressTracker } from '../utils/progressTracker';\n\nexport class NearestColor extends AbstractImageQuantizer {\n  private _distance: AbstractDistanceCalculator;\n\n  constructor(colorDistanceCalculator: AbstractDistanceCalculator) {\n    super();\n    this._distance = colorDistanceCalculator;\n  }\n\n  /**\n   * Mutates pointContainer\n   */\n  *quantize(\n    pointContainer: PointContainer,\n    palette: Palette,\n  ): IterableIterator<ImageQuantizerYieldValue> {\n    const pointArray = pointContainer.getPointArray();\n    const width = pointContainer.getWidth();\n    const height = pointContainer.getHeight();\n\n    const tracker = new ProgressTracker(height, 99);\n    for (let y = 0; y < height; y++) {\n      if (tracker.shouldNotify(y)) {\n        yield {\n          progress: tracker.progress,\n        };\n      }\n      for (let x = 0, idx = y * width; x < width; x++, idx++) {\n        // Image pixel\n        const point = pointArray[idx];\n        // Reduced pixel\n        point.from(palette.getNearestColor(this._distance, point));\n      }\n    }\n\n    yield {\n      pointContainer,\n      progress: 100,\n    };\n  }\n}\n", "/**\n * @preserve\n * Copyright 2015-2018 <PERSON>\n * All rights reserved. (MIT Licensed)\n *\n * ditherErrorDiffusionArray.ts - part of Image Quantization Library\n */\nimport { AbstractImageQuantizer } from './imageQuantizer';\nimport { AbstractDistanceCalculator } from '../distance/distanceCalculator';\nimport { PointContainer } from '../utils/pointContainer';\nimport { Palette } from '../utils/palette';\nimport { Point } from '../utils/point';\nimport { inRange0to255Rounded } from '../utils/arithmetic';\nimport { ImageQuantizerYieldValue } from './imageQuantizerYieldValue';\nimport { ProgressTracker } from '../utils/progressTracker';\n\n// TODO: is it the best name for this enum \"kernel\"?\nexport enum ErrorDiffusionArrayKernel {\n  FloydSteinberg = 0,\n  FalseFloyd<PERSON><PERSON><PERSON>,\n  <PERSON>ucki,\n  <PERSON>,\n  <PERSON>,\n  <PERSON>,\n  Sierra,\n  TwoSierra,\n  SierraLite,\n}\n\n// http://www.tannerhelland.com/4660/dithering-eleven-algorithms-source-code/\nexport class ErrorDiffusionArray extends AbstractImageQuantizer {\n  private _minColorDistance: number;\n  private _serpentine: boolean;\n  private _kernel!: number[][];\n  /** true = GIMP, false = XNVIEW */\n  private _calculateErrorLikeGIMP: boolean;\n\n  private _distance: AbstractDistanceCalculator;\n\n  constructor(\n    colorDistanceCalculator: AbstractDistanceCalculator,\n    kernel: ErrorDiffusionArrayKernel,\n    serpentine = true,\n    minimumColorDistanceToDither = 0,\n    calculateErrorLikeGIMP = false,\n  ) {\n    super();\n    this._setKernel(kernel);\n\n    this._distance = colorDistanceCalculator;\n    this._minColorDistance = minimumColorDistanceToDither;\n    this._serpentine = serpentine;\n    this._calculateErrorLikeGIMP = calculateErrorLikeGIMP;\n  }\n\n  /**\n   * adapted from http://jsbin.com/iXofIji/2/edit by PAEz\n   * fixed version. it doesn't use image pixels as error storage, also it doesn't have 0.3 + 0.3 + 0.3 + 0.3 = 0 error\n   * Mutates pointContainer\n   */\n  *quantize(\n    pointContainer: PointContainer,\n    palette: Palette,\n  ): IterableIterator<ImageQuantizerYieldValue> {\n    const pointArray = pointContainer.getPointArray();\n    const originalPoint = new Point();\n    const width = pointContainer.getWidth();\n    const height = pointContainer.getHeight();\n    const errorLines: number[][][] = [];\n\n    let dir = 1;\n    let maxErrorLines = 1;\n\n    // initial error lines (number is taken from dithering kernel)\n    for (const kernel of this._kernel) {\n      const kernelErrorLines = kernel[2] + 1;\n      if (maxErrorLines < kernelErrorLines) maxErrorLines = kernelErrorLines;\n    }\n    for (let i = 0; i < maxErrorLines; i++) {\n      this._fillErrorLine((errorLines[i] = []), width);\n    }\n\n    const tracker = new ProgressTracker(height, 99);\n    for (let y = 0; y < height; y++) {\n      if (tracker.shouldNotify(y)) {\n        yield {\n          progress: tracker.progress,\n        };\n      }\n\n      // always serpentine\n      if (this._serpentine) dir *= -1;\n\n      const lni = y * width;\n      const xStart = dir === 1 ? 0 : width - 1;\n      const xEnd = dir === 1 ? width : -1;\n\n      // cyclic shift with erasing\n      this._fillErrorLine(errorLines[0], width);\n      // TODO: why it is needed to cast types here?\n      errorLines.push(errorLines.shift() as number[][]);\n\n      const errorLine = errorLines[0];\n      for (\n        let x = xStart, idx = lni + xStart;\n        x !== xEnd;\n        x += dir, idx += dir\n      ) {\n        // Image pixel\n        const point = pointArray[idx];\n        // originalPoint = new Utils.Point(),\n        const error = errorLine[x];\n\n        originalPoint.from(point);\n\n        const correctedPoint = Point.createByRGBA(\n          inRange0to255Rounded(point.r + error[0]),\n          inRange0to255Rounded(point.g + error[1]),\n          inRange0to255Rounded(point.b + error[2]),\n          inRange0to255Rounded(point.a + error[3]),\n        );\n\n        // Reduced pixel\n        const palettePoint = palette.getNearestColor(\n          this._distance,\n          correctedPoint,\n        );\n        point.from(palettePoint);\n\n        // dithering strength\n        if (this._minColorDistance) {\n          const dist = this._distance.calculateNormalized(\n            originalPoint,\n            palettePoint,\n          );\n          if (dist < this._minColorDistance) continue;\n        }\n\n        // Component distance\n        let er;\n        let eg;\n        let eb;\n        let ea;\n        if (this._calculateErrorLikeGIMP) {\n          er = correctedPoint.r - palettePoint.r;\n          eg = correctedPoint.g - palettePoint.g;\n          eb = correctedPoint.b - palettePoint.b;\n          ea = correctedPoint.a - palettePoint.a;\n        } else {\n          er = originalPoint.r - palettePoint.r;\n          eg = originalPoint.g - palettePoint.g;\n          eb = originalPoint.b - palettePoint.b;\n          ea = originalPoint.a - palettePoint.a;\n        }\n\n        const dStart = dir === 1 ? 0 : this._kernel.length - 1;\n        const dEnd = dir === 1 ? this._kernel.length : -1;\n\n        for (let i = dStart; i !== dEnd; i += dir) {\n          const x1 = this._kernel[i][1] * dir;\n          const y1 = this._kernel[i][2];\n\n          if (x1 + x >= 0 && x1 + x < width && y1 + y >= 0 && y1 + y < height) {\n            const d = this._kernel[i][0];\n            const e = errorLines[y1][x1 + x];\n\n            e[0] += er * d;\n            e[1] += eg * d;\n            e[2] += eb * d;\n            e[3] += ea * d;\n          }\n        }\n      }\n    }\n\n    yield {\n      pointContainer,\n      progress: 100,\n    };\n  }\n\n  private _fillErrorLine(errorLine: number[][], width: number) {\n    // shrink\n    if (errorLine.length > width) {\n      errorLine.length = width;\n    }\n\n    // reuse existing arrays\n    const l = errorLine.length;\n    for (let i = 0; i < l; i++) {\n      const error = errorLine[i];\n      error[0] = error[1] = error[2] = error[3] = 0;\n    }\n\n    // create missing arrays\n    for (let i = l; i < width; i++) {\n      errorLine[i] = [0.0, 0.0, 0.0, 0.0];\n    }\n  }\n\n  private _setKernel(kernel: ErrorDiffusionArrayKernel) {\n    switch (kernel) {\n      case ErrorDiffusionArrayKernel.FloydSteinberg:\n        this._kernel = [\n          [7 / 16, 1, 0],\n          [3 / 16, -1, 1],\n          [5 / 16, 0, 1],\n          [1 / 16, 1, 1],\n        ];\n        break;\n\n      case ErrorDiffusionArrayKernel.FalseFloydSteinberg:\n        this._kernel = [\n          [3 / 8, 1, 0],\n          [3 / 8, 0, 1],\n          [2 / 8, 1, 1],\n        ];\n        break;\n\n      case ErrorDiffusionArrayKernel.Stucki:\n        this._kernel = [\n          [8 / 42, 1, 0],\n          [4 / 42, 2, 0],\n          [2 / 42, -2, 1],\n          [4 / 42, -1, 1],\n          [8 / 42, 0, 1],\n          [4 / 42, 1, 1],\n          [2 / 42, 2, 1],\n          [1 / 42, -2, 2],\n          [2 / 42, -1, 2],\n          [4 / 42, 0, 2],\n          [2 / 42, 1, 2],\n          [1 / 42, 2, 2],\n        ];\n        break;\n\n      case ErrorDiffusionArrayKernel.Atkinson:\n        this._kernel = [\n          [1 / 8, 1, 0],\n          [1 / 8, 2, 0],\n          [1 / 8, -1, 1],\n          [1 / 8, 0, 1],\n          [1 / 8, 1, 1],\n          [1 / 8, 0, 2],\n        ];\n        break;\n\n      case ErrorDiffusionArrayKernel.Jarvis:\n        this._kernel = [\n          // Jarvis, Judice, and Ninke / JJN?\n          [7 / 48, 1, 0],\n          [5 / 48, 2, 0],\n          [3 / 48, -2, 1],\n          [5 / 48, -1, 1],\n          [7 / 48, 0, 1],\n          [5 / 48, 1, 1],\n          [3 / 48, 2, 1],\n          [1 / 48, -2, 2],\n          [3 / 48, -1, 2],\n          [5 / 48, 0, 2],\n          [3 / 48, 1, 2],\n          [1 / 48, 2, 2],\n        ];\n        break;\n\n      case ErrorDiffusionArrayKernel.Burkes:\n        this._kernel = [\n          [8 / 32, 1, 0],\n          [4 / 32, 2, 0],\n          [2 / 32, -2, 1],\n          [4 / 32, -1, 1],\n          [8 / 32, 0, 1],\n          [4 / 32, 1, 1],\n          [2 / 32, 2, 1],\n        ];\n        break;\n\n      case ErrorDiffusionArrayKernel.Sierra:\n        this._kernel = [\n          [5 / 32, 1, 0],\n          [3 / 32, 2, 0],\n          [2 / 32, -2, 1],\n          [4 / 32, -1, 1],\n          [5 / 32, 0, 1],\n          [4 / 32, 1, 1],\n          [2 / 32, 2, 1],\n          [2 / 32, -1, 2],\n          [3 / 32, 0, 2],\n          [2 / 32, 1, 2],\n        ];\n        break;\n\n      case ErrorDiffusionArrayKernel.TwoSierra:\n        this._kernel = [\n          [4 / 16, 1, 0],\n          [3 / 16, 2, 0],\n          [1 / 16, -2, 1],\n          [2 / 16, -1, 1],\n          [3 / 16, 0, 1],\n          [2 / 16, 1, 1],\n          [1 / 16, 2, 1],\n        ];\n        break;\n\n      case ErrorDiffusionArrayKernel.SierraLite:\n        this._kernel = [\n          [2 / 4, 1, 0],\n          [1 / 4, -1, 1],\n          [1 / 4, 0, 1],\n        ];\n        break;\n\n      default:\n        throw new Error(`ErrorDiffusionArray: unknown kernel = ${kernel}`);\n    }\n  }\n}\n", "import { ProgressTracker } from '../../utils/progressTracker';\nimport { ImageQuantizerYieldValue } from '../imageQuantizerYieldValue';\n\nenum Direction {\n  NONE = 0,\n  UP,\n  LEFT,\n  RIGHT,\n  DOWN,\n}\n\ninterface Data {\n  x: number;\n  y: number;\n  width: number;\n  height: number;\n  level: number;\n  index: number;\n  tracker: ProgressTracker;\n  callback(x: number, y: number): void;\n}\n\nexport function* hilbertCurve(\n  width: number,\n  height: number,\n  callback: (x: number, y: number) => void,\n) {\n  const maxBound = Math.max(width, height);\n  const level = Math.floor(Math.log(maxBound) / Math.log(2) + 1);\n  const tracker = new ProgressTracker(width * height, 99);\n  const data = {\n    width,\n    height,\n    level,\n    callback,\n    tracker,\n    index: 0,\n    x: 0,\n    y: 0,\n  };\n  yield* walkHilbert(data, Direction.UP);\n  visit(data, Direction.NONE);\n}\n\nfunction* walkHilbert(\n  data: Data,\n  direction: Direction,\n): IterableIterator<ImageQuantizerYieldValue> {\n  if (data.level < 1) return;\n\n  if (data.tracker.shouldNotify(data.index)) {\n    yield { progress: data.tracker.progress };\n  }\n  data.level--;\n  switch (direction) {\n    case Direction.LEFT:\n      yield* walkHilbert(data, Direction.UP);\n      visit(data, Direction.RIGHT);\n      yield* walkHilbert(data, Direction.LEFT);\n      visit(data, Direction.DOWN);\n      yield* walkHilbert(data, Direction.LEFT);\n      visit(data, Direction.LEFT);\n      yield* walkHilbert(data, Direction.DOWN);\n      break;\n\n    case Direction.RIGHT:\n      yield* walkHilbert(data, Direction.DOWN);\n      visit(data, Direction.LEFT);\n      yield* walkHilbert(data, Direction.RIGHT);\n      visit(data, Direction.UP);\n      yield* walkHilbert(data, Direction.RIGHT);\n      visit(data, Direction.RIGHT);\n      yield* walkHilbert(data, Direction.UP);\n      break;\n\n    case Direction.UP:\n      yield* walkHilbert(data, Direction.LEFT);\n      visit(data, Direction.DOWN);\n      yield* walkHilbert(data, Direction.UP);\n      visit(data, Direction.RIGHT);\n      yield* walkHilbert(data, Direction.UP);\n      visit(data, Direction.UP);\n      yield* walkHilbert(data, Direction.RIGHT);\n      break;\n\n    case Direction.DOWN:\n      yield* walkHilbert(data, Direction.RIGHT);\n      visit(data, Direction.UP);\n      yield* walkHilbert(data, Direction.DOWN);\n      visit(data, Direction.LEFT);\n      yield* walkHilbert(data, Direction.DOWN);\n      visit(data, Direction.DOWN);\n      yield* walkHilbert(data, Direction.LEFT);\n      break;\n\n    default:\n      break;\n  }\n  data.level++;\n}\n\nfunction visit(data: Data, direction: Direction) {\n  if (\n    data.x >= 0 &&\n    data.x < data.width &&\n    data.y >= 0 &&\n    data.y < data.height\n  ) {\n    data.callback(data.x, data.y);\n    data.index++;\n  }\n  switch (direction) {\n    case Direction.LEFT:\n      data.x--;\n      break;\n    case Direction.RIGHT:\n      data.x++;\n      break;\n    case Direction.UP:\n      data.y--;\n      break;\n    case Direction.DOWN:\n      data.y++;\n      break;\n  }\n}\n", "/**\n * @preserve\n * MIT License\n *\n * Copyright 2015-2018 <PERSON>\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to\n * deal in the Software without restriction, including without limitation the\n * rights to use, copy, modify, merge, publish, distribute, sublicense, and/or\n * sell copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL\n * THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS\n * IN THE SOFTWARE.\n *\n * riemersma.ts - part of Image Quantization Library\n */\nimport { AbstractImageQuantizer } from './imageQuantizer';\nimport { hilbertCurve } from './spaceFillingCurves/hilbertCurve';\nimport { AbstractDistanceCalculator } from '../distance/distanceCalculator';\nimport { PointContainer } from '../utils/pointContainer';\nimport { Palette } from '../utils/palette';\nimport { Point } from '../utils/point';\nimport { inRange0to255Rounded } from '../utils/arithmetic';\n\nexport class ErrorDiffusionRiemersma extends AbstractImageQuantizer {\n  private _distance: AbstractDistanceCalculator;\n  private _weights: number[];\n  private _errorQueueSize: number;\n\n  constructor(\n    colorDistanceCalculator: AbstractDistanceCalculator,\n    errorQueueSize = 16,\n    errorPropagation = 1,\n  ) {\n    super();\n    this._distance = colorDistanceCalculator;\n    this._errorQueueSize = errorQueueSize;\n    this._weights = ErrorDiffusionRiemersma._createWeights(\n      errorPropagation,\n      errorQueueSize,\n    );\n  }\n\n  /**\n   * Mutates pointContainer\n   */\n  *quantize(pointContainer: PointContainer, palette: Palette) {\n    const pointArray = pointContainer.getPointArray();\n    const width = pointContainer.getWidth();\n    const height = pointContainer.getHeight();\n    const errorQueue: Array<{\n      r: number;\n      g: number;\n      b: number;\n      a: number;\n    }> = [];\n\n    let head = 0;\n\n    for (let i = 0; i < this._errorQueueSize; i++) {\n      errorQueue[i] = { r: 0, g: 0, b: 0, a: 0 };\n    }\n\n    yield* hilbertCurve(width, height, (x, y) => {\n      const p = pointArray[x + y * width];\n      let { r, g, b, a } = p;\n      for (let i = 0; i < this._errorQueueSize; i++) {\n        const weight = this._weights[i];\n        const e = errorQueue[(i + head) % this._errorQueueSize];\n\n        r += e.r * weight;\n        g += e.g * weight;\n        b += e.b * weight;\n        a += e.a * weight;\n      }\n\n      const correctedPoint = Point.createByRGBA(\n        inRange0to255Rounded(r),\n        inRange0to255Rounded(g),\n        inRange0to255Rounded(b),\n        inRange0to255Rounded(a),\n      );\n\n      const quantizedPoint = palette.getNearestColor(\n        this._distance,\n        correctedPoint,\n      );\n\n      // update head and calculate tail\n      head = (head + 1) % this._errorQueueSize;\n      const tail = (head + this._errorQueueSize - 1) % this._errorQueueSize;\n\n      // update error with new value\n      errorQueue[tail].r = p.r - quantizedPoint.r;\n      errorQueue[tail].g = p.g - quantizedPoint.g;\n      errorQueue[tail].b = p.b - quantizedPoint.b;\n      errorQueue[tail].a = p.a - quantizedPoint.a;\n\n      // update point\n      p.from(quantizedPoint);\n    });\n\n    yield {\n      pointContainer,\n      progress: 100,\n    };\n  }\n\n  private static _createWeights(\n    errorPropagation: number,\n    errorQueueSize: number,\n  ) {\n    const weights = [];\n\n    const multiplier = Math.exp(\n      Math.log(errorQueueSize) / (errorQueueSize - 1),\n    );\n    for (let i = 0, next = 1; i < errorQueueSize; i++) {\n      weights[i] = (((next + 0.5) | 0) / errorQueueSize) * errorPropagation;\n      next *= multiplier;\n    }\n\n    return weights;\n  }\n}\n", "/**\n * @preserve\n * Copyright 2015-2018 <PERSON>\n * All rights reserved. (MIT Licensed)\n *\n * iq.ts - Image Quantization Library\n */\nexport { ssim } from './ssim';\n", "/**\n * @preserve\n * Copyright 2015-2018 <PERSON>\n * All rights reserved. (MIT Licensed)\n *\n * ssim.ts - part of Image Quantization Library\n */\nimport { PointContainer } from '../utils/pointContainer';\nimport { Y } from '../constants/bt709';\n\n// based on https://github.com/rhys-e/structural-similarity\n// http://en.wikipedia.org/wiki/Structural_similarity\nconst K1 = 0.01;\nconst K2 = 0.03;\n\nexport function ssim(image1: PointContainer, image2: PointContainer) {\n  if (\n    image1.getHeight() !== image2.getHeight() ||\n    image1.getWidth() !== image2.getWidth()\n  ) {\n    throw new Error('Images have different sizes!');\n  }\n\n  const bitsPerComponent = 8;\n  const L = (1 << bitsPerComponent) - 1;\n  const c1 = (K1 * L) ** 2;\n  const c2 = (K2 * L) ** 2;\n\n  let numWindows = 0;\n  let mssim = 0.0;\n\n  // calculate ssim for each window\n  iterate(\n    image1,\n    image2,\n    (lumaValues1, lumaValues2, averageLumaValue1, averageLumaValue2) => {\n      // calculate variance and covariance\n      let sigxy = 0.0;\n      let sigsqx = 0.0;\n      let sigsqy = 0.0;\n\n      for (let i = 0; i < lumaValues1.length; i++) {\n        sigsqx += (lumaValues1[i] - averageLumaValue1) ** 2;\n        sigsqy += (lumaValues2[i] - averageLumaValue2) ** 2;\n\n        sigxy +=\n          (lumaValues1[i] - averageLumaValue1) *\n          (lumaValues2[i] - averageLumaValue2);\n      }\n\n      const numPixelsInWin = lumaValues1.length - 1;\n      sigsqx /= numPixelsInWin;\n      sigsqy /= numPixelsInWin;\n      sigxy /= numPixelsInWin;\n\n      // perform ssim calculation on window\n      const numerator =\n        (2 * averageLumaValue1 * averageLumaValue2 + c1) * (2 * sigxy + c2);\n      const denominator =\n        (averageLumaValue1 ** 2 + averageLumaValue2 ** 2 + c1) *\n        (sigsqx + sigsqy + c2);\n      const ssim = numerator / denominator;\n\n      mssim += ssim;\n      numWindows++;\n    },\n  );\n  return mssim / numWindows;\n}\n\nfunction iterate(\n  image1: PointContainer,\n  image2: PointContainer,\n  callback: (\n    lumaValues1: number[],\n    lumaValues2: number[],\n    averageLumaValue1: number,\n    averageLumaValue2: number,\n  ) => void,\n) {\n  const windowSize = 8;\n  const width = image1.getWidth();\n  const height = image1.getHeight();\n\n  for (let y = 0; y < height; y += windowSize) {\n    for (let x = 0; x < width; x += windowSize) {\n      // avoid out-of-width/height\n      const windowWidth = Math.min(windowSize, width - x);\n      const windowHeight = Math.min(windowSize, height - y);\n\n      const lumaValues1 = calculateLumaValuesForWindow(\n        image1,\n        x,\n        y,\n        windowWidth,\n        windowHeight,\n      );\n      const lumaValues2 = calculateLumaValuesForWindow(\n        image2,\n        x,\n        y,\n        windowWidth,\n        windowHeight,\n      );\n      const averageLuma1 = calculateAverageLuma(lumaValues1);\n      const averageLuma2 = calculateAverageLuma(lumaValues2);\n\n      callback(lumaValues1, lumaValues2, averageLuma1, averageLuma2);\n    }\n  }\n}\n\nfunction calculateLumaValuesForWindow(\n  image: PointContainer,\n  x: number,\n  y: number,\n  width: number,\n  height: number,\n) {\n  const pointArray = image.getPointArray();\n  const lumaValues = [];\n\n  let counter = 0;\n\n  for (let j = y; j < y + height; j++) {\n    const offset = j * image.getWidth();\n    for (let i = x; i < x + width; i++) {\n      const point = pointArray[offset + i];\n      lumaValues[counter] =\n        point.r * Y.RED + point.g * Y.GREEN + point.b * Y.BLUE;\n      counter++;\n    }\n  }\n\n  return lumaValues;\n}\n\nfunction calculateAverageLuma(lumaValues: number[]) {\n  let sumLuma = 0.0;\n  for (const luma of lumaValues) {\n    sumLuma += luma;\n  }\n\n  return sumLuma / lumaValues.length;\n}\n", "/**\n * @preserve\n * Copyright 2015-2018 <PERSON>\n * All rights reserved. (MIT Licensed)\n *\n * helper.ts - part of Image Quantization Library\n */\nimport * as distance from './distance';\nimport * as image from './image';\nimport * as palette from './palette';\nimport { AbstractDistanceCalculator } from './distance/distanceCalculator';\nimport { PointContainer } from './utils/pointContainer';\nimport { Palette } from './utils/palette';\n\nconst setImmediateImpl =\n  typeof setImmediate === 'function'\n    ? setImmediate\n    : typeof process !== 'undefined' && typeof process?.nextTick === 'function'\n    ? (callback: () => void) => process.nextTick(callback)\n    : (callback: () => void) => setTimeout(callback, 0);\n\nexport type ColorDistanceFormula =\n  | 'cie94-textiles'\n  | 'cie94-graphic-arts'\n  | 'ciede2000'\n  | 'color-metric'\n  | 'euclidean'\n  | 'euclidean-bt709-noalpha'\n  | 'euclidean-bt709'\n  | 'man<PERSON>'\n  | 'manhattan-bt709'\n  | 'manhattan-nommyde'\n  | 'pngquant';\n\nexport type PaletteQuantization =\n  | 'neuquant'\n  | 'neuquant-float'\n  | 'rgbquant'\n  | 'wuquant';\n\nexport type ImageQuantization =\n  | 'nearest'\n  | 'riemersma'\n  | 'floyd-steinberg'\n  | 'false-floyd-steinberg'\n  | 'stucki'\n  | 'atkinson'\n  | 'jarvis'\n  | 'burkes'\n  | 'sierra'\n  | 'two-sierra'\n  | 'sierra-lite';\n\nexport interface ProgressOptions {\n  onProgress?: (progress: number) => void;\n}\n\nexport interface ApplyPaletteOptions {\n  colorDistanceFormula?: ColorDistanceFormula;\n  imageQuantization?: ImageQuantization;\n}\n\nexport interface BuildPaletteOptions {\n  colorDistanceFormula?: ColorDistanceFormula;\n  paletteQuantization?: PaletteQuantization;\n  colors?: number;\n}\n\nexport function buildPaletteSync(\n  images: PointContainer[],\n  {\n    colorDistanceFormula,\n    paletteQuantization,\n    colors,\n  }: BuildPaletteOptions = {},\n) {\n  const distanceCalculator =\n    colorDistanceFormulaToColorDistance(colorDistanceFormula);\n  const paletteQuantizer = paletteQuantizationToPaletteQuantizer(\n    distanceCalculator,\n    paletteQuantization,\n    colors,\n  );\n  images.forEach((image) => paletteQuantizer.sample(image));\n  return paletteQuantizer.quantizeSync();\n}\n\nexport async function buildPalette(\n  images: PointContainer[],\n  {\n    colorDistanceFormula,\n    paletteQuantization,\n    colors,\n    onProgress,\n  }: BuildPaletteOptions & ProgressOptions = {},\n) {\n  return new Promise<Palette>((resolve, reject) => {\n    const distanceCalculator =\n      colorDistanceFormulaToColorDistance(colorDistanceFormula);\n    const paletteQuantizer = paletteQuantizationToPaletteQuantizer(\n      distanceCalculator,\n      paletteQuantization,\n      colors,\n    );\n    images.forEach((image) => paletteQuantizer.sample(image));\n\n    let palette: Palette;\n    const iterator = paletteQuantizer.quantize();\n    const next = () => {\n      try {\n        const result = iterator.next();\n        if (result.done) {\n          resolve(palette);\n        } else {\n          if (result.value.palette) palette = result.value.palette;\n          if (onProgress) onProgress(result.value.progress);\n          setImmediateImpl(next);\n        }\n      } catch (error) {\n        reject(error);\n      }\n    };\n    setImmediateImpl(next);\n  });\n}\n\nexport function applyPaletteSync(\n  image: PointContainer,\n  palette: Palette,\n  { colorDistanceFormula, imageQuantization }: ApplyPaletteOptions = {},\n) {\n  const distanceCalculator =\n    colorDistanceFormulaToColorDistance(colorDistanceFormula);\n  const imageQuantizer = imageQuantizationToImageQuantizer(\n    distanceCalculator,\n    imageQuantization,\n  );\n  return imageQuantizer.quantizeSync(image, palette);\n}\n\nexport async function applyPalette(\n  image: PointContainer,\n  palette: Palette,\n  {\n    colorDistanceFormula,\n    imageQuantization,\n    onProgress,\n  }: ApplyPaletteOptions & ProgressOptions = {},\n) {\n  return new Promise<PointContainer>((resolve, reject) => {\n    const distanceCalculator =\n      colorDistanceFormulaToColorDistance(colorDistanceFormula);\n    const imageQuantizer = imageQuantizationToImageQuantizer(\n      distanceCalculator,\n      imageQuantization,\n    );\n\n    let outPointContainer: PointContainer;\n    const iterator = imageQuantizer.quantize(image, palette);\n    const next = () => {\n      try {\n        const result = iterator.next();\n        if (result.done) {\n          resolve(outPointContainer);\n        } else {\n          if (result.value.pointContainer) {\n            outPointContainer = result.value.pointContainer;\n          }\n          if (onProgress) onProgress(result.value.progress);\n          setImmediateImpl(next);\n        }\n      } catch (error) {\n        reject(error);\n      }\n    };\n    setImmediateImpl(next);\n  });\n}\n\nfunction colorDistanceFormulaToColorDistance(\n  colorDistanceFormula: ColorDistanceFormula = 'euclidean-bt709',\n) {\n  switch (colorDistanceFormula) {\n    case 'cie94-graphic-arts':\n      return new distance.CIE94GraphicArts();\n    case 'cie94-textiles':\n      return new distance.CIE94Textiles();\n    case 'ciede2000':\n      return new distance.CIEDE2000();\n    case 'color-metric':\n      return new distance.CMetric();\n    case 'euclidean':\n      return new distance.Euclidean();\n    case 'euclidean-bt709':\n      return new distance.EuclideanBT709();\n    case 'euclidean-bt709-noalpha':\n      return new distance.EuclideanBT709NoAlpha();\n    case 'manhattan':\n      return new distance.Manhattan();\n    case 'manhattan-bt709':\n      return new distance.ManhattanBT709();\n    case 'manhattan-nommyde':\n      return new distance.ManhattanNommyde();\n    case 'pngquant':\n      return new distance.PNGQuant();\n    default:\n      throw new Error(`Unknown colorDistanceFormula ${colorDistanceFormula}`);\n  }\n}\n\nfunction imageQuantizationToImageQuantizer(\n  distanceCalculator: AbstractDistanceCalculator,\n  imageQuantization: ImageQuantization = 'floyd-steinberg',\n) {\n  switch (imageQuantization) {\n    case 'nearest':\n      return new image.NearestColor(distanceCalculator);\n    case 'riemersma':\n      return new image.ErrorDiffusionRiemersma(distanceCalculator);\n    case 'floyd-steinberg':\n      return new image.ErrorDiffusionArray(\n        distanceCalculator,\n        image.ErrorDiffusionArrayKernel.FloydSteinberg,\n      );\n    case 'false-floyd-steinberg':\n      return new image.ErrorDiffusionArray(\n        distanceCalculator,\n        image.ErrorDiffusionArrayKernel.FalseFloydSteinberg,\n      );\n    case 'stucki':\n      return new image.ErrorDiffusionArray(\n        distanceCalculator,\n        image.ErrorDiffusionArrayKernel.Stucki,\n      );\n    case 'atkinson':\n      return new image.ErrorDiffusionArray(\n        distanceCalculator,\n        image.ErrorDiffusionArrayKernel.Atkinson,\n      );\n    case 'jarvis':\n      return new image.ErrorDiffusionArray(\n        distanceCalculator,\n        image.ErrorDiffusionArrayKernel.Jarvis,\n      );\n    case 'burkes':\n      return new image.ErrorDiffusionArray(\n        distanceCalculator,\n        image.ErrorDiffusionArrayKernel.Burkes,\n      );\n    case 'sierra':\n      return new image.ErrorDiffusionArray(\n        distanceCalculator,\n        image.ErrorDiffusionArrayKernel.Sierra,\n      );\n    case 'two-sierra':\n      return new image.ErrorDiffusionArray(\n        distanceCalculator,\n        image.ErrorDiffusionArrayKernel.TwoSierra,\n      );\n    case 'sierra-lite':\n      return new image.ErrorDiffusionArray(\n        distanceCalculator,\n        image.ErrorDiffusionArrayKernel.SierraLite,\n      );\n    default:\n      throw new Error(`Unknown imageQuantization ${imageQuantization}`);\n  }\n}\n\nfunction paletteQuantizationToPaletteQuantizer(\n  distanceCalculator: AbstractDistanceCalculator,\n  paletteQuantization: PaletteQuantization = 'wuquant',\n  colors = 256,\n) {\n  switch (paletteQuantization) {\n    case 'neuquant':\n      return new palette.NeuQuant(distanceCalculator, colors);\n    case 'rgbquant':\n      return new palette.RGBQuant(distanceCalculator, colors);\n    case 'wuquant':\n      return new palette.WuQuant(distanceCalculator, colors);\n    case 'neuquant-float':\n      return new palette.NeuQuantFloat(distanceCalculator, colors);\n    default:\n      throw new Error(`Unknown paletteQuantization ${paletteQuantization}`);\n  }\n}\n", "/**\n * @preserve\n * Copyright 2015-2018 <PERSON>\n * All rights reserved. (MIT Licensed)\n *\n * iq.ts - Image Quantization Library\n */\nimport * as constants from './constants';\nimport * as conversion from './conversion';\nimport * as distance from './distance';\nimport * as palette from './palette';\nimport * as image from './image';\nimport * as quality from './quality';\nimport * as utils from './utils';\n\nexport {\n  buildPalette,\n  buildPaletteSync,\n  applyPalette,\n  applyPaletteSync,\n} from './basicAPI';\n\nexport type {\n  ImageQuantization,\n  PaletteQuantization,\n  ColorDistanceFormula,\n} from './basicAPI';\n\nexport { constants, conversion, distance, palette, image, quality, utils };\n"], "mappings": ";;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYO,IAAK,IAAL,kBAAK,OAAL;AACL,iBAAM,UAAN;AACA,mBAAQ,UAAR;AACA,kBAAO,UAAP;AACA,mBAAQ,KAAR;AAJU;AAAA;AAOL,IAAK,IAAL,kBAAK,OAAL;AACL,iBAAM,QAAN;AACA,mBAAQ,OAAR;AACA,kBAAO,QAAP;AACA,mBAAQ,UAAR;AAJU;AAAA;AAOL,IAAK,IAAL,kBAAK,OAAL;AACL,iBAAM,QAAN;AACA,mBAAQ,OAAR;AACA,kBAAO,QAAP;AACA,mBAAQ,SAAR;AAJU;AAAA;AD1BZ;;AEAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,AAOA,sBAAsB,GAAW;AAC/B,SAAO,IAAI,UAAY,MAAI,SAAS,UAAU,MAAM,IAAI;AAAA;AAGnD,iBAAiB,GAAW,GAAW,GAAW;AAEvD,MAAI,aAAa,IAAI;AACrB,MAAI,aAAa,IAAI;AACrB,MAAI,aAAa,IAAI;AAGrB,SAAO;AAAA,IACL,GAAG,IAAI,SAAS,IAAI,SAAS,IAAI;AAAA,IACjC,GAAG,IAAI,SAAS,IAAI,SAAS,IAAI;AAAA,IACjC,GAAG,IAAI,SAAS,IAAI,SAAS,IAAI;AAAA;AAAA;;;ACrBrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAO,yBAAyB,GAAW;AACzC,SAAO,IAAK,MAAK,KAAK;AAAA;AAGjB,cAAc,GAAW,GAAW,GAAW;AACpD,MAAI,IAAI;AACR,MAAI,IAAI;AAAG,QAAI;AACf,MAAI,IAAI;AAAG,QAAI;AACf,SAAO;AAAA;AAGF,cAAc,GAAW,GAAW,GAAW;AACpD,MAAI,IAAI;AACR,MAAI,IAAI;AAAG,QAAI;AACf,MAAI,IAAI;AAAG,QAAI;AACf,SAAO;AAAA;AAGF,oBAAoB,OAAe,KAAa,MAAc;AACnE,MAAI,QAAQ;AAAM,YAAQ;AAC1B,MAAI,QAAQ;AAAK,YAAQ;AACzB,SAAO,QAAQ;AAAA;AAGV,8BAA8B,GAAW;AAC9C,MAAI,KAAK,MAAM;AACf,MAAI,IAAI;AAAK,QAAI;AAAA,WACR,IAAI;AAAG,QAAI;AACpB,SAAO;AAAA;AAGF,uBAAuB,GAAW;AACvC,MAAI,IAAI;AAAK,QAAI;AAAA,WACR,IAAI;AAAG,QAAI;AACpB,SAAO;AAAA;AAGF,oBACL,aACA,UACA;AACA,QAAM,OAAO,OAAO,YAAY;AAChC,MAAI;AAEJ,MAAI,SAAS,YAAY,SAAS,UAAU;AAC1C,UAAM,MAAM,uBAAO,OAAO;AAC1B,aAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,IAAI,GAAG,KAAK;AAClD,YAAM,MAAM,YAAY;AACxB,UAAI,IAAI,QAAQ,IAAI,SAAS;AAAG;AAChC,UAAI,OAAO;AAAA;AAGb,aAAS,YAAY,KAAK,CAAC,GAAG,MAAM,SAAS,GAAG,MAAM,IAAI,KAAK,IAAI;AAAA,SAC9D;AACL,UAAM,OAAO,YAAY,MAAM;AAC/B,aAAS,YAAY,KACnB,CAAC,GAAG,MAAM,SAAS,GAAG,MAAM,KAAK,QAAQ,KAAK,KAAK,QAAQ;AAAA;AAI/D,SAAO;AAAA;;;AC5DT,AAgBO,iBAAiB,GAAW,GAAW,GAAW;AACvD,QAAM,MAAM,KAAK,GAAG,GAAG;AACvB,QAAM,MAAM,KAAK,GAAG,GAAG;AACvB,QAAM,QAAQ,MAAM;AACpB,QAAM,IAAK,OAAM,OAAO;AAExB,MAAI,IAAI;AACR,MAAI,IAAI,KAAK,IAAI;AAAG,QAAI,QAAS,KAAI,MAAM,MAAM,MAAM,MAAM,MAAM;AAEnE,MAAI,IAAI;AACR,MAAI,QAAQ,GAAG;AACb,QAAI,QAAQ,GAAG;AACb,UAAK,KAAI,KAAK;AAAA,eACL,QAAQ,GAAG;AACpB,UAAI,IAAK,KAAI,KAAK;AAAA,WACb;AACL,UAAI,IAAK,KAAI,KAAK;AAAA;AAGpB,SAAK;AACL,QAAI,IAAI;AAAG,WAAK;AAAA;AAElB,SAAO,EAAE,GAAG,GAAG;AAAA;;;ACtCjB,AAOA,IAAM,OAAO;AACb,IAAM,OAAO;AACb,IAAM,OAAO;AAEb,eAAe,GAAW;AACxB,SAAO,IAAI,UAAW,KAAM,KAAI,KAAK,QAAQ,IAAI,KAAK;AAAA;AAGjD,iBAAiB,IAAW,IAAW,GAAW;AACvD,OAAI,MAAM,KAAI;AACd,OAAI,MAAM,KAAI;AACd,MAAI,MAAM,IAAI;AAEd,MAAI,MAAM,KAAI,KAAK;AAAG,UAAM,IAAI,MAAM;AACtC,SAAO;AAAA,IACL,GAAG,KAAK,IAAI,GAAG,MAAM,KAAI;AAAA,IACzB,GAAG,MAAO,MAAI;AAAA,IACd,GAAG,MAAO,MAAI;AAAA;AAAA;;;ACxBlB,AAUO,iBAAiB,GAAW,GAAW,GAAW;AACvD,QAAM,MAAM,QAAQ,GAAG,GAAG;AAC1B,SAAO,QAAQ,IAAI,GAAG,IAAI,GAAG,IAAI;AAAA;;;ACZnC,AAOA,IAAM,QAAO;AACb,IAAM,QAAO;AACb,IAAM,QAAO;AAEb,gBAAe,GAAW;AACxB,SAAO,IAAI,cAAc,KAAK,IAAK,KAAI,KAAK,OAAO;AAAA;AAG9C,iBAAiB,GAAW,GAAW,GAAW;AACvD,QAAM,KAAK,KAAI,MAAM;AACrB,QAAM,KAAI,IAAI,MAAM;AACpB,QAAM,IAAI,KAAI,IAAI;AAElB,SAAO;AAAA,IACL,GAAG,QAAO,OAAM;AAAA,IAChB,GAAG,QAAO,OAAM;AAAA,IAChB,GAAG,QAAO,OAAM;AAAA;AAAA;;;ACvBpB,AAUA,uBAAsB,GAAW;AAC/B,SAAO,IAAI,WAAY,QAAQ,KAAM,KAAI,OAAO,QAAQ,QAAQ;AAAA;AAG3D,iBAAiB,IAAW,IAAW,GAAW;AAEvD,QAAM,IAAI,cAAa,KAAI,SAAS,KAAI,UAAU,IAAI;AACtD,QAAM,IAAI,cAAa,KAAI,UAAU,KAAI,SAAS,IAAI;AACtD,QAAM,IAAI,cAAa,KAAI,SAAS,KAAI,SAAS,IAAI;AAErD,SAAO;AAAA,IACL,GAAG,qBAAqB,IAAI;AAAA,IAC5B,GAAG,qBAAqB,IAAI;AAAA,IAC5B,GAAG,qBAAqB,IAAI;AAAA;AAAA;;;ACvBhC,AAUO,iBAAiB,GAAW,GAAW,GAAW;AACvD,QAAM,MAAM,QAAQ,GAAG,GAAG;AAC1B,SAAO,QAAQ,IAAI,GAAG,IAAI,GAAG,IAAI;AAAA;ARZnC;;ASAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,AASO,uCAA0C;AAAA,EAS/C,cAAc;AARJ;AACA;AAQR,SAAK;AAGL,SAAK,cAAc,KAAK,KAAK,KAAK;AAAA;AAAA,EAGpC,cAAc,GAAW,GAAW,GAAW,GAAW;AACxD,SAAK,cAAc;AAAA,MACjB,GAAG,IAAI,IAAI,MAAM,IAAI;AAAA,MACrB,GAAG,IAAI,IAAI,MAAM,IAAI;AAAA,MACrB,GAAG,IAAI,IAAI,MAAM,IAAI;AAAA,MACrB,GAAG,IAAI,IAAI,MAAM,IAAI;AAAA;AAEvB,SAAK,eAAe,KAAK,aAAa,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAAA;AAAA,EAG7D,oBAAoB,QAAmB,QAAmB;AACxD,WACE,KAAK,aACH,OAAO,GACP,OAAO,GACP,OAAO,GACP,OAAO,GACP,OAAO,GACP,OAAO,GACP,OAAO,GACP,OAAO,KACL,KAAK;AAAA;AAAA;;;AC9Cf,AAeO,kCAAqC,2BAA2B;AAAA,EAWrE,aACE,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA;AACA,UAAM,OAAO,QACX,cAAc,KAAK,KAAK,YAAY,IACpC,cAAc,KAAK,KAAK,YAAY,IACpC,cAAc,KAAK,KAAK,YAAY;AAEtC,UAAM,OAAO,QACX,cAAc,KAAK,KAAK,YAAY,IACpC,cAAc,KAAK,KAAK,YAAY,IACpC,cAAc,KAAK,KAAK,YAAY;AAGtC,UAAM,KAAK,KAAK,IAAI,KAAK;AACzB,UAAM,KAAK,KAAK,IAAI,KAAK;AACzB,UAAM,KAAK,KAAK,IAAI,KAAK;AACzB,UAAM,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK;AACrD,UAAM,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK;AACrD,UAAM,KAAK,KAAK;AAEhB,QAAI,SAAS,KAAK,KAAK,KAAK,KAAK,KAAK;AACtC,aAAS,SAAS,IAAI,IAAI,KAAK,KAAK;AAEpC,UAAM,SAAU,MAAK,MAAM,KAAK,YAAY,IAAI,KAAK;AAGrD,WAAO,KAAK,KACT,MAAK,KAAK,QAAQ,IAChB,MAAM,KAAM,KAAK,MAAM,QAAQ,IAC/B,UAAU,KAAM,KAAK,MAAM,QAAQ,IACpC,UAAU;AAAA;AAAA;AAKX,kCAA4B,cAAc;AAAA,EACrC,eAAe;AACvB,SAAK,MAAM;AACX,SAAK,MAAM;AACX,SAAK,MAAM;AACX,SAAK,MAAO,OAAO,KAAM;AAAA;AAAA;AAItB,qCAA+B,cAAc;AAAA,EACxC,eAAe;AACvB,SAAK,MAAM;AACX,SAAK,MAAM;AACX,SAAK,MAAM;AACX,SAAK,MAAO,OAAO,MAAO;AAAA;AAAA;;;ACnF9B,AAeO,+BAAwB,2BAA2B;AAAA,EAgB9C,eAAe;AAAA;AAAA,SAEV,aAAa,GAAW,IAAY;AACjD,UAAM,KAAK,KAAK,MAAM,GAAG;AACzB,QAAI,MAAM;AAAG,aAAO;AACpB,WAAO,KAAK,WAAU;AAAA;AAAA,SAGT,aAAa,KAAa,KAAa;AACpD,UAAM,WAAW,OAAO;AACxB,UAAM,MAAM,IAAM,KAAK,KAAK,WAAY,YAAW,WAAU;AAC7D,UAAM,cACJ,WAAU,cACV,KAAK,IACH,CAAI,SAAM,WAAU,gBAAgB,WAAU,gBAAgB;AAElE,WAAO,CAAC,KAAK,IAAI,IAAM,eAAe;AAAA;AAAA,SAGzB,YAAY,KAAa;AACtC,WACE,IACA,OAAO,KAAK,IAAI,MAAM,WAAU,eAChC,OAAO,KAAK,IAAI,MAAM,KACtB,OAAO,KAAK,IAAI,MAAM,IAAM,WAAU,cACtC,MAAM,KAAK,IAAI,MAAM,IAAM,WAAU;AAAA;AAAA,SAI1B,eACb,QACA,OACA,KACA,KACA;AACA,UAAM,QAAQ,MAAM;AACpB,QAAI,WAAW;AAAG,aAAO;AACzB,QAAI,SAAS,WAAU;AAAc,aAAO,QAAQ;AACpD,QAAI,QAAQ,WAAU,cAAc;AAClC,aAAQ,SAAQ,WAAU,gBAAgB;AAAA;AAE5C,WAAQ,SAAQ,WAAU,gBAAgB;AAAA;AAAA,SAG7B,eACb,QACA,OACA,KACA,KACA;AACA,QAAI;AACJ,QAAI,WAAW,GAAG;AAChB,YAAM;AAAA,eACG,SAAS,WAAU,cAAc;AAC1C,YAAM,MAAM;AAAA,eACH,OAAO,KAAK;AACrB,YAAM,MAAM,MAAM,WAAU;AAAA,WACvB;AACL,YAAM,MAAM,MAAM,WAAU;AAAA;AAE9B,WAAO,IAAM,KAAK,KAAK,UAAU,KAAK,IAAI,MAAM;AAAA;AAAA,EAGlD,aACE,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA;AACA,UAAM,OAAO,QACX,cAAc,KAAK,KAAK,YAAY,IACpC,cAAc,KAAK,KAAK,YAAY,IACpC,cAAc,KAAK,KAAK,YAAY;AAEtC,UAAM,OAAO,QACX,cAAc,KAAK,KAAK,YAAY,IACpC,cAAc,KAAK,KAAK,YAAY,IACpC,cAAc,KAAK,KAAK,YAAY;AAEtC,UAAM,KAAM,MAAK,MAAM,KAAK,YAAY,IAAI,WAAU;AACtD,UAAM,MAAM,KAAK,kBAAkB,MAAM;AAEzC,WAAO,KAAK,KAAK,MAAM,KAAK;AAAA;AAAA,EAG9B,kBACE,MACA,MACA;AAEA,UAAM,KAAK,KAAK;AAChB,UAAM,KAAK,KAAK;AAChB,UAAM,KAAK,KAAK;AAGhB,UAAM,KAAK,KAAK;AAChB,UAAM,KAAK,KAAK;AAChB,UAAM,KAAK,KAAK;AAGhB,UAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AACpC,UAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AACpC,UAAM,mBAAqB,OAAK,MAAM,MAAQ;AAE9C,UAAM,IACJ,MACC,KACC,KAAK,KAAK,mBAAoB,oBAAmB,WAAU;AAC/D,UAAM,MAAO,KAAM,KAAK;AACxB,UAAM,MAAO,KAAM,KAAK;AAExB,UAAM,MAAM,KAAK,KAAK,MAAM,MAAM,KAAK;AACvC,UAAM,MAAM,KAAK,KAAK,MAAM,MAAM,KAAK;AACvC,UAAM,SAAS,MAAM;AAGrB,UAAM,MAAM,WAAU,aAAa,IAAI;AACvC,UAAM,MAAM,WAAU,aAAa,IAAI;AACvC,UAAM,QAAQ,KAAK,IAAI,MAAM;AAC7B,UAAM,MAAM,KAAK;AACjB,UAAM,MAAM,MAAM;AAClB,UAAM,MAAM,WAAU,eAAe,QAAQ,OAAO,KAAK;AACzD,UAAM,MAAM,WAAU,eAAe,QAAQ,OAAO,KAAK;AAEzD,UAAM,IAAI,WAAU,YAAY;AAEhC,UAAM,MAAO,OAAM,OAAO;AAC1B,UAAM,sBAAwB,OAAK,MAAM,IAAM,OAAS;AACxD,UAAM,MACJ,IACC,QAAQ,sBAAuB,KAAK,KAAK,KAAO;AACnD,UAAM,MAAM,IAAM,QAAQ;AAC1B,UAAM,MAAM,IAAM,QAAQ,IAAI;AAE9B,UAAM,MAAM,WAAU,aAAa,KAAK;AAExC,UAAM,QAAQ,MAAM;AACpB,UAAM,QAAQ,MAAM;AACpB,UAAM,QAAQ,MAAM;AAEpB,WAAO,SAAS,IAAI,SAAS,IAAI,SAAS,IAAI,MAAM,QAAQ;AAAA;AAAA;AAhKzD;AAMmB,cANnB,WAMmB,OAAO,OAAO,MAAO;AACrB,cAPnB,WAOmB,aAAY,MAAM;AAClB,cARnB,WAQmB,gBAAe,gBAAgB;AAC/B,cATnB,WASmB,gBAAe,gBAAgB;AAC/B,cAVnB,WAUmB,eAAc,gBAAgB;AAC9B,cAXnB,WAWmB,cAAa,gBAAgB;AAC7B,cAZnB,WAYmB,eAAc,gBAAgB;AAC9B,cAbnB,WAamB,gBAAe,gBAAgB;AAC/B,cAdnB,WAcmB,eAAc,gBAAgB;;;AC7BxD,AAYO,4BAAsB,2BAA2B;AAAA,EACtD,aACE,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA;AACA,UAAM,QAAU,MAAK,MAAM,IAAK,KAAK,YAAY;AACjD,UAAM,IAAK,MAAK,MAAM,KAAK,YAAY;AACvC,UAAM,IAAK,MAAK,MAAM,KAAK,YAAY;AACvC,UAAM,IAAK,MAAK,MAAM,KAAK,YAAY;AACvC,UAAM,KACD,QAAM,SAAS,IAAI,KAAM,KAC5B,IAAI,IAAI,IACL,QAAM,SAAS,IAAI,KAAM;AAC9B,UAAM,KAAM,MAAK,MAAM,KAAK,YAAY;AAExC,WAAO,KAAK,KAAK,KAAK,KAAK;AAAA;AAAA,EAGnB,eAAe;AAAA;AAAA;;;ACpC3B,AAaO,sCAAyC,2BAA2B;AAAA,EAMzE,aACE,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA;AACA,UAAM,KAAK,KAAK;AAChB,UAAM,KAAK,KAAK;AAChB,UAAM,KAAK,KAAK;AAChB,UAAM,KAAK,KAAK;AAChB,WAAO,KAAK,KACV,KAAK,MAAM,KAAK,KACd,KAAK,MAAM,KAAK,KAChB,KAAK,MAAM,KAAK,KAChB,KAAK,MAAM,KAAK;AAAA;AAAA;AAKjB,8BAAwB,kBAAkB;AAAA,EACrC,eAAe;AACvB,SAAK,MAAM;AACX,SAAK,MAAM;AACX,SAAK,MAAM;AACX,SAAK,MAAM;AAAA;AAAA;AAOR,mCAA6B,kBAAkB;AAAA,EAC1C,eAAe;AACvB,SAAK,MAAM;AACX,SAAK,MAAM;AACX,SAAK,MAAM;AAEX,SAAK,MAAM;AAAA;AAAA;AAOR,0CAAoC,kBAAkB;AAAA,EACjD,eAAe;AACvB,SAAK,MAAM;AACX,SAAK,MAAM;AACX,SAAK,MAAM;AACX,SAAK,MAAM;AAAA;AAAA;;;ACxEf,AAaO,sCAAyC,2BAA2B;AAAA,EAMzE,aACE,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA;AACA,QAAI,KAAK,KAAK;AACd,QAAI,KAAK,KAAK;AACd,QAAI,KAAK,KAAK;AACd,QAAI,KAAK,KAAK;AACd,QAAI,KAAK;AAAG,WAAK,IAAI;AACrB,QAAI,KAAK;AAAG,WAAK,IAAI;AACrB,QAAI,KAAK;AAAG,WAAK,IAAI;AACrB,QAAI,KAAK;AAAG,WAAK,IAAI;AAErB,WAAO,KAAK,MAAM,KAAK,KAAK,MAAM,KAAK,KAAK,MAAM,KAAK,KAAK,MAAM;AAAA;AAAA;AAI/D,8BAAwB,kBAAkB;AAAA,EACrC,eAAe;AACvB,SAAK,MAAM;AACX,SAAK,MAAM;AACX,SAAK,MAAM;AACX,SAAK,MAAM;AAAA;AAAA;AAQR,qCAA+B,kBAAkB;AAAA,EAC5C,eAAe;AACvB,SAAK,MAAM;AACX,SAAK,MAAM;AACX,SAAK,MAAM;AAEX,SAAK,MAAM;AAAA;AAAA;AAOR,mCAA6B,kBAAkB;AAAA,EAC1C,eAAe;AACvB,SAAK,MAAM;AACX,SAAK,MAAM;AACX,SAAK,MAAM;AAEX,SAAK,MAAM;AAAA;AAAA;;;AC1Ef,AAgBO,6BAAuB,2BAA2B;AAAA,EAiBvD,aACE,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA;AACA,UAAM,SAAU,MAAK,MAAM,KAAK,YAAY;AAC5C,WACE,KAAK,mBACH,KAAK,KAAK,YAAY,GACtB,KAAK,KAAK,YAAY,GACtB,UAEF,KAAK,mBACH,KAAK,KAAK,YAAY,GACtB,KAAK,KAAK,YAAY,GACtB,UAEF,KAAK,mBACH,KAAK,KAAK,YAAY,GACtB,KAAK,KAAK,YAAY,GACtB;AAAA;AAAA,EAKE,mBAAmB,IAAW,IAAW,QAAgB;AAG/D,UAAM,QAAQ,KAAI;AAClB,UAAM,QAAQ,QAAQ;AAEtB,WAAO,QAAQ,QAAQ,QAAQ;AAAA;AAAA,EAGvB,eAAe;AAAA;AAAA;APxE3B;;AQAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,AAUO,qCAAwC;AAAA,EAI7C,eAAe;AACb,eAAW,SAAS,KAAK,YAAY;AACnC,UAAI,MAAM,SAAS;AACjB,eAAO,MAAM;AAAA;AAAA;AAIjB,UAAM,IAAI,MAAM;AAAA;AAAA;;;ACrBpB,AAqBO,kBAAiC;AAAA,EA6CtC,cAAc;AA5Cd;AACA;AACA;AACA;AACA;AACA;AAwCE,SAAK,SAAS,OAAO;AACrB,SAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI;AACpC,SAAK,OAAO,IAAI,MAAM;AACtB,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AAAA;AAAA,SA3CV,mBAAmB,YAAsB;AAC9C,UAAM,QAAQ,IAAI;AAElB,UAAM,IAAI,WAAW,KAAK;AAC1B,UAAM,IAAI,WAAW,KAAK;AAC1B,UAAM,IAAI,WAAW,KAAK;AAC1B,UAAM,IAAI,WAAW,KAAK;AAC1B,UAAM;AACN,UAAM;AAEN,WAAO;AAAA;AAAA,SAGF,aAAa,KAAa,OAAe,MAAc,OAAe;AAC3E,UAAM,QAAQ,IAAI;AAElB,UAAM,IAAI,MAAM;AAChB,UAAM,IAAI,QAAQ;AAClB,UAAM,IAAI,OAAO;AACjB,UAAM,IAAI,QAAQ;AAClB,UAAM;AACN,UAAM;AAEN,WAAO;AAAA;AAAA,SAGF,eAAe,QAAgB;AACpC,UAAM,QAAQ,IAAI;AAElB,UAAM,SAAS,WAAW;AAC1B,UAAM;AACN,UAAM;AAEN,WAAO;AAAA;AAAA,EAoBT,KAAK,OAAc;AACjB,SAAK,IAAI,MAAM;AACf,SAAK,IAAI,MAAM;AACf,SAAK,IAAI,MAAM;AACf,SAAK,IAAI,MAAM;AACf,SAAK,SAAS,MAAM;AACpB,SAAK,KAAK,KAAK,MAAM;AACrB,SAAK,KAAK,KAAK,MAAM;AACrB,SAAK,KAAK,KAAK,MAAM;AACrB,SAAK,KAAK,KAAK,MAAM;AAAA;AAAA,EAiBvB,cAAc,iBAA0B;AACtC,QAAI,IAAI,KAAK;AACb,QAAI,IAAI,KAAK;AACb,QAAI,IAAI,KAAK;AAEb,QAAI,iBAAiB;AACnB,UAAI,KAAK,IAAI,KAAK,MAAM,KAAK,IAAK,KAAK,IAAI,IAAK;AAChD,UAAI,KAAK,IAAI,KAAK,MAAM,KAAK,IAAK,KAAK,IAAI,IAAK;AAChD,UAAI,KAAK,IAAI,KAAK,MAAM,KAAK,IAAK,KAAK,IAAI,IAAK;AAAA;AAWlD,WAAO,IAAI,mBAAQ,IAAI,qBAAU,IAAI;AAAA;AAAA,EAG/B,cAAc;AACpB,SAAK,SACD,MAAK,KAAK,KAAO,KAAK,KAAK,KAAO,KAAK,KAAK,IAAK,KAAK,OAAO;AAAA;AAAA,EAG3D,YAAY;AAClB,SAAK,IAAI,KAAK,SAAS;AACvB,SAAK,IAAK,KAAK,WAAW,IAAK;AAC/B,SAAK,IAAK,KAAK,WAAW,KAAM;AAChC,SAAK,IAAK,KAAK,WAAW,KAAM;AAAA;AAAA,EAG1B,kBAAkB;AACxB,SAAK,KAAK,KAAK,KAAK;AACpB,SAAK,KAAK,KAAK,KAAK;AACpB,SAAK,KAAK,KAAK,KAAK;AACpB,SAAK,KAAK,KAAK,KAAK;AAAA;AAAA;;;ACnJxB,AAaO,2BAAqB;AAAA,EAK1B,cAAc;AAJG;AACT;AACA;AAGN,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,cAAc;AAAA;AAAA,EAGrB,WAAW;AACT,WAAO,KAAK;AAAA;AAAA,EAGd,YAAY;AACV,WAAO,KAAK;AAAA;AAAA,EAGd,SAAS,OAAe;AACtB,SAAK,SAAS;AAAA;AAAA,EAGhB,UAAU,QAAgB;AACxB,SAAK,UAAU;AAAA;AAAA,EAGjB,gBAAgB;AACd,WAAO,KAAK;AAAA;AAAA,EAGd,QAAQ;AACN,UAAM,QAAQ,IAAI;AAClB,UAAM,SAAS,KAAK;AACpB,UAAM,UAAU,KAAK;AAErB,aAAS,IAAI,GAAG,IAAI,KAAK,YAAY,QAAQ,IAAI,GAAG,KAAK;AACvD,YAAM,YAAY,KAAK,MAAM,eAC3B,KAAK,YAAY,GAAG,SAAS;AAAA;AAIjC,WAAO;AAAA;AAAA,EAGT,gBAAgB;AACd,UAAM,IAAI,KAAK,YAAY;AAC3B,UAAM,cAAc,IAAI,YAAY;AAEpC,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,kBAAY,KAAK,KAAK,YAAY,GAAG;AAAA;AAGvC,WAAO;AAAA;AAAA,EAGT,eAAe;AACb,WAAO,IAAI,WAAW,KAAK,gBAAgB;AAAA;AAAA,SAGtC,qBAAqB,KAAuB;AACjD,UAAM,QAAQ,IAAI;AAClB,UAAM,SAAS,IAAI;AAEnB,UAAM,SAAS,SAAS,cAAc;AACtC,WAAO,QAAQ;AACf,WAAO,SAAS;AAEhB,UAAM,MAAM,OAAO,WAAW;AAC9B,QAAI,UAAU,KAAK,GAAG,GAAG,OAAO,QAAQ,GAAG,GAAG,OAAO;AAErD,WAAO,eAAe,sBAAsB;AAAA;AAAA,SAGvC,sBAAsB,QAA2B;AACtD,UAAM,QAAQ,OAAO;AACrB,UAAM,SAAS,OAAO;AAEtB,UAAM,MAAM,OAAO,WAAW;AAC9B,UAAM,UAAU,IAAI,aAAa,GAAG,GAAG,OAAO;AAE9C,WAAO,eAAe,cAAc;AAAA;AAAA,SAG/B,cAAc,WAAsB;AACzC,UAAM,QAAQ,UAAU;AACxB,UAAM,SAAS,UAAU;AAEzB,WAAO,eAAe,eAAe,UAAU,MAAM,OAAO;AAAA;AAAA,SAGvD,eACL,YACA,OACA,QACA;AACA,YAAQ,OAAO,UAAU,SAAS,KAAK;AAAA,WAChC;AAAA,WACA;AACH;AAAA;AAGA,qBAAa,IAAI,WAAW;AAAA;AAGhC,UAAM,cAAc,IAAI,YAAa,WAA0B;AAC/D,WAAO,eAAe,gBAAgB,aAAa,OAAO;AAAA;AAAA,SAGrD,gBACL,aACA,OACA,QACA;AACA,UAAM,YAAY,IAAI;AAEtB,cAAU,SAAS;AACnB,cAAU,UAAU;AAEpB,aAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,IAAI,GAAG,KAAK;AAClD,gBAAU,YAAY,KAAK,MAAM,eAAe,YAAY,KAAK;AAAA;AAGnE,WAAO;AAAA;AAAA,SAGF,WAAW,QAAgB,OAAe,QAAgB;AAC/D,UAAM,cAAc,IAAI,YACtB,OAAO,QACP,OAAO,YACP,OAAO,aAAa,YAAY;AAElC,WAAO,eAAe,gBAAgB,aAAa,OAAO;AAAA;AAAA;;;ACjJ9D,AAeA,IAAM,YAAY;AAEX,kBAAkB,KAAa,gBAAwB;AAC5D,QAAM,SAAS;AACf,QAAM,MAAM,SAAS;AACrB,QAAM,OAAO,MAAM;AAEnB,WAAS,IAAI,GAAG,MAAM,MAAM,MAAM,IAAI,gBAAgB,KAAK,OAAO,KAAK;AACrE,QAAI,OAAO,OAAO,MAAM,MAAM;AAAK,aAAO;AAAA;AAE5C,SAAO;AAAA;AAGF,oBAAc;AAAA,EAKnB,cAAc;AAJG;AACA,uCAAuB;AAChC,mCAAqC;AAG3C,SAAK,kBAAkB,IAAI;AAC3B,SAAK,gBAAgB,UAAU;AAC/B,SAAK,cAAc,KAAK,gBAAgB;AAAA;AAAA,EAG1C,IAAI,OAAc;AAChB,SAAK,YAAY,KAAK;AACtB,SAAK,gBAAgB,SAAS,KAAK,YAAY;AAAA;AAAA,EAGjD,IAAI,OAAc;AAChB,aAAS,IAAI,KAAK,YAAY,SAAS,GAAG,KAAK,GAAG,KAAK;AACrD,UAAI,MAAM,WAAW,KAAK,YAAY,GAAG;AAAQ,eAAO;AAAA;AAG1D,WAAO;AAAA;AAAA,EAIT,gBACE,yBACA,OACA;AACA,WAAO,KAAK,YACV,KAAK,iBAAiB,yBAAyB,SAAS;AAAA;AAAA,EAI5D,oBAAoB;AAClB,WAAO,KAAK;AAAA;AAAA,EAiCN,uBAAuB,KAAa;AAC1C,WAAO,OAAO,KAAK,QAAQ,SAAS,WAAW,KAAK,QAAQ,OAAO;AAAA;AAAA,EAG7D,iBACN,yBACA,OACA;AACA,QAAI,MAAM,KAAK,uBAAuB,KAAK,MAAM;AACjD,QAAI,OAAO;AAAG,aAAO;AAErB,QAAI,kBAAkB,OAAO;AAE7B,UAAM;AACN,aAAS,IAAI,GAAG,IAAI,KAAK,YAAY,QAAQ,IAAI,GAAG,KAAK;AACvD,YAAM,IAAI,KAAK,YAAY;AAC3B,YAAM,WAAW,wBAAwB,aACvC,MAAM,GACN,MAAM,GACN,MAAM,GACN,MAAM,GACN,EAAE,GACF,EAAE,GACF,EAAE,GACF,EAAE;AAGJ,UAAI,WAAW,iBAAiB;AAC9B,0BAAkB;AAClB,cAAM;AAAA;AAAA;AAIV,SAAK,QAAQ,MAAM,UAAU;AAC7B,WAAO;AAAA;AAAA,EAsDT,OAAO;AACL,SAAK,UAAU;AACf,SAAK,YAAY,KAAK,CAAC,GAAU,MAAa;AAC5C,YAAM,OAAO,QAAQ,EAAE,GAAG,EAAE,GAAG,EAAE;AACjC,YAAM,OAAO,QAAQ,EAAE,GAAG,EAAE,GAAG,EAAE;AAGjC,YAAM,OACJ,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,IAAI,IAAI,SAAS,KAAK,GAAG;AACxD,YAAM,OACJ,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,IAAI,IAAI,SAAS,KAAK,GAAG;AAMxD,YAAM,UAAU,OAAO;AACvB,UAAI;AAAS,eAAO,CAAC;AAMrB,YAAM,KAAK,EAAE,cAAc;AAC3B,YAAM,KAAK,EAAE,cAAc;AAE3B,UAAI,KAAK,OAAO;AAAG,eAAO,KAAK;AAE/B,YAAM,UAAY,MAAK,IAAI,MAAO,KAAO,MAAK,IAAI,MAAO;AACzD,UAAI;AAAS,eAAO,CAAC;AAErB,aAAO;AAAA;AAAA;AAAA;;;ACvNb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,AAUA,qBAAe;AAAA,EAAf,cAVA;AAWE,+BAAM;AACN,gCAAiB;AAAA;AAAA;AAGZ,0BAAoB;AAAA,EAMzB,YAAY,WAAmB,SAAiB;AALxC;AACA;AACA;AACA;AAGN,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,SAAS;AAEd,aAAS,IAAI,GAAG,KAAK,WAAW,KAAK;AACnC,WAAK,OAAO,KAAK,IAAI;AAAA;AAGvB,SAAK,cAAc;AAAA;AAAA,EAGrB,MAAM,KAAa;AACjB,QAAI,KAAK,gBAAgB,KAAK,aAAa,GAAG;AAC5C,WAAK,QAAQ,MAAM;AAAA;AAAA;AAGrB,UAAM,IAAI,MAAM;AAChB,UAAM,IAAK,QAAQ,IAAK;AACxB,UAAM,IAAK,QAAQ,KAAM;AACzB,UAAM,KACJ,MAAM,KAAK,MAAM,IACb,IACA,IAAI,SAAS,QAAQ,GAAG,GAAG,GAAG,GAAG,KAAK;AAC5C,UAAM,KAAK,KAAK,OAAO;AACvB,UAAM,MAAM,KAAK;AAEjB,OAAG;AAEH,QAAI,GAAG,MAAM,KAAK;AAChB;AAAA;AAEF,QAAI,GAAG,QAAQ,KAAK;AAClB,WAAK;AAAA;AAGP,QAAI,GAAG,OAAO,KAAK;AACjB,WAAK,OAAO,IAAI,KAAK,KAAK;AAAA;AAAA;AAAA,EAI9B,qBAAqB,OAA+B;AAClD,aAAS,IAAI,GAAG,KAAK,KAAK,YAAY,KAAK;AACzC,UAAI,KAAK,OAAO,GAAG,OAAO,KAAK,UAAU;AACvC,aAAK,OAAO,GAAG,KAAK,QAAQ,CAAC,QAAgB;AAC3C,cAAI,CAAC,MAAM,MAAM;AACf,kBAAM,OAAO;AAAA,iBACR;AACL,kBAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOhB,gBAAgB,OAAiB;AAC/B,aAAS,IAAI,GAAG,KAAK,KAAK,YAAY,KAAK;AACzC,UAAI,KAAK,OAAO,GAAG,OAAO,KAAK,UAAU;AACvC,aAAK,OAAO,GAAG,KAAK,QAAQ,CAAC,QAAiB;AAC5C,cAAI,MAAM,QAAQ,SAAmB,IAAI;AACvC,kBAAM,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACjFhB,6BAAsB;AAAA,EAU3B,YAAY,YAAoB,eAAuB;AAPvD;AAEQ;AACA;AACA;AACA;AAGN,SAAK,SAAS;AACd,SAAK,iBAAiB;AACtB,SAAK,QAAQ,KAAK,IAAI,GAAI,KAAK,SAAU,kBAAgB,QAAQ,KAAM;AACvE,SAAK,QAAQ,CAAC,KAAK;AACnB,SAAK,WAAW;AAAA;AAAA,EAGlB,aAAa,SAAiB;AAC5B,QAAI,UAAU,KAAK,SAAS,KAAK,OAAO;AACtC,WAAK,QAAQ;AACb,WAAK,WAAW,KAAK,IAClB,KAAK,iBAAiB,KAAK,QAAS,KAAK,QAC1C,KAAK;AAEP,aAAO;AAAA;AAGT,WAAO;AAAA;AAAA;AA5BJ;AACW,cADX,iBACW,SAAQ;AFD1B;;AGqBA,AAiBA,IAAM,mBAAmB;AAEzB,mBAAa;AAAA,EAMX,YAAY,cAAsB;AALlC;AACA;AACA;AACA;AAGE,SAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI;AAAA;AAAA,EAQtC,UAAU;AACR,WAAO,MAAM,aACX,KAAK,KAAK,kBACV,KAAK,KAAK,kBACV,KAAK,KAAK,kBACV,KAAK,KAAK;AAAA;AAAA,EAId,SAAS,GAAW,GAAW,GAAW,GAAW;AACnD,SAAK,KAAK,IAAI;AACd,SAAK,KAAK,IAAI;AACd,SAAK,KAAK,IAAI;AACd,SAAK,KAAK,IAAI;AAAA;AAAA;AAuBX,8BAAuB,yBAAyB;AAAA,EAwErD,YACE,yBACA,SAAS,KACT;AACA;AAnBM;AACS;AACT;AAGS;AACT;AAGA;AAGA;AACS;AAOf,SAAK,YAAY;AACjB,SAAK,cAAc;AACnB,SAAK,gBAAgB;AACrB,SAAK,eAAe;AAEpB,SAAK,UAAU,cACb,OAAO,kBACP,OAAO,kBACP,OAAO,kBACP,OAAO;AAAA;AAAA,EAIX,OAAO,gBAAgC;AACrC,SAAK,cAAc,KAAK,YAAY,OAAO,eAAe;AAAA;AAAA,GAG3D,WAAyD;AACxD,SAAK;AAEL,WAAO,KAAK;AAEZ,UAAM;AAAA,MACJ,SAAS,KAAK;AAAA,MACd,UAAU;AAAA;AAAA;AAAA,EAIN,QAAQ;AACd,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,aAAS,IAAI,GAAG,IAAI,KAAK,cAAc,KAAK;AAC1C,WAAK,SAAS,KAAK,IAAI,OACnB,MAAM,mBAAmB,KAAM,KAAK,eAAgB;AAIxD,WAAK,MAAM,KAAM,UAAS,eAAe,KAAK,eAAgB;AAC9D,WAAK,MAAM,KAAK;AAAA;AAAA;AAAA,GAOX,SAAS;AAChB,QAAI,eAAe,KAAK;AACxB,UAAM,eAAe,KAAK,YAAY;AACtC,QAAI,eAAe,UAAS;AAAkB,qBAAe;AAE7D,UAAM,WAAY,KAAM,gBAAe,KAAK,IAAK;AACjD,UAAM,iBAAkB,eAAe,eAAgB;AAEvD,QAAI,QAAS,iBAAiB,UAAS,WAAY;AACnD,QAAI,QAAQ,UAAS;AACrB,QAAI,SAAU,MAAK,gBAAgB,KAAK,UAAS;AAEjD,QAAI,MAAM,UAAU,UAAS;AAC7B,QAAI,OAAO;AAAG,YAAM;AAEpB,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,WAAK,UAAU,KACZ,QAAW,QAAM,MAAM,IAAI,KAAK,UAAS,WAAa,OAAM,UAC7D;AAAA;AAGJ,QAAI;AACJ,QAAI,eAAe,UAAS,kBAAkB;AAC5C,aAAO;AAAA,eACE,eAAe,UAAS,YAAY,GAAG;AAChD,aAAO,UAAS;AAAA,eACP,eAAe,UAAS,YAAY,GAAG;AAChD,aAAO,UAAS;AAAA,eACP,eAAe,UAAS,YAAY,GAAG;AAChD,aAAO,UAAS;AAAA,WACX;AACL,aAAO,UAAS;AAAA;AAGlB,UAAM,UAAU,IAAI,gBAAgB,gBAAgB;AACpD,aAAS,IAAI,GAAG,aAAa,GAAG,IAAI,kBAAkB;AACpD,UAAI,QAAQ,aAAa,IAAI;AAC3B,cAAM;AAAA,UACJ,UAAU,QAAQ;AAAA;AAAA;AAItB,YAAM,QAAQ,KAAK,YAAY;AAC/B,YAAM,IAAI,MAAM,KAAK;AACrB,YAAM,IAAI,MAAM,KAAK;AACrB,YAAM,IAAI,MAAM,KAAK;AACrB,YAAM,IAAI,MAAM,KAAK;AACrB,YAAM,cAAc,KAAK,SAAS,GAAG,GAAG,GAAG;AAE3C,WAAK,aAAa,OAAO,aAAa,GAAG,GAAG,GAAG;AAC/C,UAAI,QAAQ;AAAG,aAAK,gBAAgB,KAAK,aAAa,GAAG,GAAG,GAAG;AAG/D,oBAAc;AACd,UAAI,cAAc;AAAc,sBAAc;AAC9C;AAEA,UAAI,UAAU;AAAG,gBAAQ;AAEzB,UAAI,IAAI,UAAU,GAAG;AACnB,iBAAU,QAAQ,WAAY;AAC9B,kBAAW,SAAS,UAAS,kBAAmB;AAChD,cAAM,UAAU,UAAS;AAEzB,YAAI,OAAO;AAAG,gBAAM;AACpB,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,eAAK,UAAU,KACZ,QACI,QAAM,MAAM,IAAI,KAAK,UAAS,WAAa,OAAM,UACtD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMF,gBAAgB;AACtB,UAAM,UAAU,IAAI;AAEpB,SAAK,SAAS,QAAQ,CAAC,WAAW;AAChC,cAAQ,IAAI,OAAO;AAAA;AAGrB,YAAQ;AACR,WAAO;AAAA;AAAA,EAMD,gBACN,KACA,GACA,GACA,GACA,GACA,IACA;AACA,QAAI,KAAK,IAAI;AACb,QAAI,KAAK;AAAI,WAAK;AAElB,QAAI,KAAK,IAAI;AACb,QAAI,KAAK,KAAK;AAAc,WAAK,KAAK;AAEtC,QAAI,IAAI,IAAI;AACZ,QAAI,IAAI,IAAI;AACZ,QAAI,IAAI;AAER,WAAO,IAAI,MAAM,IAAI,IAAI;AACvB,YAAM,IAAI,KAAK,UAAU,OAAO,UAAS;AACzC,UAAI,IAAI,IAAI;AACV,cAAM,IAAI,KAAK,SAAS;AACxB,UAAE,SAAS,IAAK,GAAE,IAAI,IAAI,IAAK,GAAE,IAAI,IAAI,IAAK,GAAE,IAAI,IAAI,IAAK,GAAE,IAAI;AAAA;AAGrE,UAAI,IAAI,IAAI;AACV,cAAM,IAAI,KAAK,SAAS;AACxB,UAAE,SAAS,IAAK,GAAE,IAAI,IAAI,IAAK,GAAE,IAAI,IAAI,IAAK,GAAE,IAAI,IAAI,IAAK,GAAE,IAAI;AAAA;AAAA;AAAA;AAAA,EAQjE,aACN,OACA,GACA,GACA,GACA,GACA,GACA;AACA,aAAS,UAAS;AAGlB,UAAM,IAAI,KAAK,SAAS;AACxB,MAAE,SACA,QAAS,GAAE,IAAI,IACf,QAAS,GAAE,IAAI,IACf,QAAS,GAAE,IAAI,IACf,QAAS,GAAE,IAAI;AAAA;AAAA,EAeX,SAAS,GAAW,GAAW,GAAW,GAAW;AAC3D,UAAM,aAAc,MAAM,KAAM;AAEhC,QAAI,QAAQ,CAAE,MAAK;AACnB,QAAI,YAAY;AAChB,QAAI,UAAU;AACd,QAAI,cAAc;AAElB,aAAS,IAAI,GAAG,IAAI,KAAK,cAAc,KAAK;AAC1C,YAAM,IAAI,KAAK,SAAS;AACxB,YAAM,OACH,KAAK,UAAU,oBAAoB,GAAG,EAAE,GAAG,GAAG,GAAG,OAAO,aACzD;AAEF,UAAI,OAAO,OAAO;AAChB,gBAAQ;AACR,kBAAU;AAAA;AAGZ,YAAM,WACJ,OACC,MAAK,MAAM,MAAO,UAAS,oBAAoB;AAClD,UAAI,WAAW,WAAW;AACxB,oBAAY;AACZ,sBAAc;AAAA;AAEhB,YAAM,WAAW,KAAK,MAAM,MAAM,UAAS;AAC3C,WAAK,MAAM,MAAM;AACjB,WAAK,MAAM,MAAM,YAAY,UAAS;AAAA;AAExC,SAAK,MAAM,YAAY,UAAS;AAChC,SAAK,MAAM,YAAY,UAAS;AAChC,WAAO;AAAA;AAAA;AAvTJ;AAKmB,cALnB,UAKmB,WAAU;AACV,cANnB,UAMmB,WAAU;AACV,cAPnB,UAOmB,WAAU;AACV,cARnB,UAQmB,WAAU;AACV,cATnB,UASmB,oBAAmB,UAAS;AAG5B,cAZnB,UAYmB,YAAW;AAGX,cAfnB,UAemB,qBAAoB;AAGpB,cAlBnB,UAkBmB,gBAAe,KAAK,UAAS;AAC7B,cAnBnB,UAmBmB,eAAc;AAKd,cAxBnB,UAwBmB,cAAa;AACb,cAzBnB,UAyBmB,SAAQ,UAAS,gBAAgB,UAAS;AAG1C,cA5BnB,UA4BmB,cACtB,UAAS,gBAAiB,UAAS,cAAc,UAAS;AAKpC,cAlCnB,UAkCmB,oBAAmB;AAGnB,cArCnB,UAqCmB,eAAc,KAAK,UAAS;AAG5B,cAxCnB,UAwCmB,mBAAkB;AAKlB,cA7CnB,UA6CmB,mBAAkB;AAGlB,cAhDnB,UAgDmB,cAAa,KAAK,UAAS;AAG3B,cAnDnB,UAmDmB,iBAAgB;AAChB,cApDnB,UAoDmB,YAAW,KAAK,UAAS;AACzB,cArDnB,UAqDmB,sBACtB,UAAS,kBAAkB,UAAS;AACd,cAvDnB,UAuDmB,iBAAgB,KAAK,UAAS;;;AC9HxD,AAgBA,IAAM,oBAAmB;AAEzB,wBAAkB;AAAA,EAMhB,YAAY,cAAsB;AALlC;AACA;AACA;AACA;AAGE,SAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI;AAAA;AAAA,EAQtC,UAAU;AACR,WAAO,MAAM,aACX,KAAK,KAAK,mBACV,KAAK,KAAK,mBACV,KAAK,KAAK,mBACV,KAAK,KAAK;AAAA;AAAA,EAId,SAAS,GAAW,GAAW,GAAW,GAAW;AACnD,SAAK,KAAK;AACV,SAAK,KAAK;AACV,SAAK,KAAK;AACV,SAAK,KAAK;AAAA;AAAA;AAIP,mCAA4B,yBAAyB;AAAA,EA0E1D,YACE,yBACA,SAAS,KACT;AACA;AAnBM;AACS;AACT;AAGS;AACT;AAGA;AAGA;AACS;AAOf,SAAK,YAAY;AACjB,SAAK,cAAc;AACnB,SAAK,gBAAgB;AACrB,SAAK,eAAe;AAEpB,SAAK,UAAU,cACb,OAAO,mBACP,OAAO,mBACP,OAAO,mBACP,OAAO;AAAA;AAAA,EAIX,OAAO,gBAAgC;AACrC,SAAK,cAAc,KAAK,YAAY,OAAO,eAAe;AAAA;AAAA,GAG3D,WAAW;AACV,SAAK;AACL,WAAO,KAAK;AAEZ,UAAM;AAAA,MACJ,SAAS,KAAK;AAAA,MACd,UAAU;AAAA;AAAA;AAAA,EAIN,QAAQ;AACd,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,aAAS,IAAI,GAAG,IAAI,KAAK,cAAc,KAAK;AAC1C,WAAK,SAAS,KAAK,IAAI,YACpB,MAAM,oBAAmB,KAAM,KAAK;AAIvC,WAAK,MAAM,KAAK,eAAc,eAAe,KAAK;AAClD,WAAK,MAAM,KAAK;AAAA;AAAA;AAAA,GAOX,SAAuD;AAC9D,QAAI,eAAe,KAAK;AAExB,UAAM,eAAe,KAAK,YAAY;AACtC,QAAI,eAAe,eAAc;AAAkB,qBAAe;AAElE,UAAM,WAAW,KAAM,gBAAe,KAAK;AAC3C,UAAM,iBAAiB,eAAe;AAEtC,QAAI,QAAS,iBAAiB,eAAc,WAAY;AACxD,QAAI,QAAQ,eAAc;AAC1B,QAAI,SAAU,MAAK,gBAAgB,KAAK,eAAc;AAEtD,QAAI,MAAM,UAAU,eAAc;AAClC,QAAI,OAAO;AAAG,YAAM;AAEpB,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,WAAK,UAAU,KACb,QAAW,QAAM,MAAM,IAAI,KAAK,eAAc,WAAa,OAAM;AAAA;AAGrE,QAAI;AACJ,QAAI,eAAe,eAAc,kBAAkB;AACjD,aAAO;AAAA,eACE,eAAe,eAAc,YAAY,GAAG;AACrD,aAAO,eAAc;AAAA,eACZ,eAAe,eAAc,YAAY,GAAG;AACrD,aAAO,eAAc;AAAA,eACZ,eAAe,eAAc,YAAY,GAAG;AACrD,aAAO,eAAc;AAAA,WAChB;AACL,aAAO,eAAc;AAAA;AAGvB,UAAM,UAAU,IAAI,gBAAgB,gBAAgB;AACpD,aAAS,IAAI,GAAG,aAAa,GAAG,IAAI,kBAAkB;AACpD,UAAI,QAAQ,aAAa,IAAI;AAC3B,cAAM;AAAA,UACJ,UAAU,QAAQ;AAAA;AAAA;AAItB,YAAM,QAAQ,KAAK,YAAY;AAC/B,YAAM,IAAI,MAAM,KAAK;AACrB,YAAM,IAAI,MAAM,KAAK;AACrB,YAAM,IAAI,MAAM,KAAK;AACrB,YAAM,IAAI,MAAM,KAAK;AACrB,YAAM,cAAc,KAAK,SAAS,GAAG,GAAG,GAAG;AAE3C,WAAK,aAAa,OAAO,aAAa,GAAG,GAAG,GAAG;AAC/C,UAAI,QAAQ;AAAG,aAAK,gBAAgB,KAAK,aAAa,GAAG,GAAG,GAAG;AAG/D,oBAAc;AACd,UAAI,cAAc;AAAc,sBAAc;AAC9C;AAEA,UAAI,UAAU;AAAG,gBAAQ;AAEzB,UAAI,IAAI,UAAU,GAAG;AACnB,iBAAS,QAAQ;AACjB,kBAAU,SAAS,eAAc;AACjC,cAAM,UAAU,eAAc;AAE9B,YAAI,OAAO;AAAG,gBAAM;AACpB,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,eAAK,UAAU,KACb,QACG,QAAM,MAAM,IAAI,KAAK,eAAc,WAAa,OAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAM3D,gBAAgB;AACtB,UAAM,UAAU,IAAI;AAEpB,SAAK,SAAS,QAAQ,CAAC,WAAW;AAChC,cAAQ,IAAI,OAAO;AAAA;AAGrB,YAAQ;AACR,WAAO;AAAA;AAAA,EAMD,gBACN,KACA,GACA,GACA,GACA,GACA,IACA;AACA,QAAI,KAAK,IAAI;AACb,QAAI,KAAK;AAAI,WAAK;AAElB,QAAI,KAAK,IAAI;AACb,QAAI,KAAK,KAAK;AAAc,WAAK,KAAK;AAEtC,QAAI,IAAI,IAAI;AACZ,QAAI,IAAI,IAAI;AACZ,QAAI,IAAI;AAER,WAAO,IAAI,MAAM,IAAI,IAAI;AACvB,YAAM,IAAI,KAAK,UAAU,OAAO,eAAc;AAC9C,UAAI,IAAI,IAAI;AACV,cAAM,IAAI,KAAK,SAAS;AACxB,UAAE,SAAS,IAAK,GAAE,IAAI,IAAI,IAAK,GAAE,IAAI,IAAI,IAAK,GAAE,IAAI,IAAI,IAAK,GAAE,IAAI;AAAA;AAGrE,UAAI,IAAI,IAAI;AACV,cAAM,IAAI,KAAK,SAAS;AACxB,UAAE,SAAS,IAAK,GAAE,IAAI,IAAI,IAAK,GAAE,IAAI,IAAI,IAAK,GAAE,IAAI,IAAI,IAAK,GAAE,IAAI;AAAA;AAAA;AAAA;AAAA,EAQjE,aACN,OACA,GACA,GACA,GACA,GACA,GACA;AACA,aAAS,eAAc;AAGvB,UAAM,IAAI,KAAK,SAAS;AACxB,MAAE,SACA,QAAS,GAAE,IAAI,IACf,QAAS,GAAE,IAAI,IACf,QAAS,GAAE,IAAI,IACf,QAAS,GAAE,IAAI;AAAA;AAAA,EAeX,SAAS,GAAW,GAAW,GAAW,IAAY;AAC5D,UAAM,aAAc,MAAM,KAAM;AAEhC,QAAI,QAAQ,CAAE,MAAK;AACnB,QAAI,YAAY;AAChB,QAAI,UAAU;AACd,QAAI,cAAc;AAElB,aAAS,IAAI,GAAG,IAAI,KAAK,cAAc,KAAK;AAC1C,YAAM,IAAI,KAAK,SAAS;AACxB,YAAM,OACJ,KAAK,UAAU,oBAAoB,GAAG,EAAE,GAAG,GAAG,GAAG,GAAG,QAAQ;AAE9D,UAAI,OAAO,OAAO;AAChB,gBAAQ;AACR,kBAAU;AAAA;AAGZ,YAAM,WACJ,OACC,MAAK,MAAM,MAAO,eAAc,oBAAoB;AACvD,UAAI,WAAW,WAAW;AACxB,oBAAY;AACZ,sBAAc;AAAA;AAEhB,YAAM,WAAW,KAAK,MAAM,MAAM,eAAc;AAChD,WAAK,MAAM,MAAM;AACjB,WAAK,MAAM,MAAM,YAAY,eAAc;AAAA;AAE7C,SAAK,MAAM,YAAY,eAAc;AACrC,SAAK,MAAM,YAAY,eAAc;AACrC,WAAO;AAAA;AAAA;AAtTJ;AAKmB,cALnB,eAKmB,WAAU;AACV,cANnB,eAMmB,WAAU;AACV,cAPnB,eAOmB,WAAU;AACV,cARnB,eAQmB,WAAU;AACV,cATnB,eASmB,oBAAmB,eAAc;AAGjC,cAZnB,eAYmB,YAAW;AAGX,cAfnB,eAemB,qBAAoB;AAGpB,cAlBnB,eAkBmB,gBAAe,KAAK,eAAc;AAClC,cAnBnB,eAmBmB,eAAc;AAKd,cAxBnB,eAwBmB,cAAa;AACb,cAzBnB,eAyBmB,SACtB,eAAc,gBAAgB,eAAc;AAGtB,cA7BnB,eA6BmB,cACtB,eAAc,gBACb,eAAc,cAAc,eAAc;AAKrB,cApCnB,eAoCmB,oBAAmB;AAGnB,cAvCnB,eAuCmB,eAAc,KAAK,eAAc;AAGjC,cA1CnB,eA0CmB,mBAAkB;AAKlB,cA/CnB,eA+CmB,mBAAkB;AAGlB,cAlDnB,eAkDmB,cAAa,KAAK,eAAc;AAGhC,cArDnB,eAqDmB,iBAAgB;AAChB,cAtDnB,eAsDmB,YAAW,KAAK,eAAc;AAC9B,cAvDnB,eAuDmB,sBACtB,eAAc,kBAAkB,eAAc;AACxB,cAzDnB,eAyDmB,iBAAgB,KAAK,eAAc;;;ACxH7D,AAkBO,4BAAqB;AAAA,EAmB1B,YAAY,QAAgB,QAAgB;AAbpC;AAGA;AAEA;AAGA;AAGA;AAIN,SAAK,UAAU;AAGf,SAAK,cAAc,UAAU;AAG7B,SAAK,cAAc,UAAU;AAG7B,SAAK,YAAY,IAAI,cACnB,gBAAe,YACf,KAAK;AAGP,SAAK,aAAa,uBAAO,OAAO;AAAA;AAAA,EAGlC,OAAO,gBAAgC;AACrC,YAAQ,KAAK;AAAA,WACN;AACH,aAAK,cAAc;AACnB;AAAA,WACG;AACH,aAAK,cAAc;AACnB;AAAA;AAAA;AAAA,EAIN,kCAAkC;AAEhC,UAAM,SAAS,WACb,OAAO,KAAK,KAAK,aACjB,CAAC,GAAG,MAAM,KAAK,WAAW,KAAK,KAAK,WAAW;AAEjD,QAAI,OAAO,WAAW,GAAG;AACvB,aAAO;AAAA;AAGT,QAAI;AACJ,YAAQ,KAAK;AAAA,WACN;AACH,cAAM,qBAAqB,KAAK,IAAI,OAAO,QAAQ,KAAK;AACxD,cAAM,OAAO,OAAO,qBAAqB;AACzC,cAAM,OAAO,KAAK,WAAW;AAE7B,iBAAS,OAAO,MAAM,GAAG;AAGzB,YAAI,MAAM;AACV,cAAM,MAAM,OAAO;AACnB,eAAO,MAAM,OAAO,KAAK,WAAW,OAAO,UAAU,MAAM;AACzD,iBAAO,KAAK,OAAO;AAAA;AAIrB,aAAK,UAAU,gBAAgB;AAC/B;AAAA,WAEG;AACH,iBAAS;AACT;AAAA;AAIA,cAAM,IAAI,MAAM;AAAA;AAIpB,WAAO,OAAO,IAAI,CAAC,MAAM,CAAC;AAAA;AAAA,EAIpB,cAAc,gBAAgC;AACpD,UAAM,QAAQ,KAAK;AACnB,UAAM,aAAa,eAAe;AAClC,UAAM,MAAM,WAAW;AAEvB,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,YAAM,MAAM,WAAW,GAAG;AAG1B,WAAK,UAAU,MAAM;AAErB,UAAI,OAAO,OAAO;AAChB,cAAM;AAAA,aACD;AACL,cAAM,OAAO;AAAA;AAAA;AAAA;AAAA,EAQX,cAAc,gBAAgC;AACpD,UAAM,QAAQ,eAAe;AAC7B,UAAM,SAAS,eAAe;AAC9B,UAAM,aAAa,eAAe;AAElC,UAAM,OAAO,gBAAe,SAAS;AACrC,UAAM,OAAO,gBAAe,SAAS;AACrC,UAAM,OAAO,OAAO;AACpB,UAAM,QAAQ,KAAK,WAAW,OAAO,QAAQ,MAAM;AACnD,UAAM,QAAQ,KAAK;AAEnB,UAAM,QAAQ,CAAC,QAAQ;AACrB,UAAI,OAAO,KAAK,MAAO,IAAI,IAAI,IAAI,IAAK,QAAQ,gBAAe;AAC/D,UAAI,OAAO;AAAG,eAAO;AAErB,YAAM,QAAgC;AACtC,WAAK,YAAY,KAAK,OAAO,CAAC,MAAM;AAClC,cAAM,MAAM,WAAW,GAAG;AAG1B,aAAK,UAAU,MAAM;AAErB,YAAI,OAAO,OAAO;AAChB,gBAAM;AAAA,mBACG,OAAO,OAAO;AACvB,cAAI,EAAE,MAAM,QAAQ,MAAM;AACxB,kBAAM,OAAO,MAAM;AAAA;AAAA,eAEhB;AACL,gBAAM,OAAO;AAAA;AAAA;AAAA;AAMnB,SAAK,UAAU,qBAAqB;AAAA;AAAA,EAI9B,YAAY,MAAW,KAAa,IAAyB;AACnE,UAAM,IAAI;AACV,UAAM,KAAK,EAAE,IAAI,MAAM,EAAE;AACzB,UAAM,KAAM,GAAE,IAAI,EAAE,IAAI,KAAK,MAAO,GAAE,IAAI,EAAE,IAAI;AAChD,UAAM,OAAO,MAAM,EAAE,IAAI;AAEzB,QAAI,MAAM;AACV,QAAI,IAAI;AAER,OAAG;AACD,SAAG,KAAK,MAAM;AACd,WAAK,EAAE,MAAM,EAAE,MAAM,IAAI,OAAO;AAAA,aACzB,KAAK;AAAA;AAAA,EAOR,WACN,OACA,QACA,OACA,OACA;AACA,UAAM,OAAO,QAAQ;AACrB,UAAM,OAAO,SAAS;AACtB,UAAM,OAAO,QAAQ;AACrB,UAAM,OAAO,SAAS;AACtB,UAAM,aAAa;AAEnB,aAAS,KAAI,GAAG,KAAI,QAAQ,MAAK,OAAO;AACtC,eAAS,KAAI,GAAG,KAAI,OAAO,MAAK,OAAO;AACrC,mBAAW,KAAK;AAAA,UACd;AAAA,UACA;AAAA,UACA,GAAG,OAAM,OAAO,OAAO;AAAA,UACvB,GAAG,OAAM,OAAO,OAAO;AAAA;AAAA;AAAA;AAK7B,WAAO;AAAA;AAAA;AApMJ;AACU,cADV,gBACU,YAAW,CAAC,IAAI;AAChB,cAFV,gBAEU,cAAa;AACb,cAHV,gBAGU,cAAa;;;ACrB9B,AAkBA,yBAAmB;AAAA,EAKjB,YAAY,OAAe,OAAc,UAAkB;AAJlD;AACA;AACA;AAGP,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,WAAW;AAAA;AAAA;AAKb,6BAAuB,yBAAyB;AAAA,EAcrD,YACE,yBACA,SAAS,KACT,SAAS,GACT;AACA;AAjBe;AAGA;AAGA;AAGA;AACA;AAQf,SAAK,YAAY;AAEjB,SAAK,UAAU;AAGf,SAAK,aAAa,IAAI,eAAe,QAAQ;AAE7C,SAAK,mBAAmB;AACxB,SAAK,qBAAqB;AAAA;AAAA,EAI5B,OAAO,OAAuB;AAiB5B,SAAK,WAAW,OAAO;AAAA;AAAA,GAIxB,WAAW;AACV,UAAM,SAAS,KAAK,WAAW;AAC/B,QAAI,OAAO,WAAW,GAAG;AACvB,YAAM,IAAI,MAAM;AAAA;AAGlB,WAAO,KAAK,cAAc;AAAA;AAAA,GAInB,cACP,QAC8C;AAG9C,UAAM,UAAU,IAAI;AACpB,UAAM,aAAa,QAAQ,oBAAoB;AAC/C,UAAM,aAAa,IAAI,MAAM,OAAO;AAEpC,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,iBAAW,KAAK,MAAM,eAAe,OAAO;AAC5C,iBAAW,KAAK;AAAA;AAGlB,UAAM,MAAM,WAAW;AACvB,UAAM,UAAU;AAEhB,QAAI,SAAS;AACb,QAAI,QAAQ,KAAK;AAGjB,UAAM,UAAU,IAAI,gBAAgB,SAAS,KAAK,SAAS;AAC3D,WAAO,SAAS,KAAK,SAAS;AAC5B,cAAQ,SAAS;AAGjB,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,YAAI,QAAQ,aAAa,MAAM,SAAS;AACtC,gBAAM;AAAA,YACJ,UAAU,QAAQ;AAAA;AAAA;AAItB,YAAI,WAAW,OAAO;AAAG;AACzB,cAAM,MAAM,WAAW;AAGvB,iBAAS,IAAI,IAAI,GAAG,IAAI,KAAK,KAAK;AAChC,cAAI,WAAW,OAAO;AAAG;AACzB,gBAAM,MAAM,WAAW;AAGvB,gBAAM,OAAO,KAAK,UAAU,oBAAoB,KAAK;AACrD,cAAI,OAAO,OAAO;AAEhB,oBAAQ,KAAK,IAAI,aAAa,GAAG,KAAK;AACtC,uBAAW,KAAK;AAChB;AAAA;AAAA;AAAA;AAQN,eACE,SAAS,KAAK,UAAU,IACpB,KAAK,mBACL,KAAK;AAAA;AAIb,QAAI,SAAS,KAAK,SAAS;AAEzB,iBAAW,SAAS,CAAC,GAAG,MAAM,EAAE,WAAW,EAAE;AAE7C,UAAI,IAAI;AACR,aAAO,SAAS,KAAK,WAAW,IAAI,QAAQ,QAAQ;AAClD,cAAM,eAAe,QAAQ;AAE7B,mBAAW,aAAa,SAAS;AACjC;AACA;AAAA;AAAA;AAIJ,QAAI,SAAS,WAAW;AACxB,aAAS,aAAa,SAAS,GAAG,cAAc,GAAG,cAAc;AAC/D,UAAI,WAAW,gBAAgB,GAAG;AAChC,YAAI,eAAe,SAAS,GAAG;AAC7B,qBAAW,cAAc,WAAW,SAAS;AAAA;AAE/C,UAAE;AAAA;AAAA;AAGN,eAAW,SAAS;AAEpB,YAAQ;AAER,UAAM;AAAA,MACJ;AAAA,MACA,UAAU;AAAA;AAAA;AAAA;;;AChMhB,AAeA,uBAAuB,YAAoB;AACzC,QAAM,IAAI;AACV,WAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,MAAE,KAAK;AAAA;AAET,SAAO;AAAA;AAGT,uBACE,YACA,YACA,YACA,YACgB;AAChB,QAAM,IAAI,IAAI,MAAM;AACpB,WAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,MAAE,KAAK,IAAI,MAAM;AACjB,aAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,QAAE,GAAG,KAAK,IAAI,MAAM;AACpB,eAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,UAAE,GAAG,GAAG,KAAK,IAAI,MAAM;AACvB,iBAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,YAAE,GAAG,GAAG,GAAG,KAAK;AAAA;AAAA;AAAA;AAAA;AAKxB,SAAO;AAAA;AAGT,uBACE,YACA,YACA,YACc;AACd,QAAM,IAAI,IAAI,MAAM;AACpB,WAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,MAAE,KAAK,IAAI,MAAM;AACjB,aAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,QAAE,GAAG,KAAK,IAAI,MAAM;AACpB,eAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,UAAE,GAAG,GAAG,KAAK;AAAA;AAAA;AAAA;AAInB,SAAO;AAAA;AAGT,qBACE,GACA,YACA,YACA,YACA,OACA;AACA,WAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,MAAE,KAAK;AACP,aAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,QAAE,GAAG,KAAK;AACV,eAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,UAAE,GAAG,GAAG,KAAK;AAAA;AAAA;AAAA;AAAA;AAMrB,qBAAwB,GAAQ,YAAoB,OAAU;AAC5D,WAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,MAAE,KAAK;AAAA;AAAA;AAIJ,wBAAkB;AAAA,EAAlB,cAvFP;AAwFE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAGK,6BAAsB,yBAAyB;AAAA,EAgCpD,YACE,yBACA,SAAS,KACT,4BAA4B,GAC5B;AACA;AA/BM;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AAES;AAQf,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,YAAY;AAAA;AAAA,EAGnB,OAAO,OAAuB;AAC5B,UAAM,aAAa,MAAM;AAEzB,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,IAAI,GAAG,KAAK;AACjD,WAAK,UAAU,WAAW;AAAA;AAG5B,SAAK,UAAU,KAAK,QAAQ,OAAO;AAAA;AAAA,GAGpC,WAAW;AACV,WAAO,KAAK;AAEZ,UAAM,UAAU,IAAI;AAGpB,aAAS,eAAe,GAAG,eAAe,KAAK,SAAS,gBAAgB;AACtE,UAAI,KAAK,MAAM,gBAAgB,GAAG;AAChC,cAAM,MAAM,KAAK,MAAM;AACvB,cAAM,IAAI,KAAK,MAAM,gBAAgB;AACrC,cAAM,IAAI,KAAK,QAAQ,gBAAgB;AACvC,cAAM,IAAI,KAAK,OAAO,gBAAgB;AACtC,cAAM,IAAI,KAAK,QAAQ,gBAAgB;AAEvC,cAAM,QAAQ,MAAM,aAAa,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI;AAC1D,gBAAQ,IAAI;AAAA;AAAA;AAIhB,YAAQ;AAER,UAAM;AAAA,MACJ;AAAA,MACA,UAAU;AAAA;AAAA;AAAA,GAIL,kBAAkB;AAEzB,WAAO,KAAK;AAEZ,QAAI,OAAO;AACX,UAAM,iBAAiB,cAAc,KAAK;AAG1C,aAAS,YAAY,GAAG,YAAY,KAAK,SAAS,EAAE,WAAW;AAE7D,UAAI,KAAK,KAAK,KAAK,OAAO,OAAO,KAAK,OAAO,aAAa;AACxD,uBAAe,QACb,KAAK,OAAO,MAAM,SAAS,IACvB,KAAK,mBAAmB,KAAK,OAAO,SACpC;AACN,uBAAe,aACb,KAAK,OAAO,WAAW,SAAS,IAC5B,KAAK,mBAAmB,KAAK,OAAO,cACpC;AAAA,aACD;AAEL,uBAAe,QAAQ;AACvB;AAAA;AAGF,aAAO;AACP,UAAI,OAAO,eAAe;AAE1B,eAAS,QAAQ,GAAG,SAAS,WAAW,EAAE,OAAO;AAC/C,YAAI,eAAe,SAAS,MAAM;AAChC,iBAAO,eAAe;AACtB,iBAAO;AAAA;AAAA;AAIX,UAAI,QAAQ,GAAK;AACf,aAAK,UAAU,YAAY;AAC3B;AAAA;AAAA;AAIJ,UAAM,YAAY;AAClB,UAAM,cAAc;AACpB,UAAM,aAAa;AACnB,UAAM,cAAc;AAGpB,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,EAAE,GAAG;AACrC,YAAM,SAAS,SAAQ,QAAQ,KAAK,OAAO,IAAI,KAAK;AAEpD,UAAI,SAAS,GAAG;AACd,kBAAU,KACP,SAAQ,QAAQ,KAAK,OAAO,IAAI,KAAK,eAAe,SAAU;AACjE,oBAAY,KACT,SAAQ,QAAQ,KAAK,OAAO,IAAI,KAAK,iBAAiB,SAAU;AACnE,mBAAW,KACR,SAAQ,QAAQ,KAAK,OAAO,IAAI,KAAK,gBAAgB,SAAU;AAClE,oBAAY,KACT,SAAQ,QAAQ,KAAK,OAAO,IAAI,KAAK,iBAAiB,SAAU;AAAA,aAC9D;AACL,kBAAU,KAAK;AACf,oBAAY,KAAK;AACjB,mBAAW,KAAK;AAChB,oBAAY,KAAK;AAAA;AAAA;AAIrB,SAAK,QAAQ,cAAc,KAAK,UAAU;AAC1C,SAAK,UAAU,cAAc,KAAK,UAAU;AAC5C,SAAK,SAAS,cAAc,KAAK,UAAU;AAC3C,SAAK,UAAU,cAAc,KAAK,UAAU;AAC5C,SAAK,QAAQ,cAAc,KAAK,UAAU;AAG1C,aAAS,QAAQ,GAAG,IAAI,KAAK,QAAQ,QAAQ,QAAQ,GAAG,SAAS;AAC/D,YAAM,QAAQ,KAAK,QAAQ;AAE3B,YAAM,QAAQ;AAEd,UAAI,YAAY;AAChB,UAAI,eAAe,OAAO;AAE1B,eAAS,SAAS,GAAG,SAAS,KAAK,SAAS,UAAU;AACpD,cAAM,WAAW,UAAU;AAC3B,cAAM,aAAa,YAAY;AAC/B,cAAM,YAAY,WAAW;AAC7B,cAAM,aAAa,YAAY;AAE/B,cAAM,WAAW,KAAK,UAAU,aAC9B,UACA,YACA,WACA,YACA,MAAM,GACN,MAAM,GACN,MAAM,GACN,MAAM;AAGR,YAAI,WAAW,cAAc;AAC3B,yBAAe;AACf,sBAAY;AAAA;AAAA;AAIhB,WAAK,MAAM,cAAc,MAAM;AAC/B,WAAK,QAAQ,cAAc,MAAM;AACjC,WAAK,OAAO,cAAc,MAAM;AAChC,WAAK,QAAQ,cAAc,MAAM;AACjC,WAAK,MAAM;AAAA;AAAA;AAAA,EAIP,UAAU,OAAc;AAC9B,UAAM,eAAe,IAAI,KAAK;AAC9B,UAAM,WAAY,OAAM,KAAK,gBAAgB;AAC7C,UAAM,aAAc,OAAM,KAAK,gBAAgB;AAC/C,UAAM,YAAa,OAAM,KAAK,gBAAgB;AAC9C,UAAM,aAAc,OAAM,KAAK,gBAAgB;AAG/C,SAAK,SAAS,YAAY,UAAU,YAAY;AAChD,SAAK,YAAY,YAAY,UAAU,YAAY,cAAc,MAAM;AACvE,SAAK,cAAc,YAAY,UAAU,YAAY,cAAc,MAAM;AACzE,SAAK,aAAa,YAAY,UAAU,YAAY,cAAc,MAAM;AACxE,SAAK,cAAc,YAAY,UAAU,YAAY,cAAc,MAAM;AACzE,SAAK,SAAS,YAAY,UAAU,YAAY,cAC9C,KAAK,OAAO,MAAM,KAClB,KAAK,OAAO,MAAM,KAClB,KAAK,OAAO,MAAM,KAClB,KAAK,OAAO,MAAM;AAAA;AAAA,GAOb,oBAAkE;AACzE,UAAM,OAAiB;AACvB,UAAM,UAAoB;AAC1B,UAAM,YAAsB;AAC5B,UAAM,WAAqB;AAC3B,UAAM,YAAsB;AAC5B,UAAM,QAAkB;AAExB,UAAM,QAAQ,cAAc,KAAK,WAAW,KAAK,WAAW,KAAK;AACjE,UAAM,WAAW,cACf,KAAK,WACL,KAAK,WACL,KAAK;AAEP,UAAM,aAAa,cACjB,KAAK,WACL,KAAK,WACL,KAAK;AAEP,UAAM,YAAY,cAChB,KAAK,WACL,KAAK,WACL,KAAK;AAEP,UAAM,aAAa,cACjB,KAAK,WACL,KAAK,WACL,KAAK;AAEP,UAAM,SAAS,cACb,KAAK,WACL,KAAK,WACL,KAAK;AAGP,QAAI,kBAAkB;AACtB,UAAM,UAAU,IAAI,gBAClB,KAAK,qBAAqB,KAAK,eAC/B;AAGF,aACM,aAAa,GACjB,cAAc,KAAK,oBACnB,EAAE,YACF;AACA,kBACE,OACA,KAAK,WACL,KAAK,WACL,KAAK,WACL;AAEF,kBACE,UACA,KAAK,WACL,KAAK,WACL,KAAK,WACL;AAEF,kBACE,YACA,KAAK,WACL,KAAK,WACL,KAAK,WACL;AAEF,kBACE,WACA,KAAK,WACL,KAAK,WACL,KAAK,WACL;AAEF,kBACE,YACA,KAAK,WACL,KAAK,WACL,KAAK,WACL;AAEF,kBACE,QACA,KAAK,WACL,KAAK,WACL,KAAK,WACL;AAGF,eACM,WAAW,GACf,YAAY,KAAK,eACjB,EAAE,UAAU,EAAE,iBACd;AACA,YAAI,QAAQ,aAAa,kBAAkB;AACzC,gBAAM;AAAA,YACJ,UAAU,QAAQ;AAAA;AAAA;AAItB,oBAAoB,MAAM,KAAK,WAAW;AAC1C,oBAAoB,SAAS,KAAK,WAAW;AAC7C,oBAAoB,WAAW,KAAK,WAAW;AAC/C,oBAAoB,UAAU,KAAK,WAAW;AAC9C,oBAAoB,WAAW,KAAK,WAAW;AAC/C,oBAAoB,OAAO,KAAK,WAAW;AAE3C,iBACM,aAAa,GACjB,cAAc,KAAK,eACnB,EAAE,YACF;AACA,cAAI,OAAO;AACX,cAAI,UAAU;AACd,cAAI,YAAY;AAChB,cAAI,WAAW;AACf,cAAI,YAAY;AAChB,cAAI,QAAQ;AAEZ,mBACM,YAAY,GAChB,aAAa,KAAK,eAClB,EAAE,WACF;AACA,oBAAQ,KAAK,SAAS,YAAY,UAAU,YAAY;AACxD,uBACE,KAAK,YAAY,YAAY,UAAU,YAAY;AACrD,yBACE,KAAK,cAAc,YAAY,UAAU,YAAY;AACvD,wBACE,KAAK,aAAa,YAAY,UAAU,YAAY;AACtD,yBACE,KAAK,cAAc,YAAY,UAAU,YAAY;AACvD,qBAAS,KAAK,SAAS,YAAY,UAAU,YAAY;AAEzD,iBAAK,cAAc;AACnB,oBAAQ,cAAc;AACtB,sBAAU,cAAc;AACxB,qBAAS,cAAc;AACvB,sBAAU,cAAc;AACxB,kBAAM,cAAc;AAEpB,kBAAM,UAAU,YAAY,aAC1B,MAAM,WAAW,GAAG,YAAY,aAAa,KAAK;AACpD,qBAAS,UAAU,YAAY,aAC7B,SAAS,WAAW,GAAG,YAAY,aACnC,QAAQ;AACV,uBAAW,UAAU,YAAY,aAC/B,WAAW,WAAW,GAAG,YAAY,aACrC,UAAU;AACZ,sBAAU,UAAU,YAAY,aAC9B,UAAU,WAAW,GAAG,YAAY,aACpC,SAAS;AACX,uBAAW,UAAU,YAAY,aAC/B,WAAW,WAAW,GAAG,YAAY,aACrC,UAAU;AACZ,mBAAO,UAAU,YAAY,aAC3B,OAAO,WAAW,GAAG,YAAY,aAAa,MAAM;AAEtD,iBAAK,SAAS,YAAY,UAAU,YAAY,aAC9C,KAAK,SAAS,aAAa,GAAG,UAAU,YAAY,aACpD,MAAM,UAAU,YAAY;AAC9B,iBAAK,YAAY,YAAY,UAAU,YAAY,aACjD,KAAK,YAAY,aAAa,GAAG,UAAU,YACzC,aACE,SAAS,UAAU,YAAY;AACrC,iBAAK,cAAc,YAAY,UAAU,YAAY,aACnD,KAAK,cAAc,aAAa,GAAG,UAAU,YAC3C,aACE,WAAW,UAAU,YAAY;AACvC,iBAAK,aAAa,YAAY,UAAU,YAAY,aAClD,KAAK,aAAa,aAAa,GAAG,UAAU,YAC1C,aACE,UAAU,UAAU,YAAY;AACtC,iBAAK,cAAc,YAAY,UAAU,YAAY,aACnD,KAAK,cAAc,aAAa,GAAG,UAAU,YAC3C,aACE,WAAW,UAAU,YAAY;AACvC,iBAAK,SAAS,YAAY,UAAU,YAAY,aAC9C,KAAK,SAAS,aAAa,GAAG,UAAU,YAAY,aACpD,OAAO,UAAU,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,SAU1B,aAAa,MAAmB,QAAwB;AACrE,WACE,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,KAAK,eAEP,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,KAAK,eAEP,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,KAAK,eAEP,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,KAAK,eAEP,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,KAAK,eAEP,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,KAAK,eAEP,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,KAAK,eAEP,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,KAAK,eAEN,QAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC/C,KAAK,eAEL,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,KAAK,eAEP,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,KAAK,eAEP,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,KAAK,eAEP,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,KAAK,eAEP,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,KAAK,eAEP,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,KAAK,eAEP,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,KAAK;AAAA;AAAA,SAQE,QAAQ,MAAmB,QAAwB;AAChE,WAAO,SAAQ,aAAa,MAAM,UAAU;AAAA;AAAA,SAM/B,KACb,MACA,WACA,UACA,QACA;AACA,QAAI;AACJ,YAAQ;AAAA,WACD,SAAQ;AACX,iBACE,OAAO,UAAU,KAAK,YAAY,KAAK,cACrC,KAAK,eAEP,OAAO,UAAU,KAAK,YAAY,KAAK,cACrC,KAAK,eAEP,OAAO,UAAU,KAAK,YAAY,KAAK,cACrC,KAAK,eAEP,OAAO,UAAU,KAAK,YAAY,KAAK,cACrC,KAAK,eAEN,QAAO,UAAU,KAAK,YAAY,KAAK,cACtC,KAAK,eAEL,OAAO,UAAU,KAAK,YAAY,KAAK,cACrC,KAAK,eAEP,OAAO,UAAU,KAAK,YAAY,KAAK,cACrC,KAAK,eAEP,OAAO,UAAU,KAAK,YAAY,KAAK,cACrC,KAAK;AAEX;AAAA,WAEG,SAAQ;AACX,iBACE,OAAO,KAAK,cAAc,UAAU,KAAK,cACvC,KAAK,eAEP,OAAO,KAAK,cAAc,UAAU,KAAK,cACvC,KAAK,eAEP,OAAO,KAAK,cAAc,UAAU,KAAK,cACvC,KAAK,eAEP,OAAO,KAAK,cAAc,UAAU,KAAK,cACvC,KAAK,eAEN,QAAO,KAAK,cAAc,UAAU,KAAK,cACxC,KAAK,eAEL,OAAO,KAAK,cAAc,UAAU,KAAK,cACvC,KAAK,eAEP,OAAO,KAAK,cAAc,UAAU,KAAK,cACvC,KAAK,eAEP,OAAO,KAAK,cAAc,UAAU,KAAK,cACvC,KAAK;AAEX;AAAA,WAEG,SAAQ;AACX,iBACE,OAAO,KAAK,cAAc,KAAK,YAAY,UACzC,KAAK,eAEP,OAAO,KAAK,cAAc,KAAK,YAAY,UACzC,KAAK,eAEP,OAAO,KAAK,cAAc,KAAK,YAAY,UACzC,KAAK,eAEP,OAAO,KAAK,cAAc,KAAK,YAAY,UACzC,KAAK,eAEN,QAAO,KAAK,cAAc,KAAK,YAAY,UAC1C,KAAK,eAEL,OAAO,KAAK,cAAc,KAAK,YAAY,UACzC,KAAK,eAEP,OAAO,KAAK,cAAc,KAAK,YAAY,UACzC,KAAK,eAEP,OAAO,KAAK,cAAc,KAAK,YAAY,UACzC,KAAK;AAEX;AAAA,WAEG,SAAQ;AACX,iBACE,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,YAEF,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,YAEF,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,YAEF,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,YAED,QAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC/C,YAEA,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,YAEF,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,YAEF,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C;AAEN;AAAA;AAEA,cAAM,IAAI,MAAM;AAAA;AAGpB,WAAO,SAAS;AAAA;AAAA,SAMH,QACb,MACA,WACA,QACA;AACA,YAAQ;AAAA,WACD,SAAQ;AACX,eACE,CAAC,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC/C,KAAK,eAEP,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,KAAK,eAEP,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,KAAK,eAEP,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,KAAK,eAEN,EAAC,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAChD,KAAK,eAEL,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,KAAK,eAEP,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,KAAK,eAEP,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,KAAK;AAAA,WAIR,SAAQ;AACX,eACE,CAAC,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC/C,KAAK,eAEP,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,KAAK,eAEP,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,KAAK,eAEP,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,KAAK,eAEN,EAAC,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAChD,KAAK,eAEL,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,KAAK,eAEP,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,KAAK,eAEP,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,KAAK;AAAA,WAIR,SAAQ;AACX,eACE,CAAC,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC/C,KAAK,eAEP,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,KAAK,eAEP,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,KAAK,eAEP,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,KAAK,eAEN,EAAC,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAChD,KAAK,eAEL,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,KAAK,eAEP,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,KAAK,eAEP,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,KAAK;AAAA,WAIR,SAAQ;AACX,eACE,CAAC,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC/C,KAAK,eAEP,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,KAAK,eAEP,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,KAAK,eAEP,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,KAAK,eAEN,EAAC,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAChD,KAAK,eAEL,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,KAAK,eAEP,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,KAAK,eAEP,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,cAC9C,KAAK;AAAA;AAMX,eAAO;AAAA;AAAA;AAAA,EAOL,mBAAmB,MAAmB;AAC5C,UAAM,YAAY,SAAQ,QAAQ,MAAM,KAAK;AAC7C,UAAM,cAAc,SAAQ,QAAQ,MAAM,KAAK;AAC/C,UAAM,aAAa,SAAQ,QAAQ,MAAM,KAAK;AAC9C,UAAM,cAAc,SAAQ,QAAQ,MAAM,KAAK;AAC/C,UAAM,eAAe,SAAQ,aAAa,MAAM,KAAK;AACrD,UAAM,eAAe,SAAQ,QAAQ,MAAM,KAAK;AAChD,UAAM,WACJ,YAAY,YACZ,cAAc,cACd,aAAa,aACb,cAAc;AAEhB,WAAO,eAAe,WAAW;AAAA;AAAA,EAM3B,UACN,MACA,WACA,OACA,MACA,UACA,YACA,WACA,YACA,aACA;AACA,UAAM,YAAY,SAAQ,QAAQ,MAAM,WAAW,KAAK,eAAe;AACvE,UAAM,cACJ,SAAQ,QAAQ,MAAM,WAAW,KAAK,iBAAiB;AACzD,UAAM,aAAa,SAAQ,QAAQ,MAAM,WAAW,KAAK,gBAAgB;AACzE,UAAM,cACJ,SAAQ,QAAQ,MAAM,WAAW,KAAK,iBAAiB;AACzD,UAAM,eAAe,SAAQ,QAAQ,MAAM,WAAW,KAAK,YAAY;AAEvE,QAAI,SAAS;AACb,QAAI,cAAc;AAElB,aAAS,WAAW,OAAO,WAAW,MAAM,EAAE,UAAU;AAEtD,UAAI,UACF,YAAY,SAAQ,KAAK,MAAM,WAAW,UAAU,KAAK;AAC3D,UAAI,YACF,cACA,SAAQ,KAAK,MAAM,WAAW,UAAU,KAAK;AAC/C,UAAI,WACF,aAAa,SAAQ,KAAK,MAAM,WAAW,UAAU,KAAK;AAC5D,UAAI,YACF,cACA,SAAQ,KAAK,MAAM,WAAW,UAAU,KAAK;AAC/C,UAAI,aACF,eAAe,SAAQ,KAAK,MAAM,WAAW,UAAU,KAAK;AAG9D,UAAI,eAAe,GAAG;AACpB,YAAI,eACF,UAAU,UACV,YAAY,YACZ,WAAW,WACX,YAAY;AACd,YAAI,OAAO,eAAe;AAE1B,kBAAU,WAAW;AACrB,oBAAY,aAAa;AACzB,mBAAW,YAAY;AACvB,oBAAY,aAAa;AACzB,qBAAa,cAAc;AAE3B,YAAI,eAAe,GAAG;AACpB,yBACE,UAAU,UACV,YAAY,YACZ,WAAW,WACX,YAAY;AACd,kBAAQ,eAAe;AAEvB,cAAI,OAAO,QAAQ;AACjB,qBAAS;AACT,0BAAc;AAAA;AAAA;AAAA;AAAA;AAMtB,WAAO,EAAE,KAAK,QAAQ,UAAU;AAAA;AAAA,EAI1B,KAAK,OAAoB,QAAqB;AACpD,QAAI;AAEJ,UAAM,WAAW,SAAQ,QAAQ,OAAO,KAAK;AAC7C,UAAM,aAAa,SAAQ,QAAQ,OAAO,KAAK;AAC/C,UAAM,YAAY,SAAQ,QAAQ,OAAO,KAAK;AAC9C,UAAM,aAAa,SAAQ,QAAQ,OAAO,KAAK;AAC/C,UAAM,cAAc,SAAQ,QAAQ,OAAO,KAAK;AAEhD,UAAM,MAAM,KAAK,UACf,OACA,SAAQ,MACR,MAAM,aAAa,GACnB,MAAM,YACN,UACA,YACA,WACA,YACA;AAEF,UAAM,QAAQ,KAAK,UACjB,OACA,SAAQ,QACR,MAAM,eAAe,GACrB,MAAM,cACN,UACA,YACA,WACA,YACA;AAEF,UAAM,OAAO,KAAK,UAChB,OACA,SAAQ,OACR,MAAM,cAAc,GACpB,MAAM,aACN,UACA,YACA,WACA,YACA;AAEF,UAAM,QAAQ,KAAK,UACjB,OACA,SAAQ,QACR,MAAM,eAAe,GACrB,MAAM,cACN,UACA,YACA,WACA,YACA;AAGF,QACE,MAAM,OAAO,IAAI,OACjB,MAAM,OAAO,MAAM,OACnB,MAAM,OAAO,KAAK,KAClB;AACA,kBAAY,SAAQ;AAGpB,UAAI,MAAM,WAAW;AAAG,eAAO;AAAA,eAE/B,IAAI,OAAO,MAAM,OACjB,IAAI,OAAO,MAAM,OACjB,IAAI,OAAO,KAAK,KAChB;AACA,kBAAY,SAAQ;AAAA,eAEpB,MAAM,OAAO,MAAM,OACnB,MAAM,OAAO,IAAI,OACjB,MAAM,OAAO,KAAK,KAClB;AACA,kBAAY,SAAQ;AAAA,WACf;AACL,kBAAY,SAAQ;AAAA;AAGtB,WAAO,aAAa,MAAM;AAC1B,WAAO,eAAe,MAAM;AAC5B,WAAO,cAAc,MAAM;AAC3B,WAAO,eAAe,MAAM;AAG5B,YAAQ;AAAA,WACD,SAAQ;AACX,eAAO,aAAa,MAAM,aAAa,IAAI;AAC3C,eAAO,eAAe,MAAM;AAC5B,eAAO,cAAc,MAAM;AAC3B,eAAO,eAAe,MAAM;AAC5B;AAAA,WAEG,SAAQ;AACX,eAAO,eAAe,MAAM,eAAe,MAAM;AACjD,eAAO,aAAa,MAAM;AAC1B,eAAO,cAAc,MAAM;AAC3B,eAAO,eAAe,MAAM;AAC5B;AAAA,WAEG,SAAQ;AACX,eAAO,cAAc,MAAM,cAAc,KAAK;AAC9C,eAAO,aAAa,MAAM;AAC1B,eAAO,eAAe,MAAM;AAC5B,eAAO,eAAe,MAAM;AAC5B;AAAA,WAEG,SAAQ;AACX,eAAO,eAAe,MAAM,eAAe,MAAM;AACjD,eAAO,cAAc,MAAM;AAC3B,eAAO,aAAa,MAAM;AAC1B,eAAO,eAAe,MAAM;AAC5B;AAAA;AAIJ,UAAM,SACH,OAAM,aAAa,MAAM,cACzB,OAAM,eAAe,MAAM,gBAC3B,OAAM,cAAc,MAAM,eAC1B,OAAM,eAAe,MAAM;AAC9B,WAAO,SACJ,QAAO,aAAa,OAAO,cAC3B,QAAO,eAAe,OAAO,gBAC7B,QAAO,cAAc,OAAO,eAC5B,QAAO,eAAe,OAAO;AAGhC,WAAO;AAAA;AAAA,EAGD,YAAY,QAAgB;AAClC,SAAK,UAAU;AAGf,SAAK,SAAS;AAGd,aAAS,YAAY,GAAG,YAAY,QAAQ,aAAa;AACvD,WAAK,OAAO,aAAa,IAAI;AAAA;AAI/B,SAAK,OAAO,GAAG,aAAa;AAC5B,SAAK,OAAO,GAAG,eAAe;AAC9B,SAAK,OAAO,GAAG,cAAc;AAC7B,SAAK,OAAO,GAAG,eAAe;AAG9B,SAAK,OAAO,GAAG,aAAa,KAAK;AACjC,SAAK,OAAO,GAAG,eAAe,KAAK;AACnC,SAAK,OAAO,GAAG,cAAc,KAAK;AAClC,SAAK,OAAO,GAAG,eAAe,KAAK;AAEnC,SAAK,WAAW,cACd,KAAK,gBACL,KAAK,WACL,KAAK,WACL,KAAK;AAEP,SAAK,cAAc,cACjB,KAAK,gBACL,KAAK,WACL,KAAK,WACL,KAAK;AAEP,SAAK,gBAAgB,cACnB,KAAK,gBACL,KAAK,WACL,KAAK,WACL,KAAK;AAEP,SAAK,eAAe,cAClB,KAAK,gBACL,KAAK,WACL,KAAK,WACL,KAAK;AAEP,SAAK,gBAAgB,cACnB,KAAK,gBACL,KAAK,WACL,KAAK,WACL,KAAK;AAEP,SAAK,WAAW,cACd,KAAK,gBACL,KAAK,WACL,KAAK,WACL,KAAK;AAGP,SAAK,SAAS;AACd,aAAS,aAAa,GAAG,aAAa,KAAK,EAAE,YAAY;AACvD,WAAK,OAAO,cAAc,aAAa;AAAA;AAGzC,SAAK,UAAU;AAAA;AAAA,EAGT,YAAY,4BAA4B,GAAG;AACjD,SAAK,6BAA6B;AAClC,SAAK,gBAAgB,KAAK,KAAK;AAC/B,SAAK,qBAAqB,KAAK;AAE/B,SAAK,YAAY,KAAK,gBAAgB;AACtC,SAAK,iBAAiB,KAAK,qBAAqB;AAAA;AAAA;AA9/B7C;AACmB,cADnB,SACmB,UAAS;AACT,cAFnB,SAEmB,QAAO;AACP,cAHnB,SAGmB,UAAS;AACT,cAJnB,SAImB,SAAQ;AZvGlC;;AaAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,AAWO,mCAAsC;AAAA,EAM3C,aAAa,gBAAgC,SAAkB;AAC7D,eAAW,SAAS,KAAK,SAAS,gBAAgB,UAAU;AAC1D,UAAI,MAAM,gBAAgB;AACxB,eAAO,MAAM;AAAA;AAAA;AAIjB,UAAM,IAAI,MAAM;AAAA;AAAA;;;ACxBpB,AAcO,iCAA2B,uBAAuB;AAAA,EAGvD,YAAY,yBAAqD;AAC/D;AAHM;AAIN,SAAK,YAAY;AAAA;AAAA,GAMlB,SACC,gBACA,SAC4C;AAC5C,UAAM,aAAa,eAAe;AAClC,UAAM,QAAQ,eAAe;AAC7B,UAAM,SAAS,eAAe;AAE9B,UAAM,UAAU,IAAI,gBAAgB,QAAQ;AAC5C,aAAS,KAAI,GAAG,KAAI,QAAQ,MAAK;AAC/B,UAAI,QAAQ,aAAa,KAAI;AAC3B,cAAM;AAAA,UACJ,UAAU,QAAQ;AAAA;AAAA;AAGtB,eAAS,KAAI,GAAG,MAAM,KAAI,OAAO,KAAI,OAAO,MAAK,OAAO;AAEtD,cAAM,QAAQ,WAAW;AAEzB,cAAM,KAAK,QAAQ,gBAAgB,KAAK,WAAW;AAAA;AAAA;AAIvD,UAAM;AAAA,MACJ;AAAA,MACA,UAAU;AAAA;AAAA;AAAA;;;AClDhB,AAiBO,IAAK,4BAAL,kBAAK,+BAAL;AACL,4EAAiB,KAAjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATU;AAAA;AAaL,wCAAkC,uBAAuB;AAAA,EAS9D,YACE,yBACA,QACA,aAAa,MACb,+BAA+B,GAC/B,yBAAyB,OACzB;AACA;AAfM;AACA;AACA;AAEA;AAEA;AAUN,SAAK,WAAW;AAEhB,SAAK,YAAY;AACjB,SAAK,oBAAoB;AACzB,SAAK,cAAc;AACnB,SAAK,0BAA0B;AAAA;AAAA,GAQhC,SACC,gBACA,SAC4C;AAC5C,UAAM,aAAa,eAAe;AAClC,UAAM,gBAAgB,IAAI;AAC1B,UAAM,QAAQ,eAAe;AAC7B,UAAM,SAAS,eAAe;AAC9B,UAAM,aAA2B;AAEjC,QAAI,MAAM;AACV,QAAI,gBAAgB;AAGpB,eAAW,UAAU,KAAK,SAAS;AACjC,YAAM,mBAAmB,OAAO,KAAK;AACrC,UAAI,gBAAgB;AAAkB,wBAAgB;AAAA;AAExD,aAAS,IAAI,GAAG,IAAI,eAAe,KAAK;AACtC,WAAK,eAAgB,WAAW,KAAK,IAAK;AAAA;AAG5C,UAAM,UAAU,IAAI,gBAAgB,QAAQ;AAC5C,aAAS,KAAI,GAAG,KAAI,QAAQ,MAAK;AAC/B,UAAI,QAAQ,aAAa,KAAI;AAC3B,cAAM;AAAA,UACJ,UAAU,QAAQ;AAAA;AAAA;AAKtB,UAAI,KAAK;AAAa,eAAO;AAE7B,YAAM,MAAM,KAAI;AAChB,YAAM,SAAS,QAAQ,IAAI,IAAI,QAAQ;AACvC,YAAM,OAAO,QAAQ,IAAI,QAAQ;AAGjC,WAAK,eAAe,WAAW,IAAI;AAEnC,iBAAW,KAAK,WAAW;AAE3B,YAAM,YAAY,WAAW;AAC7B,eACM,KAAI,QAAQ,MAAM,MAAM,QAC5B,OAAM,MACN,MAAK,KAAK,OAAO,KACjB;AAEA,cAAM,QAAQ,WAAW;AAEzB,cAAM,QAAQ,UAAU;AAExB,sBAAc,KAAK;AAEnB,cAAM,iBAAiB,MAAM,aAC3B,qBAAqB,MAAM,IAAI,MAAM,KACrC,qBAAqB,MAAM,IAAI,MAAM,KACrC,qBAAqB,MAAM,IAAI,MAAM,KACrC,qBAAqB,MAAM,IAAI,MAAM;AAIvC,cAAM,eAAe,QAAQ,gBAC3B,KAAK,WACL;AAEF,cAAM,KAAK;AAGX,YAAI,KAAK,mBAAmB;AAC1B,gBAAM,OAAO,KAAK,UAAU,oBAC1B,eACA;AAEF,cAAI,OAAO,KAAK;AAAmB;AAAA;AAIrC,YAAI;AACJ,YAAI;AACJ,YAAI;AACJ,YAAI;AACJ,YAAI,KAAK,yBAAyB;AAChC,eAAK,eAAe,IAAI,aAAa;AACrC,eAAK,eAAe,IAAI,aAAa;AACrC,eAAK,eAAe,IAAI,aAAa;AACrC,eAAK,eAAe,IAAI,aAAa;AAAA,eAChC;AACL,eAAK,cAAc,IAAI,aAAa;AACpC,eAAK,cAAc,IAAI,aAAa;AACpC,eAAK,cAAc,IAAI,aAAa;AACpC,eAAK,cAAc,IAAI,aAAa;AAAA;AAGtC,cAAM,SAAS,QAAQ,IAAI,IAAI,KAAK,QAAQ,SAAS;AACrD,cAAM,OAAO,QAAQ,IAAI,KAAK,QAAQ,SAAS;AAE/C,iBAAS,IAAI,QAAQ,MAAM,MAAM,KAAK,KAAK;AACzC,gBAAM,KAAK,KAAK,QAAQ,GAAG,KAAK;AAChC,gBAAM,KAAK,KAAK,QAAQ,GAAG;AAE3B,cAAI,KAAK,MAAK,KAAK,KAAK,KAAI,SAAS,KAAK,MAAK,KAAK,KAAK,KAAI,QAAQ;AACnE,kBAAM,IAAI,KAAK,QAAQ,GAAG;AAC1B,kBAAM,IAAI,WAAW,IAAI,KAAK;AAE9B,cAAE,MAAM,KAAK;AACb,cAAE,MAAM,KAAK;AACb,cAAE,MAAM,KAAK;AACb,cAAE,MAAM,KAAK;AAAA;AAAA;AAAA;AAAA;AAMrB,UAAM;AAAA,MACJ;AAAA,MACA,UAAU;AAAA;AAAA;AAAA,EAIN,eAAe,WAAuB,OAAe;AAE3D,QAAI,UAAU,SAAS,OAAO;AAC5B,gBAAU,SAAS;AAAA;AAIrB,UAAM,IAAI,UAAU;AACpB,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,YAAM,QAAQ,UAAU;AACxB,YAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK;AAAA;AAI9C,aAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,gBAAU,KAAK,CAAC,GAAK,GAAK,GAAK;AAAA;AAAA;AAAA,EAI3B,WAAW,QAAmC;AACpD,YAAQ;AAAA,WACD;AACH,aAAK,UAAU;AAAA,UACb,CAAC,IAAI,IAAI,GAAG;AAAA,UACZ,CAAC,IAAI,IAAI,IAAI;AAAA,UACb,CAAC,IAAI,IAAI,GAAG;AAAA,UACZ,CAAC,IAAI,IAAI,GAAG;AAAA;AAEd;AAAA,WAEG;AACH,aAAK,UAAU;AAAA,UACb,CAAC,IAAI,GAAG,GAAG;AAAA,UACX,CAAC,IAAI,GAAG,GAAG;AAAA,UACX,CAAC,IAAI,GAAG,GAAG;AAAA;AAEb;AAAA,WAEG;AACH,aAAK,UAAU;AAAA,UACb,CAAC,IAAI,IAAI,GAAG;AAAA,UACZ,CAAC,IAAI,IAAI,GAAG;AAAA,UACZ,CAAC,IAAI,IAAI,IAAI;AAAA,UACb,CAAC,IAAI,IAAI,IAAI;AAAA,UACb,CAAC,IAAI,IAAI,GAAG;AAAA,UACZ,CAAC,IAAI,IAAI,GAAG;AAAA,UACZ,CAAC,IAAI,IAAI,GAAG;AAAA,UACZ,CAAC,IAAI,IAAI,IAAI;AAAA,UACb,CAAC,IAAI,IAAI,IAAI;AAAA,UACb,CAAC,IAAI,IAAI,GAAG;AAAA,UACZ,CAAC,IAAI,IAAI,GAAG;AAAA,UACZ,CAAC,IAAI,IAAI,GAAG;AAAA;AAEd;AAAA,WAEG;AACH,aAAK,UAAU;AAAA,UACb,CAAC,IAAI,GAAG,GAAG;AAAA,UACX,CAAC,IAAI,GAAG,GAAG;AAAA,UACX,CAAC,IAAI,GAAG,IAAI;AAAA,UACZ,CAAC,IAAI,GAAG,GAAG;AAAA,UACX,CAAC,IAAI,GAAG,GAAG;AAAA,UACX,CAAC,IAAI,GAAG,GAAG;AAAA;AAEb;AAAA,WAEG;AACH,aAAK,UAAU;AAAA,UAEb,CAAC,IAAI,IAAI,GAAG;AAAA,UACZ,CAAC,IAAI,IAAI,GAAG;AAAA,UACZ,CAAC,IAAI,IAAI,IAAI;AAAA,UACb,CAAC,IAAI,IAAI,IAAI;AAAA,UACb,CAAC,IAAI,IAAI,GAAG;AAAA,UACZ,CAAC,IAAI,IAAI,GAAG;AAAA,UACZ,CAAC,IAAI,IAAI,GAAG;AAAA,UACZ,CAAC,IAAI,IAAI,IAAI;AAAA,UACb,CAAC,IAAI,IAAI,IAAI;AAAA,UACb,CAAC,IAAI,IAAI,GAAG;AAAA,UACZ,CAAC,IAAI,IAAI,GAAG;AAAA,UACZ,CAAC,IAAI,IAAI,GAAG;AAAA;AAEd;AAAA,WAEG;AACH,aAAK,UAAU;AAAA,UACb,CAAC,IAAI,IAAI,GAAG;AAAA,UACZ,CAAC,IAAI,IAAI,GAAG;AAAA,UACZ,CAAC,IAAI,IAAI,IAAI;AAAA,UACb,CAAC,IAAI,IAAI,IAAI;AAAA,UACb,CAAC,IAAI,IAAI,GAAG;AAAA,UACZ,CAAC,IAAI,IAAI,GAAG;AAAA,UACZ,CAAC,IAAI,IAAI,GAAG;AAAA;AAEd;AAAA,WAEG;AACH,aAAK,UAAU;AAAA,UACb,CAAC,IAAI,IAAI,GAAG;AAAA,UACZ,CAAC,IAAI,IAAI,GAAG;AAAA,UACZ,CAAC,IAAI,IAAI,IAAI;AAAA,UACb,CAAC,IAAI,IAAI,IAAI;AAAA,UACb,CAAC,IAAI,IAAI,GAAG;AAAA,UACZ,CAAC,IAAI,IAAI,GAAG;AAAA,UACZ,CAAC,IAAI,IAAI,GAAG;AAAA,UACZ,CAAC,IAAI,IAAI,IAAI;AAAA,UACb,CAAC,IAAI,IAAI,GAAG;AAAA,UACZ,CAAC,IAAI,IAAI,GAAG;AAAA;AAEd;AAAA,WAEG;AACH,aAAK,UAAU;AAAA,UACb,CAAC,IAAI,IAAI,GAAG;AAAA,UACZ,CAAC,IAAI,IAAI,GAAG;AAAA,UACZ,CAAC,IAAI,IAAI,IAAI;AAAA,UACb,CAAC,IAAI,IAAI,IAAI;AAAA,UACb,CAAC,IAAI,IAAI,GAAG;AAAA,UACZ,CAAC,IAAI,IAAI,GAAG;AAAA,UACZ,CAAC,IAAI,IAAI,GAAG;AAAA;AAEd;AAAA,WAEG;AACH,aAAK,UAAU;AAAA,UACb,CAAC,IAAI,GAAG,GAAG;AAAA,UACX,CAAC,IAAI,GAAG,IAAI;AAAA,UACZ,CAAC,IAAI,GAAG,GAAG;AAAA;AAEb;AAAA;AAGA,cAAM,IAAI,MAAM,yCAAyC;AAAA;AAAA;AAAA;;;ACnS1D,uBACL,OACA,QACA,UACA;AACA,QAAM,WAAW,KAAK,IAAI,OAAO;AACjC,QAAM,QAAQ,KAAK,MAAM,KAAK,IAAI,YAAY,KAAK,IAAI,KAAK;AAC5D,QAAM,UAAU,IAAI,gBAAgB,QAAQ,QAAQ;AACpD,QAAM,OAAO;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO;AAAA,IACP,GAAG;AAAA,IACH,GAAG;AAAA;AAEL,SAAO,YAAY,MAAM;AACzB,QAAM,MAAM;AAAA;AAGd,sBACE,MACA,WAC4C;AAC5C,MAAI,KAAK,QAAQ;AAAG;AAEpB,MAAI,KAAK,QAAQ,aAAa,KAAK,QAAQ;AACzC,UAAM,EAAE,UAAU,KAAK,QAAQ;AAAA;AAEjC,OAAK;AACL,UAAQ;AAAA,SACD;AACH,aAAO,YAAY,MAAM;AACzB,YAAM,MAAM;AACZ,aAAO,YAAY,MAAM;AACzB,YAAM,MAAM;AACZ,aAAO,YAAY,MAAM;AACzB,YAAM,MAAM;AACZ,aAAO,YAAY,MAAM;AACzB;AAAA,SAEG;AACH,aAAO,YAAY,MAAM;AACzB,YAAM,MAAM;AACZ,aAAO,YAAY,MAAM;AACzB,YAAM,MAAM;AACZ,aAAO,YAAY,MAAM;AACzB,YAAM,MAAM;AACZ,aAAO,YAAY,MAAM;AACzB;AAAA,SAEG;AACH,aAAO,YAAY,MAAM;AACzB,YAAM,MAAM;AACZ,aAAO,YAAY,MAAM;AACzB,YAAM,MAAM;AACZ,aAAO,YAAY,MAAM;AACzB,YAAM,MAAM;AACZ,aAAO,YAAY,MAAM;AACzB;AAAA,SAEG;AACH,aAAO,YAAY,MAAM;AACzB,YAAM,MAAM;AACZ,aAAO,YAAY,MAAM;AACzB,YAAM,MAAM;AACZ,aAAO,YAAY,MAAM;AACzB,YAAM,MAAM;AACZ,aAAO,YAAY,MAAM;AACzB;AAAA;AAGA;AAAA;AAEJ,OAAK;AAAA;AAGP,eAAe,MAAY,WAAsB;AAC/C,MACE,KAAK,KAAK,KACV,KAAK,IAAI,KAAK,SACd,KAAK,KAAK,KACV,KAAK,IAAI,KAAK,QACd;AACA,SAAK,SAAS,KAAK,GAAG,KAAK;AAC3B,SAAK;AAAA;AAEP,UAAQ;AAAA,SACD;AACH,WAAK;AACL;AAAA,SACG;AACH,WAAK;AACL;AAAA,SACG;AACH,WAAK;AACL;AAAA,SACG;AACH,WAAK;AACL;AAAA;AAAA;;;AC3HN,AAkCO,4CAAsC,uBAAuB;AAAA,EAKlE,YACE,yBACA,iBAAiB,IACjB,mBAAmB,GACnB;AACA;AATM;AACA;AACA;AAQN,SAAK,YAAY;AACjB,SAAK,kBAAkB;AACvB,SAAK,WAAW,wBAAwB,eACtC,kBACA;AAAA;AAAA,GAOH,SAAS,gBAAgC,SAAkB;AAC1D,UAAM,aAAa,eAAe;AAClC,UAAM,QAAQ,eAAe;AAC7B,UAAM,SAAS,eAAe;AAC9B,UAAM,aAKD;AAEL,QAAI,OAAO;AAEX,aAAS,IAAI,GAAG,IAAI,KAAK,iBAAiB,KAAK;AAC7C,iBAAW,KAAK,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAAA;AAGzC,WAAO,aAAa,OAAO,QAAQ,CAAC,IAAG,OAAM;AAC3C,YAAM,IAAI,WAAW,KAAI,KAAI;AAC7B,UAAI,EAAE,GAAG,GAAG,GAAG,MAAM;AACrB,eAAS,IAAI,GAAG,IAAI,KAAK,iBAAiB,KAAK;AAC7C,cAAM,SAAS,KAAK,SAAS;AAC7B,cAAM,IAAI,WAAY,KAAI,QAAQ,KAAK;AAEvC,aAAK,EAAE,IAAI;AACX,aAAK,EAAE,IAAI;AACX,aAAK,EAAE,IAAI;AACX,aAAK,EAAE,IAAI;AAAA;AAGb,YAAM,iBAAiB,MAAM,aAC3B,qBAAqB,IACrB,qBAAqB,IACrB,qBAAqB,IACrB,qBAAqB;AAGvB,YAAM,iBAAiB,QAAQ,gBAC7B,KAAK,WACL;AAIF,aAAQ,QAAO,KAAK,KAAK;AACzB,YAAM,OAAQ,QAAO,KAAK,kBAAkB,KAAK,KAAK;AAGtD,iBAAW,MAAM,IAAI,EAAE,IAAI,eAAe;AAC1C,iBAAW,MAAM,IAAI,EAAE,IAAI,eAAe;AAC1C,iBAAW,MAAM,IAAI,EAAE,IAAI,eAAe;AAC1C,iBAAW,MAAM,IAAI,EAAE,IAAI,eAAe;AAG1C,QAAE,KAAK;AAAA;AAGT,UAAM;AAAA,MACJ;AAAA,MACA,UAAU;AAAA;AAAA;AAAA,SAIC,eACb,kBACA,gBACA;AACA,UAAM,UAAU;AAEhB,UAAM,aAAa,KAAK,IACtB,KAAK,IAAI,kBAAmB,kBAAiB;AAE/C,aAAS,IAAI,GAAG,OAAO,GAAG,IAAI,gBAAgB,KAAK;AACjD,cAAQ,KAAQ,QAAO,MAAO,KAAK,iBAAkB;AACrD,cAAQ;AAAA;AAGV,WAAO;AAAA;AAAA;ALpIX;;AMAA;AAAA;AAAA;AAAA;;;ACAA,AAYA,IAAM,KAAK;AACX,IAAM,KAAK;AAEJ,cAAc,QAAwB,QAAwB;AACnE,MACE,OAAO,gBAAgB,OAAO,eAC9B,OAAO,eAAe,OAAO,YAC7B;AACA,UAAM,IAAI,MAAM;AAAA;AAGlB,QAAM,mBAAmB;AACzB,QAAM,IAAK,MAAK,oBAAoB;AACpC,QAAM,KAAM,MAAK,MAAM;AACvB,QAAM,KAAM,MAAK,MAAM;AAEvB,MAAI,aAAa;AACjB,MAAI,QAAQ;AAGZ,UACE,QACA,QACA,CAAC,aAAa,aAAa,mBAAmB,sBAAsB;AAElE,QAAI,QAAQ;AACZ,QAAI,SAAS;AACb,QAAI,SAAS;AAEb,aAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,gBAAW,aAAY,KAAK,sBAAsB;AAClD,gBAAW,aAAY,KAAK,sBAAsB;AAElD,eACG,aAAY,KAAK,qBACjB,aAAY,KAAK;AAAA;AAGtB,UAAM,iBAAiB,YAAY,SAAS;AAC5C,cAAU;AACV,cAAU;AACV,aAAS;AAGT,UAAM,YACH,KAAI,oBAAoB,oBAAoB,MAAO,KAAI,QAAQ;AAClE,UAAM,cACH,sBAAqB,IAAI,qBAAqB,IAAI,MAClD,UAAS,SAAS;AACrB,UAAM,QAAO,YAAY;AAEzB,aAAS;AACT;AAAA;AAGJ,SAAO,QAAQ;AAAA;AAGjB,iBACE,QACA,QACA,UAMA;AACA,QAAM,aAAa;AACnB,QAAM,QAAQ,OAAO;AACrB,QAAM,SAAS,OAAO;AAEtB,WAAS,KAAI,GAAG,KAAI,QAAQ,MAAK,YAAY;AAC3C,aAAS,KAAI,GAAG,KAAI,OAAO,MAAK,YAAY;AAE1C,YAAM,cAAc,KAAK,IAAI,YAAY,QAAQ;AACjD,YAAM,eAAe,KAAK,IAAI,YAAY,SAAS;AAEnD,YAAM,cAAc,6BAClB,QACA,IACA,IACA,aACA;AAEF,YAAM,cAAc,6BAClB,QACA,IACA,IACA,aACA;AAEF,YAAM,eAAe,qBAAqB;AAC1C,YAAM,eAAe,qBAAqB;AAE1C,eAAS,aAAa,aAAa,cAAc;AAAA;AAAA;AAAA;AAKvD,sCACE,OACA,IACA,IACA,OACA,QACA;AACA,QAAM,aAAa,MAAM;AACzB,QAAM,aAAa;AAEnB,MAAI,UAAU;AAEd,WAAS,IAAI,IAAG,IAAI,KAAI,QAAQ,KAAK;AACnC,UAAM,SAAS,IAAI,MAAM;AACzB,aAAS,IAAI,IAAG,IAAI,KAAI,OAAO,KAAK;AAClC,YAAM,QAAQ,WAAW,SAAS;AAClC,iBAAW,WACT,MAAM,IAAI,mBAAQ,MAAM,IAAI,qBAAU,MAAM,IAAI;AAClD;AAAA;AAAA;AAIJ,SAAO;AAAA;AAGT,8BAA8B,YAAsB;AAClD,MAAI,UAAU;AACd,aAAW,QAAQ,YAAY;AAC7B,eAAW;AAAA;AAGb,SAAO,UAAU,WAAW;AAAA;AD/I9B;;AEAA,AAcA,IAAM,mBACJ,OAAO,iBAAiB,aACpB,eACA,OAAO,YAAY,eAAe,OAAO,oCAAS,cAAa,aAC/D,CAAC,aAAyB,QAAQ,SAAS,YAC3C,CAAC,aAAyB,WAAW,UAAU;AAiD9C,0BACL,QACA;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,IACuB,IACzB;AACA,QAAM,qBACJ,oCAAoC;AACtC,QAAM,mBAAmB,sCACvB,oBACA,qBACA;AAEF,SAAO,QAAQ,CAAC,UAAU,iBAAiB,OAAO;AAClD,SAAO,iBAAiB;AAAA;AAG1B,4BACE,QACA;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,IACyC,IAC3C;AACA,SAAO,IAAI,QAAiB,CAAC,SAAS,WAAW;AAC/C,UAAM,qBACJ,oCAAoC;AACtC,UAAM,mBAAmB,sCACvB,oBACA,qBACA;AAEF,WAAO,QAAQ,CAAC,UAAU,iBAAiB,OAAO;AAElD,QAAI;AACJ,UAAM,WAAW,iBAAiB;AAClC,UAAM,OAAO,MAAM;AACjB,UAAI;AACF,cAAM,SAAS,SAAS;AACxB,YAAI,OAAO,MAAM;AACf,kBAAQ;AAAA,eACH;AACL,cAAI,OAAO,MAAM;AAAS,sBAAU,OAAO,MAAM;AACjD,cAAI;AAAY,uBAAW,OAAO,MAAM;AACxC,2BAAiB;AAAA;AAAA,eAEZ,OAAP;AACA,eAAO;AAAA;AAAA;AAGX,qBAAiB;AAAA;AAAA;AAId,0BACL,OACA,SACA,EAAE,sBAAsB,sBAA2C,IACnE;AACA,QAAM,qBACJ,oCAAoC;AACtC,QAAM,iBAAiB,kCACrB,oBACA;AAEF,SAAO,eAAe,aAAa,OAAO;AAAA;AAG5C,4BACE,OACA,SACA;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,IACyC,IAC3C;AACA,SAAO,IAAI,QAAwB,CAAC,SAAS,WAAW;AACtD,UAAM,qBACJ,oCAAoC;AACtC,UAAM,iBAAiB,kCACrB,oBACA;AAGF,QAAI;AACJ,UAAM,WAAW,eAAe,SAAS,OAAO;AAChD,UAAM,OAAO,MAAM;AACjB,UAAI;AACF,cAAM,SAAS,SAAS;AACxB,YAAI,OAAO,MAAM;AACf,kBAAQ;AAAA,eACH;AACL,cAAI,OAAO,MAAM,gBAAgB;AAC/B,gCAAoB,OAAO,MAAM;AAAA;AAEnC,cAAI;AAAY,uBAAW,OAAO,MAAM;AACxC,2BAAiB;AAAA;AAAA,eAEZ,OAAP;AACA,eAAO;AAAA;AAAA;AAGX,qBAAiB;AAAA;AAAA;AAIrB,6CACE,uBAA6C,mBAC7C;AACA,UAAQ;AAAA,SACD;AACH,aAAO,IAAa;AAAA,SACjB;AACH,aAAO,IAAa;AAAA,SACjB;AACH,aAAO,IAAa;AAAA,SACjB;AACH,aAAO,IAAa;AAAA,SACjB;AACH,aAAO,IAAa;AAAA,SACjB;AACH,aAAO,IAAa;AAAA,SACjB;AACH,aAAO,IAAa;AAAA,SACjB;AACH,aAAO,IAAa;AAAA,SACjB;AACH,aAAO,IAAa;AAAA,SACjB;AACH,aAAO,IAAa;AAAA,SACjB;AACH,aAAO,IAAa;AAAA;AAEpB,YAAM,IAAI,MAAM,gCAAgC;AAAA;AAAA;AAItD,2CACE,oBACA,oBAAuC,mBACvC;AACA,UAAQ;AAAA,SACD;AACH,aAAO,IAAU,aAAa;AAAA,SAC3B;AACH,aAAO,IAAU,wBAAwB;AAAA,SACtC;AACH,aAAO,IAAU,oBACf,oBACA;AAAA,SAEC;AACH,aAAO,IAAU,oBACf,oBACA;AAAA,SAEC;AACH,aAAO,IAAU,oBACf,oBACA;AAAA,SAEC;AACH,aAAO,IAAU,oBACf,oBACA;AAAA,SAEC;AACH,aAAO,IAAU,oBACf,oBACA;AAAA,SAEC;AACH,aAAO,IAAU,oBACf,oBACA;AAAA,SAEC;AACH,aAAO,IAAU,oBACf,oBACA;AAAA,SAEC;AACH,aAAO,IAAU,oBACf,oBACA;AAAA,SAEC;AACH,aAAO,IAAU,oBACf,oBACA;AAAA;AAGF,YAAM,IAAI,MAAM,6BAA6B;AAAA;AAAA;AAInD,+CACE,oBACA,sBAA2C,WAC3C,SAAS,KACT;AACA,UAAQ;AAAA,SACD;AACH,aAAO,IAAY,SAAS,oBAAoB;AAAA,SAC7C;AACH,aAAO,IAAY,SAAS,oBAAoB;AAAA,SAC7C;AACH,aAAO,IAAY,QAAQ,oBAAoB;AAAA,SAC5C;AACH,aAAO,IAAY,cAAc,oBAAoB;AAAA;AAErD,YAAM,IAAI,MAAM,+BAA+B;AAAA;AAAA;AC5RrD", "names": []}