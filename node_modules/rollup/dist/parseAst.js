/*
  @license
	Rollup.js v4.50.1
	Sun, 07 Sep 2025 10:51:49 GMT - commit 79d5563ab4787f9425a5fa317bad0d6ae4be480b

	https://github.com/rollup/rollup

	Released under the MIT License.
*/
'use strict';

Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });

require('./native.js');
const parseAst_js = require('./shared/parseAst.js');
require('node:path');



exports.parseAst = parseAst_js.parseAst;
exports.parseAstAsync = parseAst_js.parseAstAsync;
//# sourceMappingURL=parseAst.js.map
