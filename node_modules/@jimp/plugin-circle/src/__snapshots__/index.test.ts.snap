// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Circle > makes a circle based on image height and width 1`] = `
Visualization:

▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾
▾▾▾▾▾▾▾▾8A▦A8▾▾▾▾▾▾▾
▾▾▾▾▾▾▾E▦▦▦▦▦E▾▾▾▾▾▾
▾▾▾▾▾▾8▦▦▦▦▦▦▦8▾▾▾▾▾
▾▾▾▾▾▾A▦▦▦▦▦▦▦A▾▾▾▾▾
▾▾▾▾▾▾▦▦▦▦▦▦▦▦▦▾▾▾▾▾
▾▾▾▾▾▾A▦▦▦▦▦▦▦A▾▾▾▾▾
▾▾▾▾▾▾8▦▦▦▦▦▦▦8▾▾▾▾▾
▾▾▾▾▾▾▾E▦▦▦▦▦E▾▾▾▾▾▾
▾▾▾▾▾▾▾▾8A▦A8▾▾▾▾▾▾▾

Data:

80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰
80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁸⁶ 80-80-80ᵈᶠ 80-80-80ᶠᶠ 80-80-80ᵈᶠ 80-80-80⁸⁶ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰
80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80ᶜ¹ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶜ¹ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰
80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁸⁶ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80⁸⁶ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰
80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80ᵈᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᵈᶠ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰
80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰
80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80ᵈᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᵈᶠ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰
80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁸⁶ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80⁸⁶ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰
80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80ᶜ¹ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶜ¹ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰
80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁸⁶ 80-80-80ᵈᶠ 80-80-80ᶠᶠ 80-80-80ᵈᶠ 80-80-80⁸⁶ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰
`;

exports[`Circle > makes a circle using provided radius 1`] = `
Visualization:

▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾
▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾
▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾
▾▾▾▾▾▾▾▾ F▦F ▾▾▾▾▾▾▾
▾▾▾▾▾▾▾▾F▦▦▦F▾▾▾▾▾▾▾
▾▾▾▾▾▾▾▾▦▦▦▦▦▾▾▾▾▾▾▾
▾▾▾▾▾▾▾▾F▦▦▦F▾▾▾▾▾▾▾
▾▾▾▾▾▾▾▾ F▦F ▾▾▾▾▾▾▾
▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾
▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾

Data:

80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰
80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰
80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰
80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80²ᵇ 80-80-80ᶜ² 80-80-80ᶠᶠ 80-80-80ᶜ² 80-80-80²ᵇ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰
80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80ᶜ² 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶜ² 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰
80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰
80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80ᶜ² 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶜ² 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰
80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80²ᵇ 80-80-80ᶜ² 80-80-80ᶠᶠ 80-80-80ᶜ² 80-80-80²ᵇ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰
80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰
80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰
`;

exports[`Circle > should  1`] = `
Visualization:

▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾▾
▾▾▾8A▦A8▾▾▾▾▾▾▾▾▾▾▾▾
▾▾E▦▦▦▦▦E▾▾▾▾▾▾▾▾▾▾▾
▾8▦▦▦▦▦▦▦8▾▾▾▾▾▾▾▾▾▾
▾A▦▦▦▦▦▦▦A▾▾▾▾▾▾▾▾▾▾
▾▦▦▦▦▦▦▦▦▦▾▾▾▾▾▾▾▾▾▾
▾A▦▦▦▦▦▦▦A▾▾▾▾▾▾▾▾▾▾
▾8▦▦▦▦▦▦▦8▾▾▾▾▾▾▾▾▾▾
▾▾E▦▦▦▦▦E▾▾▾▾▾▾▾▾▾▾▾
▾▾▾8A▦A8▾▾▾▾▾▾▾▾▾▾▾▾

Data:

80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰
80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁸⁶ 80-80-80ᵈᶠ 80-80-80ᶠᶠ 80-80-80ᵈᶠ 80-80-80⁸⁶ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰
80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80ᶜ¹ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶜ¹ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰
80-80-80⁰⁰ 80-80-80⁸⁶ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80⁸⁶ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰
80-80-80⁰⁰ 80-80-80ᵈᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᵈᶠ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰
80-80-80⁰⁰ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰
80-80-80⁰⁰ 80-80-80ᵈᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᵈᶠ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰
80-80-80⁰⁰ 80-80-80⁸⁶ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80⁸⁶ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰
80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80ᶜ¹ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶜ¹ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰
80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁸⁶ 80-80-80ᵈᶠ 80-80-80ᶠᶠ 80-80-80ᵈᶠ 80-80-80⁸⁶ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰ 80-80-80⁰⁰
`;
