

> @jimp/plugin-crop@1.1.2 test:browser /Users/<USER>/Documents/jimp/plugins/plugin-crop
> vitest --config vitest.config.browser.mjs "--watch=false"

Port 5173 is in use, trying another one...
Port 5174 is in use, trying another one...
Port 5175 is in use, trying another one...

[7m[1m[36m RUN [39m[22m[27m [36mv2.0.5[39m [90m/Users/<USER>/Documents/jimp/plugins/plugin-crop[39m
[2m[32m      Browser runner started at http://localhost:5176/[39m[22m

[2m1:39:24 AM[22m [36m[1m[vite][22m[39m [32m✨ new dependencies optimized: [33mvite-plugin-node-polyfills/shims/buffer, vite-plugin-node-polyfills/shims/global, vite-plugin-node-polyfills/shims/process, path[32m[39m
[2m1:39:24 AM[22m [36m[1m[vite][22m[39m [32m✨ optimized dependencies changed. reloading[39m
[?25l [90m·[39m [2msrc/[22mautocrop[2m.test.ts[22m[2m (15)[22m
 [90m·[39m [2msrc/[22mcrop[2m.test.ts[22m[2m (6)[22m
[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mautocrop[2m.test.ts[22m[2m (15)[22m
 [32m✓[39m [2msrc/[22mcrop[2m.test.ts[22m[2m (6)[22m
[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mautocrop[2m.test.ts[22m[2m (15)[22m
 [32m✓[39m [2msrc/[22mcrop[2m.test.ts[22m[2m (6)[22m

[2m Test Files [22m [1m[32m2 passed[39m[22m[90m (2)[39m
[2m      Tests [22m [1m[32m21 passed[39m[22m[90m (21)[39m
[2m   Start at [22m 01:39:14
[2m   Duration [22m 12.94s[2m (transform 0ms, setup 0ms, collect 1.07s, tests 11ms, environment 0ms, prepare 4.31s)[22m

[?25h[?25h
