
> @jimp/plugin-hash@1.1.1 test:browser /Users/<USER>/Documents/jimp/plugins/plugin-hash
> vitest --config vitest.config.browser.mjs "--watch=false" "--u"


[7m[1m[36m RUN [39m[22m[27m [36mv1.4.0[39m [90m/Users/<USER>/Documents/jimp/plugins/plugin-hash[39m
[2m[32m      Browser runner started at http://localhost:5173/[39m[22m

[?25l [90m·[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [90m·[39m hash[2m (6)[22m
     [90m·[39m base 2
     [90m·[39m base 10 (decimal)
     [90m·[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [33m⠙[39m base 10 (decimal)
     [90m·[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [33m⠹[39m base 10 (decimal)
     [90m·[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [33m⠸[39m base 10 (decimal)
     [90m·[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [33m⠼[39m base 10 (decimal)
     [90m·[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [33m⠴[39m base 10 (decimal)
     [90m·[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [33m⠦[39m base 10 (decimal)
     [90m·[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [33m⠧[39m base 10 (decimal)
     [90m·[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [33m⠇[39m base 10 (decimal)
     [90m·[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠙[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠹[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠸[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠼[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠴[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠦[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠧[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠇[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠏[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠋[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠙[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠹[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠸[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠼[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠴[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠦[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠧[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠇[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠏[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠋[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠙[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠹[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠸[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠼[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠴[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠦[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠧[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠇[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠏[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠋[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠙[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠹[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠸[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠼[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠴[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠦[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠧[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠇[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠏[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠋[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠙[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠹[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠸[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠼[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠴[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠦[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠧[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠇[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠏[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠋[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠙[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠹[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠸[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠼[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠴[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠦[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠧[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠇[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠏[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠋[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠙[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠹[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠸[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠼[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠴[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠦[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [33m⠧[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [32m✓[39m hash[2m (6)[22m[33m 1367[2mms[22m[39m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [32m✓[39m base 23[33m 1044[2mms[22m[39m
     [32m✓[39m base 17
   [33m❯[39m pHash[2m (2)[22m
     [33m⠙[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [32m✓[39m hash[2m (6)[22m[33m 1367[2mms[22m[39m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [32m✓[39m base 23[33m 1044[2mms[22m[39m
     [32m✓[39m base 17
   [33m❯[39m pHash[2m (2)[22m
     [33m⠹[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [32m✓[39m hash[2m (6)[22m[33m 1367[2mms[22m[39m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [32m✓[39m base 23[33m 1044[2mms[22m[39m
     [32m✓[39m base 17
   [33m❯[39m pHash[2m (2)[22m
     [33m⠸[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [32m✓[39m hash[2m (6)[22m[33m 1367[2mms[22m[39m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [32m✓[39m base 23[33m 1044[2mms[22m[39m
     [32m✓[39m base 17
   [33m❯[39m pHash[2m (2)[22m
     [33m⠼[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [32m✓[39m hash[2m (6)[22m[33m 1367[2mms[22m[39m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [32m✓[39m base 23[33m 1044[2mms[22m[39m
     [32m✓[39m base 17
   [33m❯[39m pHash[2m (2)[22m
     [33m⠴[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [32m✓[39m hash[2m (6)[22m[33m 1367[2mms[22m[39m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [32m✓[39m base 23[33m 1044[2mms[22m[39m
     [32m✓[39m base 17
   [33m❯[39m pHash[2m (2)[22m
     [33m⠦[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [32m✓[39m hash[2m (6)[22m[33m 1367[2mms[22m[39m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [32m✓[39m base 23[33m 1044[2mms[22m[39m
     [32m✓[39m base 17
   [33m❯[39m pHash[2m (2)[22m
     [32m✓[39m should calculate the distance using distanceFromHash
     [33m⠙[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [32m✓[39m hash[2m (6)[22m[33m 1367[2mms[22m[39m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [32m✓[39m base 23[33m 1044[2mms[22m[39m
     [32m✓[39m base 17
   [33m❯[39m pHash[2m (2)[22m
     [32m✓[39m should calculate the distance using distanceFromHash
     [33m⠹[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [32m✓[39m hash[2m (6)[22m[33m 1367[2mms[22m[39m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [32m✓[39m base 23[33m 1044[2mms[22m[39m
     [32m✓[39m base 17
   [33m❯[39m pHash[2m (2)[22m
     [32m✓[39m should calculate the distance using distanceFromHash
     [33m⠸[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [32m✓[39m hash[2m (6)[22m[33m 1367[2mms[22m[39m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [32m✓[39m base 23[33m 1044[2mms[22m[39m
     [32m✓[39m base 17
   [33m❯[39m pHash[2m (2)[22m
     [32m✓[39m should calculate the distance using distanceFromHash
     [33m⠼[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m[33m 1573[2mms[22m[39m
   [32m✓[39m hash[2m (6)[22m[33m 1367[2mms[22m[39m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [32m✓[39m base 23[33m 1044[2mms[22m[39m
     [32m✓[39m base 17
   [32m✓[39m pHash[2m (2)[22m
     [32m✓[39m should calculate the distance using distanceFromHash
     [32m✓[39m should calculate the distance using compareHashes.
[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m[33m 1573[2mms[22m[39m
   [32m✓[39m hash[2m (6)[22m[33m 1367[2mms[22m[39m
     [32m✓[39m base 2
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)
     [32m✓[39m base 64
     [32m✓[39m base 23[33m 1044[2mms[22m[39m
     [32m✓[39m base 17
   [32m✓[39m pHash[2m (2)[22m
     [32m✓[39m should calculate the distance using distanceFromHash
     [32m✓[39m should calculate the distance using compareHashes.

[2m Test Files [22m [1m[32m1 passed[39m[22m[90m (1)[39m
[2m      Tests [22m [1m[32m8 passed[39m[22m[90m (8)[39m
[2m   Start at [22m 00:42:17
[2m   Duration [22m 3.12s[2m (transform 0ms, setup 0ms, collect 124ms, tests 1.57s, environment 0ms, prepare 0ms)[22m

[?25h[?25h
