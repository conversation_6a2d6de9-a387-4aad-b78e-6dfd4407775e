{"version": 3, "file": "measure-text.js", "sourceRoot": "", "sources": ["../../src/measure-text.ts"], "names": [], "mappings": ";;AAEA,kCAoBC;AAED,gCAyDC;AAED,8CAQC;AAzFD,SAAgB,WAAW,CAAC,IAAY,EAAE,IAAY;IACpD,IAAI,CAAC,GAAG,CAAC,CAAC;IAEV,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACrC,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAE,CAAC;QACtB,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAElC,IAAI,QAAQ,EAAE,CAAC;YACb,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAC7B,MAAM,OAAO,GACX,WAAW,IAAI,QAAQ,IAAI,WAAW,CAAC,QAAQ,CAAC;gBAC9C,CAAC,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC;gBAC5B,CAAC,CAAC,CAAC,CAAC;YAER,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,CAAC,CAAC,GAAG,OAAO,CAAC;QAC1C,CAAC;IACH,CAAC;IAED,OAAO,CAAC,CAAC;AACX,CAAC;AAED,SAAgB,UAAU,CAAC,IAAY,EAAE,IAAY,EAAE,QAAgB;IACrE,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAEzD,MAAM,KAAK,GAAe,EAAE,CAAC;IAC7B,IAAI,WAAW,GAAa,EAAE,CAAC;IAC/B,IAAI,WAAW,GAAG,CAAC,CAAC;IAEpB,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;QACrB,MAAM,SAAS,GAAG,WAAW,CAAC,IAAI,EAAE,IAAI,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAE1E,iFAAiF;QACjF,IAAI,SAAS,GAAG,QAAQ,EAAE,CAAC;YACzB,MAAM,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;YAElD,IAAI,OAAO,GAAG,EAAE,CAAC;YAEjB,KAAK,MAAM,IAAI,IAAI,iBAAiB,EAAE,CAAC;gBACrC,MAAM,QAAQ,GAAG,CAAC,GAAG,WAAW,EAAE,OAAO,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAC5D,MAAM,MAAM,GAAG,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;gBAE3C,IAAI,MAAM,GAAG,QAAQ,EAAE,CAAC;oBACtB,OAAO,IAAI,IAAI,CAAC;gBAClB,CAAC;qBAAM,IAAI,MAAM,GAAG,QAAQ,EAAE,CAAC;oBAC7B,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC;oBACtC,WAAW,GAAG,EAAE,CAAC;oBACjB,OAAO,GAAG,IAAI,CAAC;gBACjB,CAAC;qBAAM,CAAC;oBACN,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,WAAW,EAAE,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC;oBAC7C,WAAW,GAAG,EAAE,CAAC;oBACjB,OAAO,GAAG,EAAE,CAAC;gBACf,CAAC;YACH,CAAC;YAED,OAAO;QACT,CAAC;QAED,MAAM,IAAI,GAAG,CAAC,GAAG,WAAW,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC9C,MAAM,MAAM,GAAG,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAEvC,IAAI,MAAM,IAAI,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YAC/C,IAAI,MAAM,GAAG,WAAW,EAAE,CAAC;gBACzB,WAAW,GAAG,MAAM,CAAC;YACvB,CAAC;YAED,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzB,CAAC;aAAM,CAAC;YACN,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACxB,WAAW,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;QACzC,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAExB,OAAO;QACL,KAAK;QACL,WAAW;KACZ,CAAC;AACJ,CAAC;AAED,SAAgB,iBAAiB,CAC/B,IAAY,EACZ,IAAY,EACZ,QAAgB;IAEhB,MAAM,EAAE,KAAK,EAAE,GAAG,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;IAEnD,OAAO,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;AAC/C,CAAC"}