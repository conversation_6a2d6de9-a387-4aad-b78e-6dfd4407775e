name: CI

on:
  push:
    branches:
      - main
      - 'v*'
    paths-ignore:
      - 'docs/**'
      - '*.md'
  pull_request:
    paths-ignore:
      - 'docs/**'
      - '*.md'

# This allows a subsequently queued workflow run to interrupt previous runs
concurrency:
  group: "${{ github.workflow }} @ ${{ github.event.pull_request.head.label || github.head_ref || github.ref }}"
  cancel-in-progress: true

jobs:
  dependency-review:
    name: Dependency Review
    if: github.event_name == 'pull_request'
    runs-on: ubuntu-latest
    permissions:
      contents: read
    steps:
      - name: Check out repo
        uses: actions/checkout@v5
        with:
          persist-credentials: false

      - name: Dependency review
        uses: actions/dependency-review-action@v4

  test:
    name: ${{ matrix.node-version }} ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    permissions:
      contents: read
    strategy:
      fail-fast: false
      matrix:
        os: [macOS-latest, windows-latest, ubuntu-latest]
        node-version: [18, 20, 22]
        exclude:
          - os: windows-latest
            node-version: 22

    steps:
      - name: Check out repo
        uses: actions/checkout@v5
        with:
          persist-credentials: false

      - name: Setup Node ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}

      - name: Install dependencies
        run: npm i --ignore-scripts

      - name: Run tests
        run: npm run test-ci

      - name: Run smoke test
        if: >
          matrix.os != 'windows-latest' &&
          matrix.node-version > 14
        run: npm run test:smoke

  automerge:
    name: Automerge Dependabot PRs
    if: >
        github.event_name == 'pull_request' &&
        github.event.pull_request.user.login == 'dependabot[bot]'
    needs: test
    permissions:
      pull-requests: write
      contents: write
    runs-on: ubuntu-latest
    steps:
      - uses: fastify/github-action-merge-dependabot@v3
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          exclude: 'sonic-boom,pino-std-serializers,quick-format-unescaped,fast-redact'
