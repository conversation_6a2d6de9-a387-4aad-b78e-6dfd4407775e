{"name": "jpeg-js", "version": "0.4.4", "description": "A pure javascript JPEG encoder and decoder", "main": "index.js", "scripts": {"test": "jest --testMatch=**/test/*.js"}, "repository": {"type": "git", "url": "https://github.com/eugeneware/jpeg-js"}, "keywords": ["jpeg", "jpg", "encoder", "decoder", "codec", "image", "javascript", "js"], "author": "<PERSON> <<EMAIL>>", "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/eugeneware/jpeg-js/issues"}, "dependencies": {}, "devDependencies": {"jest": "^25.4.0"}}