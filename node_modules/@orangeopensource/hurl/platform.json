[{"type": "Windows_NT", "architecture": "x64", "rust_target": "x86_64-pc-windows-msvc", "binary_name": "hurl.exe", "archive_extension": ".zip", "checksum": "7ddc8add8e65343422dba4e21c50ae9c6bc9a6833290ed67e0f5fbea85803f32"}, {"type": "Linux", "architecture": "x64", "rust_target": "x86_64-unknown-linux-gnu", "binary_name": "hurl", "archive_extension": ".tar.gz", "checksum": "006a9032401b61004d9e104b06aa0973413f7b36dc5437063b0bf028188c0cf9"}, {"type": "Linux", "architecture": "arm64", "rust_target": "aarch64-unknown-linux-gnu", "binary_name": "hurl", "archive_extension": ".tar.gz", "checksum": "c115e94687921661c788b6fcd54a3e0118314498c8575657032fd0a935cd61bc"}, {"type": "<PERSON>", "architecture": "x64", "rust_target": "x86_64-apple-darwin", "binary_name": "hurl", "archive_extension": ".tar.gz", "checksum": "47421e90f66b4622a3ff835a32cb64cb5187b19f3686d30f9dc99c1df5dfa511"}, {"type": "<PERSON>", "architecture": "arm64", "rust_target": "aarch64-apple-darwin", "binary_name": "hurl", "archive_extension": ".tar.gz", "checksum": "eaef7eb8cc29b6f908fbdc0a1e9b0ac6cc52db71e0e87ab870d5f027f3289d62"}]