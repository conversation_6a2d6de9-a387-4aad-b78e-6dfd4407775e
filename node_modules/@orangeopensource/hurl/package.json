{"name": "@orangeopensource/hurl", "version": "7.0.0", "hurlBinaryVersion": "7.0.0", "description": "Run and Test HTTP Requests with plain text and curl", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "contributors": ["<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>"], "repository": {"type": "git", "url": "git+https://github.com/Orange-OpenSource/hurl.git"}, "homepage": "https://hurl.dev", "license": "Apache-2.0", "bin": {"hurl": "hurl.js", "hurlfmt": "hurlfmt.js"}, "scripts": {"postinstall": "node ./install.js"}, "dependencies": {"extract-zip": "2.0.1", "tar": "7.4.3"}, "devDependencies": {}, "keywords": ["cli", "http", "curl", "integration-testing", "http-client", "testing-tools", "api-testing"], "man": ["./docs/hurl.1", "./docs/hurlfmt.1"]}