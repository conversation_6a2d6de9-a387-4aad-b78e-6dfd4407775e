#compdef hurl

autoload -U is-at-least

_hurl() {
    typeset -A opt_args
    typeset -a _arguments_options
    local ret=1

    if is-at-least 5.2; then
        _arguments_options=(-s -S -C)
    else
        _arguments_options=(-s -C)
    fi

    local context curcontext="$curcontext" state line
    _arguments "${_arguments_options[@]}" \
    '--aws-sigv4[Use AWS V4 signature authentication in the transfer]: :' \
    '--cacert[CA certificate to verify peer against (PEM format)]: :_files' \
    '(-E --cert)'{-E,--cert}'[Client certificate file and password]: :' \
    '--key[Private key file name]: :' \
    '--color[Colorize output]' \
    '--compressed[Request compressed response (using deflate or gzip)]' \
    '--connect-timeout[Maximum time allowed for connection]: :' \
    '*--connect-to[For a request to the given HOST1:PORT1 pair, connect to HOST2:PORT2 instead]: :' \
    '--continue-on-error[Continue executing requests even if an error occurs]' \
    '(-b --cookie)'{-b,--cookie}'[Read cookies from FILE]: :_files' \
    '(-c --cookie-jar)'{-c,--cookie-jar}'[Write cookies to FILE after running the session]: :_files' \
    '--curl[Export each request to a list of curl commands]: :_files' \
    '--delay[Sets delay before each request (aka sleep)]: :' \
    '--error-format[Control the format of error messages]: :' \
    '--file-root[Set root directory to import files \[default: input file directory\]]: :' \
    '(-L --location)'{-L,--location}'[Follow redirects]' \
    '--location-trusted[Follow redirects but allows sending the name + password to all hosts that the site may redirect to]' \
    '--from-entry[Execute Hurl file from ENTRY_NUMBER (starting at 1)]: :' \
    '*--glob[Specify input files that match the given GLOB. Multiple glob flags may be used]: :' \
    '(-H --header)*'{-H,--header}'[Pass custom header(s) to server]: :' \
    '(-0 --http1.0)'{-0,--http1.0}'[Tell Hurl to use HTTP version 1.0]' \
    '--http1.1[Tell Hurl to use HTTP version 1.1]' \
    '--http2[Tell Hurl to use HTTP version 2]' \
    '--http3[Tell Hurl to use HTTP version 3]' \
    '--ignore-asserts[Ignore asserts defined in the Hurl file]' \
    '(-i --include)'{-i,--include}'[Include the HTTP headers in the output]' \
    '(-k --insecure)'{-k,--insecure}'[Allow insecure SSL connections]' \
    '--interactive[Turn on interactive mode]' \
    '(-4 --ipv4)'{-4,--ipv4}'[Tell Hurl to use IPv4 addresses only when resolving host names, and not for example try IPv6]' \
    '(-6 --ipv6)'{-6,--ipv6}'[Tell Hurl to use IPv6 addresses only when resolving host names, and not for example try IPv4]' \
    '--jobs[Maximum number of parallel jobs, 0 to disable parallel execution]: :' \
    '--json[Output each Hurl file result to JSON]' \
    '--limit-rate[Specify the maximum transfer rate in bytes/second, for both downloads and uploads]: :' \
    '--max-filesize[Specify the maximum size in bytes of a file to download]: :' \
    '--max-redirs[Maximum number of redirects allowed, -1 for unlimited redirects]: :' \
    '(-m --max-time)'{-m,--max-time}'[Maximum time allowed for the transfer]: :' \
    '--negotiate[Tell Hurl to use Negotiate (SPNEGO) authentication]' \
    '(-n --netrc)'{-n,--netrc}'[Must read .netrc for username and password]' \
    '--netrc-file[Specify FILE for .netrc]: :_files' \
    '--netrc-optional[Use either .netrc or the URL]' \
    '--no-color[Do not colorize output]' \
    '--no-output[Suppress output. By default, Hurl outputs the body of the last response]' \
    '--noproxy[List of hosts which do not use proxy]: :' \
    '--ntlm[Tell Hurl to use NTLM authentication]' \
    '(-o --output)'{-o,--output}'[Write to FILE instead of stdout]: :_files' \
    '--parallel[Run files in parallel (default in test mode)]' \
    '--path-as-is[Tell Hurl to not handle sequences of /../ or /./ in the given URL path]' \
    '--pinnedpubkey[Public key to verify peer against]: :' \
    '--progress-bar[Display a progress bar in test mode]' \
    '(-x --proxy)'{-x,--proxy}'[Use proxy on given PROTOCOL/HOST/PORT]: :' \
    '--repeat[Repeat the input files sequence NUM times, -1 for infinite loop]: :' \
    '--report-html[Generate HTML report to DIR]: :' \
    '--report-json[Generate JSON report to DIR]: :' \
    '--report-junit[Write a JUnit XML report to FILE]: :_files' \
    '--report-tap[Write a TAP report to FILE]: :_files' \
    '*--resolve[Provide a custom address for a specific HOST and PORT pair]: :' \
    '--retry[Maximum number of retries, 0 for no retries, -1 for unlimited retries]: :' \
    '--retry-interval[Interval in milliseconds before a retry]: :' \
    '*--secret[Define a variable which value is secret]: :' \
    '--ssl-no-revoke[(Windows) Tell Hurl to disable certificate revocation checks]' \
    '--test[Activate test mode (use parallel execution)]' \
    '--to-entry[Execute Hurl file to ENTRY_NUMBER (starting at 1)]: :' \
    '--unix-socket[(HTTP) Connect through this Unix domain socket, instead of using the network]: :_files' \
    '(-u --user)'{-u,--user}'[Add basic Authentication header to each request]: :' \
    '(-A --user-agent)'{-A,--user-agent}'[Specify the User-Agent string to send to the HTTP server]: :' \
    '*--variable[Define a variable]: :' \
    '*--variables-file[Define a properties file in which you define your variables]: :_files' \
    '(-v --verbose)'{-v,--verbose}'[Turn on verbose]' \
    '--very-verbose[Turn on verbose output, including HTTP response and libcurl logs]' \
    '--help[Print help]' \
    '--version[Print version]' \
    '*:file:_files' \
    && ret=0

}

(( $+functions[_hurl_commands] )) ||
_hurl_commands() {
    local commands; commands=()
    _describe -t commands 'hurl commands' commands "$@"
}

if [ "$funcstack[1]" = "_hurl" ]; then
    _hurl "$@"
else
    compdef _hurl hurl
fi
