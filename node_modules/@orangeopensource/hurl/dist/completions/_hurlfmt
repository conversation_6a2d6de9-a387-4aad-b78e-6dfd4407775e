#compdef hurlfmt

autoload -U is-at-least

_hurlfmt() {
    typeset -A opt_args
    typeset -a _arguments_options
    local ret=1

    if is-at-least 5.2; then
        _arguments_options=(-s -S -C)
    else
        _arguments_options=(-s -C)
    fi

    local context curcontext="$curcontext" state line
    _arguments "${_arguments_options[@]}" \
    '--check[Run in check mode]' \
    '--color[Colorize Output]' \
    '--in-place[Modify files in place]' \
    '--in[Specify input format: hurl or curl]: :' \
    '--no-color[Do not colorize output]' \
    '(-o --output)'{-o,--output}'[Write to FILE instead of stdout]: :_files' \
    '--out[Specify output format: hurl, json or html]: :' \
    '--standalone[Standalone HTML]' \
    '--help[Print help]' \
    '--version[Print version]' \
    '*:file:_files' \
    && ret=0

}

(( $+functions[_hurlfmt_commands] )) ||
_hurlfmt_commands() {
    local commands; commands=()
    _describe -t commands 'hurlfmt commands' commands "$@"
}

if [ "$funcstack[1]" = "_hurlfmt" ]; then
    _hurlfmt "$@"
else
    compdef _hurlfmt hurlfmt
fi
