.TH hurl 1 "30 Jul 2025" "hurl 7.0.0" " Hurl Manual"
.SH NAME

hurl - run and test HTTP requests.


.SH SYNOPSIS

.B hurl
[options] [FILE...]


.SH DESCRIPTION

.B Hurl
is a command line tool that runs HTTP requests defined in a simple plain text format.

It can chain requests, capture values and evaluate queries on headers and body response. Hurl is very versatile, it can be used for fetching data and testing HTTP sessions: HTML content, REST / SOAP / GraphQL APIs, or any other XML / JSON based APIs.

    $ hurl session.hurl

If no input files are specified, input is read from stdin.

    $ echo GET http://httpbin.org/get | hurl
        {
          "args": {},
          "headers": {
            "Accept": "*/*",
            "Accept-Encoding": "gzip",
            "Content-Length": "0",
            "Host": "httpbin.org",
            "User-Agent": "hurl/0.99.10",
            "X-Amzn-Trace-Id": "Root=1-5eedf4c7-520814d64e2f9249ea44e0"
          },
          "origin": "*******",
          "url": "http://httpbin.org/get"
        }

Hurl can take files as input, or directories. In the latter case, Hurl will search files with `.hurl` extension recursively.

Output goes to stdout by default. To have output go to a file, use the \fI-o, --output\fP option:

    $ hurl -o output input.hurl

By default, Hurl executes all HTTP requests and outputs the response body of the last HTTP call.

To have a test oriented output, you can use \fI--test\fP option:

    $ hurl --test *.hurl


.SH HURL FILE FORMAT

The Hurl file format is fully documented in \fIhttps://hurl.dev/docs/hurl-file.html\fP

It consists of one or several HTTP requests

    GET http://example.org/endpoint1
    GET http://example.org/endpoint2


.IP "Capturing values"

A value from an HTTP response can be-reused for successive HTTP requests.

A typical example occurs with CSRF tokens.

    GET https://example.org
    HTTP 200
    # Capture the CSRF token value from html body.
    [Captures]
    csrf_token: xpath "normalize-space(//meta[@name='_csrf_token']/@content)"

    # Do the login !
    POST https://example.org/login?user=toto&password=1234
    X-CSRF-TOKEN: {{csrf_token}}

More information on captures can be found here \fIhttps://hurl.dev/docs/capturing-response.html\fP

.IP "Asserts"

The HTTP response defined in the Hurl file are used to make asserts. Responses are optional.

At the minimum, response includes assert on the HTTP status code.

    GET http://example.org
    HTTP 301

It can also include asserts on the response headers

    GET http://example.org
    HTTP 301
    Location: http://www.example.org

Explicit asserts can be included by combining a query and a predicate

    GET http://example.org
    HTTP 301
    [Asserts]
    xpath "string(//title)" == "301 Moved"

With the addition of asserts, Hurl can be used as a testing tool to run scenarios.

More information on asserts can be found here \fIhttps://hurl.dev/docs/asserting-response.html\fP

.SH OPTIONS

Options that exist in curl have exactly the same semantics.

Options specified on the command line are defined for every Hurl file's entry,
except if they are tagged as cli-only (can not be defined in the Hurl request [Options] entry)

For instance:

    $ hurl --location foo.hurl

will follow redirection for each entry in `foo.hurl`. You can also define an option only for a particular entry with an `[Options]` section. For instance, this Hurl file:

    GET https://example.org
    HTTP 301

    GET https://example.org
    [Options]
    location: true
    HTTP 200

will follow a redirection only for the second entry.

.IP "--aws-sigv4 <PROVIDER1[:PROVIDER2[:REGION[:SERVICE]]]> "

Generate an `Authorization` header with an AWS SigV4 signature.

Use \fI-u, --user\fP to specify Access Key Id (username) and Secret Key (password).

To use temporary session credentials (e.g. for an AWS IAM Role), add the `X-Amz-Security-Token` header containing the session token.

.IP "--cacert <FILE> "

Specifies the certificate file for peer verification. The file may contain multiple CA certificates and must be in PEM format.
Normally Hurl is built to use a default file for this, so this option is typically used to alter that default file.

.IP "-E, --cert <CERTIFICATE[:PASSWORD]> "

Client certificate file and password.

See also \fI--key\fP.

.IP "--color "

Colorize debug output (the HTTP response output is not colorized).

This is a cli-only option.

.IP "--compressed "

Request a compressed response using one of the algorithms br, gzip, deflate and automatically decompress the content.

.IP "--connect-timeout <SECONDS> "

Maximum time in seconds that you allow Hurl's connection to take.

You can specify time units in the connect timeout expression. Set Hurl to use a connect timeout of 20 seconds with `--connect-timeout 20s` or set it to 35,000 milliseconds with `--connect-timeout 35000ms`. No spaces allowed.

See also \fI-m, --max-time\fP.

.IP "--connect-to <HOST1:PORT1:HOST2:PORT2> "

For a request to the given HOST1:PORT1 pair, connect to HOST2:PORT2 instead. This option can be used several times in a command line.

See also \fI--resolve\fP.

.IP "--continue-on-error "

Continue executing requests to the end of the Hurl file even when an assert error occurs.
By default, Hurl exits after an assert error in the HTTP response.

Note that this option does not affect the behavior with multiple input Hurl files.

All the input files are executed independently. The result of one file does not affect the execution of the other Hurl files.

This is a cli-only option.

.IP "-b, --cookie <FILE> "

Read cookies from FILE (using the Netscape cookie file format).

Combined with \fI-c, --cookie-jar\fP, you can simulate a cookie storage between successive Hurl runs.

This is a cli-only option.

.IP "-c, --cookie-jar <FILE> "

Write cookies to FILE after running the session.
The file will be written using the Netscape cookie file format.

Combined with \fI-b, --cookie\fP, you can simulate a cookie storage between successive Hurl runs.

This is a cli-only option.

.IP "--curl <FILE> "

Export each request to a list of curl commands.

This is a cli-only option.

.IP "--delay <MILLISECONDS> "

Sets delay before each request (aka sleep). The delay is not applied to requests that have been retried because of \fI--retry\fP. See \fI--retry-interval\fP to space retried requests.

You can specify time units in the delay expression. Set Hurl to use a delay of 2 seconds with `--delay 2s` or set it to 500 milliseconds with `--delay 500ms`. No spaces allowed.

.IP "--error-format <FORMAT> "

Control the format of error message (short by default or long)

This is a cli-only option.

.IP "--file-root <DIR> "

Set root directory to import files in Hurl. This is used for files in multipart form data, request body and response output.
When it is not explicitly defined, files are relative to the Hurl file's directory.

This is a cli-only option.

.IP "--from-entry <ENTRY_NUMBER> "

Execute Hurl file from ENTRY_NUMBER (starting at 1).

This is a cli-only option.

.IP "--glob <GLOB> "

Specify input files that match the given glob pattern.

Multiple glob flags may be used. This flag supports common Unix glob patterns like *, ? and [].
However, to avoid your shell accidentally expanding glob patterns before Hurl handles them, you must use single quotes or double quotes around each pattern.

This is a cli-only option.

.IP "-H, --header <HEADER> "

Add an extra header to include in information sent. Can be used several times in a command

Do not add newlines or carriage returns

.IP "-0, --http1.0 "

Tells Hurl to use HTTP version 1.0 instead of using its internally preferred HTTP version.

.IP "--http1.1 "

Tells Hurl to use HTTP version 1.1.

.IP "--http2 "

Tells Hurl to use HTTP version 2.
For HTTPS, this means Hurl negotiates HTTP/2 in the TLS handshake. Hurl does this by default.
For HTTP, this means Hurl attempts to upgrade the request to HTTP/2 using the Upgrade: request header.

.IP "--http3 "

Tells Hurl to try HTTP/3 to the host in the URL, but fallback to earlier HTTP versions if the HTTP/3 connection establishment fails. HTTP/3 is only available for HTTPS and not for HTTP URLs.

.IP "--ignore-asserts "

Ignore all asserts defined in the Hurl file.

This is a cli-only option.

.IP "-i, --include "

Include the HTTP headers in the output

This is a cli-only option.

.IP "-k, --insecure "

This option explicitly allows Hurl to perform "insecure" SSL connections and transfers.

.IP "-4, --ipv4 "

This option tells Hurl to use IPv4 addresses only when resolving host names, and not for example try IPv6.

.IP "-6, --ipv6 "

This option tells Hurl to use IPv6 addresses only when resolving host names, and not for example try IPv4.

.IP "--jobs <NUM> "

Maximum number of parallel jobs in parallel mode. Default value corresponds (in most cases) to the
current amount of CPUs.

See also \fI--parallel\fP.

This is a cli-only option.

.IP "--json "

Output each Hurl file result to JSON. The format is very closed to HAR format.

This is a cli-only option.

.IP "--key <KEY> "

Private key file name.

.IP "--limit-rate <SPEED> "

Specify the maximum transfer rate you want Hurl to use, for both downloads and uploads. This feature is useful if you have a limited pipe and you would like your transfer not to use your entire bandwidth. To make it slower than it otherwise would be.
The given speed is measured in bytes/second.

.IP "-L, --location "

Follow redirect. To limit the amount of redirects to follow use the \fI--max-redirs\fP option

.IP "--location-trusted "

Like \fI-L, --location\fP, but allows sending the name + password to all hosts that the site may redirect to.
This may or may not introduce a security breach if the site redirects you to a site to which you send your authentication info (which is plaintext in the case of HTTP Basic authentication).

.IP "--max-filesize <BYTES> "

Specify the maximum size in bytes of a file to download. If the file requested is larger than this value, the transfer does not start.

This is a cli-only option.

.IP "--max-redirs <NUM> "

Set maximum number of redirection-followings allowed

By default, the limit is set to 50 redirections. Set this option to -1 to make it unlimited.

.IP "-m, --max-time <SECONDS> "

Maximum time in seconds that you allow a request/response to take. This is the standard timeout.

You can specify time units in the maximum time expression. Set Hurl to use a maximum time of 20 seconds with `--max-time 20s` or set it to 35,000 milliseconds with `--max-time 35000ms`. No spaces allowed.

See also \fI--connect-timeout\fP.

.IP "--negotiate "

Tell Hurl to use Negotiate (SPNEGO) authentication.

This is a cli-only option.

.IP "-n, --netrc "

Scan the .netrc file in the user's home directory for the username and password.

See also \fI--netrc-file\fP and \fI--netrc-optional\fP.

.IP "--netrc-file <FILE> "

Like \fI--netrc\fP, but provide the path to the netrc file.

See also \fI--netrc-optional\fP.

.IP "--netrc-optional "

Similar to \fI--netrc\fP, but make the .netrc usage optional.

See also \fI--netrc-file\fP.

.IP "--no-color "

Do not colorize output.

This is a cli-only option.

.IP "--no-output "

Suppress output. By default, Hurl outputs the body of the last response.

This is a cli-only option.

.IP "--noproxy <HOST(S)> "

Comma-separated list of hosts which do not use a proxy.

Override value from Environment variable no_proxy.

.IP "--ntlm "

Tell Hurl to use NTLM authentication

This is a cli-only option.

.IP "-o, --output <FILE> "

Write output to FILE instead of stdout. Use '-' for stdout in [Options] sections.

.IP "--parallel "

Run files in parallel.

Each Hurl file is executed in its own worker thread, without sharing anything with the other workers. The default run mode is sequential. Parallel execution is by default in \fI--test\fP mode.

See also \fI--jobs\fP.

This is a cli-only option.

.IP "--path-as-is "

Tell Hurl to not handle sequences of /../ or /./ in the given URL path. Normally Hurl will squash or merge them according to standards but with this option set you tell it not to do that.

.IP "--pinnedpubkey <HASHES> "

When negotiating a TLS or SSL connection, the server sends a certificate indicating its identity. A public key is extracted from this certificate and if it does not exactly match the public key provided to this option, Hurl aborts the connection before sending or receiving any data.

.IP "--progress-bar "

Display a progress bar in test mode. The progress bar is displayed only in interactive TTYs. This option forces the progress bar to be displayed even in non-interactive TTYs.

This is a cli-only option.

.IP "-x, --proxy <[PROTOCOL://]HOST[:PORT]> "

Use the specified proxy.

.IP "--repeat <NUM> "

Repeat the input files sequence NUM times, -1 for infinite loop. Given a.hurl, b.hurl, c.hurl as input, repeat two
times will run a.hurl, b.hurl, c.hurl, a.hurl, b.hurl, c.hurl.

.IP "--report-html <DIR> "

Generate HTML report in DIR.

If the HTML report already exists, it will be updated with the new test results.

This is a cli-only option.

.IP "--report-json <DIR> "

Generate JSON report in DIR.

If the JSON report already exists, it will be updated with the new test results.

This is a cli-only option.

.IP "--report-junit <FILE> "

Generate JUnit File.

If the FILE report already exists, it will be updated with the new test results.

This is a cli-only option.

.IP "--report-tap <FILE> "

Generate TAP report.

If the FILE report already exists, it will be updated with the new test results.

This is a cli-only option.

.IP "--resolve <HOST:PORT:ADDR> "

Provide a custom address for a specific host and port pair. Using this, you can make the Hurl requests(s) use a specified address and prevent the otherwise normally resolved address to be used. Consider it a sort of /etc/hosts alternative provided on the command line.

.IP "--retry <NUM> "

Maximum number of retries, 0 for no retries, -1 for unlimited retries. Retry happens if any error occurs (asserts, captures, runtimes etc...).

.IP "--retry-interval <MILLISECONDS> "

Duration in milliseconds between each retry. Default is 1000 ms.

You can specify time units in the retry interval expression. Set Hurl to use a retry interval of 2 seconds with `--retry-interval 2s` or set it to 500 milliseconds with `--retry-interval 500ms`. No spaces allowed.

.IP "--secret <NAME=VALUE> "

Define secret value to be redacted from logs and report. When defined, secrets can be used as variable everywhere variables are used.

This is a cli-only option.

.IP "--ssl-no-revoke "

(Windows) This option tells Hurl to disable certificate revocation checks. WARNING: this option loosens the SSL security, and by using this flag you ask for exactly that.

This is a cli-only option.

.IP "--test "

Activate test mode: with this, the HTTP response is not outputted anymore, progress is reported for each Hurl file tested, and a text summary is displayed when all files have been run.

In test mode, files are executed in parallel. To run test in a sequential way use `--job 1`.

See also \fI--jobs\fP.

This is a cli-only option.

.IP "--to-entry <ENTRY_NUMBER> "

Execute Hurl file to ENTRY_NUMBER (starting at 1).
Ignore the remaining of the file. It is useful for debugging a session.

This is a cli-only option.

.IP "--unix-socket <PATH> "

(HTTP) Connect through this Unix domain socket, instead of using the network.

.IP "-u, --user <USER:PASSWORD> "

Add basic Authentication header to each request.

.IP "-A, --user-agent <NAME> "

Specify the User-Agent string to send to the HTTP server.

This is a cli-only option.

.IP "--variable <NAME=VALUE> "

Define variable (name/value) to be used in Hurl templates.

.IP "--variables-file <FILE> "

Set properties file in which your define your variables.

Each variable is defined as name=value exactly as with \fI--variable\fP option.

Note that defining a variable twice produces an error.

This is a cli-only option.

.IP "-v, --verbose "

Turn on verbose output on standard error stream.
Useful for debugging.

A line starting with '>' means data sent by Hurl.
A line staring with '<' means data received by Hurl.
A line starting with '*' means additional info provided by Hurl.

If you only want HTTP headers in the output, \fI-i, --include\fP might be the option you're looking for.

.IP "--very-verbose "

Turn on more verbose output on standard error stream.

In contrast to  \fI--verbose\fP option, this option outputs the full HTTP body request and response on standard error. In addition, lines starting with '**' are libcurl debug logs.

.IP "-h, --help "

Usage help. This lists all current command line options with a short description.

.IP "-V, --version "

Prints version information

.SH ENVIRONMENT

Environment variables can only be specified in lowercase.

Using an environment variable to set the proxy has the same effect as using the \fI-x, --proxy\fP option.

.IP "http_proxy [PROTOCOL://]<HOST>[:PORT]"

Sets the proxy server to use for HTTP.

.IP "https_proxy [PROTOCOL://]<HOST>[:PORT]"

Sets the proxy server to use for HTTPS.

.IP "all_proxy [PROTOCOL://]<HOST>[:PORT]"

Sets the proxy server to use if no protocol-specific proxy is set.

.IP "no_proxy <comma-separated list of hosts>"

List of host names that shouldn't go through any proxy.

.IP "HURL_name value"

Define variable (name/value) to be used in Hurl templates. This is similar than \fI--variable\fP and \fI--variables-file\fP options.

.IP "NO_COLOR"

When set to a non-empty string, do not colorize output (see \fI--no-color\fP option).

.SH EXIT CODES

.IP "0"

Success.

.IP "1"

Failed to parse command-line options.

.IP "2"

Input File Parsing Error.

.IP "3"

Runtime error (such as failure to connect to host).

.IP "4"

Assert Error.

.SH WWW

\fIhttps://hurl.dev\fP


.SH SEE ALSO

curl(1)  hurlfmt(1)


