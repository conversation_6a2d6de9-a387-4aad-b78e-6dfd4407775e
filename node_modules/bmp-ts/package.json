{"name": "bmp-ts", "version": "1.0.9", "description": "A pure typescript BMP encoder and decoder", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/hipstersmoothie/bmp-ts"}, "files": ["dist", "types"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "scripts": {"build": "tshy", "test": "vitest", "release": "auto shipit"}, "devDependencies": {"@types/node": "^10.11.7", "auto": "^11.1.2", "prettier": "^3.2.5", "tshy": "^1.12.0", "typescript": "^5.4.3", "vitest": "^1.4.0"}, "prettier": {"singleQuote": true}, "keywords": ["bmp", "1bit", "4bit", "8bit", "16bit", "24bit", "32bit", "encoder", "decoder", "image", "javascript", "js"], "tshy": {"exclude": ["**/__test__/**"], "exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "type": "module"}