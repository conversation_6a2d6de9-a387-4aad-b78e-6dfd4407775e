{"name": "gifwrap", "version": "0.10.1", "description": "A Jimp-compatible library for working with GIFs", "main": "src/index.js", "scripts": {"test": "mocha --timeout=6000 ./test/*.js", "docs": "jsdoc2md --plugin dmd-clear --template templates/README.hbs src/index.js src/gif.js src/bitmapimage.js src/gifframe.js src/gifutil.js src/gifcodec.js > README.md"}, "repository": {"type": "git", "url": "git+https://github.com/jtlapp/gifwrap.git"}, "keywords": ["image", "image", "processing", "image", "manipulation", "gif", "javascript"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/jtlapp/gifwrap/issues"}, "homepage": "https://github.com/jtlapp/gifwrap#readme", "dependencies": {"image-q": "^4.0.0", "omggif": "^1.0.10"}, "devDependencies": {"chai": "^4.2.0", "dmd-clear": "^0.1.2", "jimp": "^0.2.28", "mocha": "^9.2.2"}}