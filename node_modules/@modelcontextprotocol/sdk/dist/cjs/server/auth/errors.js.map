{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../../../../src/server/auth/errors.ts"], "names": [], "mappings": ";;;AAEA;;GAEG;AACH,MAAa,UAAW,SAAQ,KAAK;IAGnC,YACE,OAAe,EACC,QAAiB;QAEjC,KAAK,CAAC,OAAO,CAAC,CAAC;QAFC,aAAQ,GAAR,QAAQ,CAAS;QAGjC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,MAAM,QAAQ,GAAuB;YACnC,KAAK,EAAE,IAAI,CAAC,SAAS;YACrB,iBAAiB,EAAE,IAAI,CAAC,OAAO;SAChC,CAAC;QAEF,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC;QACrC,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,IAAI,SAAS;QACX,OAAQ,IAAI,CAAC,WAAiC,CAAC,SAAS,CAAA;IAC1D,CAAC;CACF;AA9BD,gCA8BC;AAED;;;;GAIG;AACH,MAAa,mBAAoB,SAAQ,UAAU;;AAAnD,kDAEC;AADQ,6BAAS,GAAG,iBAAiB,CAAC;AAGvC;;;GAGG;AACH,MAAa,kBAAmB,SAAQ,UAAU;;AAAlD,gDAEC;AADQ,4BAAS,GAAG,gBAAgB,CAAC;AAGtC;;;;GAIG;AACH,MAAa,iBAAkB,SAAQ,UAAU;;AAAjD,8CAEC;AADQ,2BAAS,GAAG,eAAe,CAAC;AAGrC;;;GAGG;AACH,MAAa,uBAAwB,SAAQ,UAAU;;AAAvD,0DAEC;AADQ,iCAAS,GAAG,qBAAqB,CAAC;AAG3C;;;GAGG;AACH,MAAa,yBAA0B,SAAQ,UAAU;;AAAzD,8DAEC;AADQ,mCAAS,GAAG,wBAAwB,CAAC;AAG9C;;;GAGG;AACH,MAAa,iBAAkB,SAAQ,UAAU;;AAAjD,8CAEC;AADQ,2BAAS,GAAG,eAAe,CAAC;AAGrC;;GAEG;AACH,MAAa,iBAAkB,SAAQ,UAAU;;AAAjD,8CAEC;AADQ,2BAAS,GAAG,eAAe,CAAC;AAGrC;;;GAGG;AACH,MAAa,WAAY,SAAQ,UAAU;;AAA3C,kCAEC;AADQ,qBAAS,GAAG,cAAc,CAAC;AAGpC;;;GAGG;AACH,MAAa,2BAA4B,SAAQ,UAAU;;AAA3D,kEAEC;AADQ,qCAAS,GAAG,yBAAyB,CAAC;AAG/C;;;GAGG;AACH,MAAa,4BAA6B,SAAQ,UAAU;;AAA5D,oEAEC;AADQ,sCAAS,GAAG,2BAA2B,CAAC;AAGjD;;;GAGG;AACH,MAAa,yBAA0B,SAAQ,UAAU;;AAAzD,8DAEC;AADQ,mCAAS,GAAG,wBAAwB,CAAC;AAG9C;;;GAGG;AACH,MAAa,iBAAkB,SAAQ,UAAU;;AAAjD,8CAEC;AADQ,2BAAS,GAAG,eAAe,CAAC;AAGrC;;;GAGG;AACH,MAAa,qBAAsB,SAAQ,UAAU;;AAArD,sDAEC;AADQ,+BAAS,GAAG,oBAAoB,CAAC;AAG1C;;;GAGG;AACH,MAAa,oBAAqB,SAAQ,UAAU;;AAApD,oDAEC;AADQ,8BAAS,GAAG,mBAAmB,CAAC;AAGzC;;;GAGG;AACH,MAAa,0BAA2B,SAAQ,UAAU;;AAA1D,gEAEC;AADQ,oCAAS,GAAG,yBAAyB,CAAC;AAG/C;;GAEG;AACH,MAAa,sBAAuB,SAAQ,UAAU;;AAAtD,wDAEC;AADQ,gCAAS,GAAG,oBAAoB,CAAC;AAG1C;;GAEG;AACH,MAAa,gBAAiB,SAAQ,UAAU;IAC9C,YAA6B,eAAuB,EAAE,OAAe,EAAE,QAAiB;QACtF,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QADE,oBAAe,GAAf,eAAe,CAAQ;IAEpD,CAAC;IAED,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;CACF;AARD,4CAQC;AAED;;GAEG;AACU,QAAA,YAAY,GAAG;IAC1B,CAAC,mBAAmB,CAAC,SAAS,CAAC,EAAE,mBAAmB;IACpD,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE,kBAAkB;IAClD,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAAE,iBAAiB;IAChD,CAAC,uBAAuB,CAAC,SAAS,CAAC,EAAE,uBAAuB;IAC5D,CAAC,yBAAyB,CAAC,SAAS,CAAC,EAAE,yBAAyB;IAChE,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAAE,iBAAiB;IAChD,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAAE,iBAAiB;IAChD,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE,WAAW;IACpC,CAAC,2BAA2B,CAAC,SAAS,CAAC,EAAE,2BAA2B;IACpE,CAAC,4BAA4B,CAAC,SAAS,CAAC,EAAE,4BAA4B;IACtE,CAAC,yBAAyB,CAAC,SAAS,CAAC,EAAE,yBAAyB;IAChE,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAAE,iBAAiB;IAChD,CAAC,qBAAqB,CAAC,SAAS,CAAC,EAAE,qBAAqB;IACxD,CAAC,oBAAoB,CAAC,SAAS,CAAC,EAAE,oBAAoB;IACtD,CAAC,0BAA0B,CAAC,SAAS,CAAC,EAAE,0BAA0B;IAClE,CAAC,sBAAsB,CAAC,SAAS,CAAC,EAAE,sBAAsB;CAClD,CAAC"}